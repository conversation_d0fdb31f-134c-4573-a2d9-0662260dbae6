// 行政端用户对象
module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const CryptoJS = require('crypto-js');

  require('./adminGroup');

  const SuperUserSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    type: { // 单位类型：1 卫生健康委 2 监督所 3 疾控中心 4 职防院
      type: Number,
      require: true,
      enum: [ 1, 2, 3, 4 ],
      set(val) {
        return Number(val);
      },
    },
    cname: String, // 单位名称
    regAdd: [ String ], // 单位的管辖区域范围, 目前存的是中文名
    area_code: { // 所在辖区区号 12位
      type: String,
      require: true,
      validate(val) { return val.length === 12; },
    },
    userName: String, // 账户名
    name: String, // 超级管理员姓名
    jobTitle: String, // 超级管理员职位
    email: String, // 邮箱
    phoneNum: String, // 超级管理员手机号码
    countryCode: { // 手机号前国家代码
      type: String,
      default: '86',
    },
    password: { // 超级管理员密码
      type: String,
      set(val) {
        return CryptoJS.SHA256(val + app.config.salt_sha2_key).toString();
      },
    },
    comments: String, // 备注
    landline: String, // 座机
    date: { // 渐渐弃用
      type: Date,
      default: Date.now,
    },
    logo: {
      type: String,
      default: '/static/upload/images/defaultlogo.png',
    },
    enable: { // 账号是否受限
      type: Boolean,
      default: false,
    },
    state: { // 账号状态
      type: String,
      default: '1', // 1正常，0删除
    },
    group: { // 资源所属组ID
      type: String,
      ref: 'AdminGroup',
      default: app.config.groupID.superGroupID,
    },
    members: [{ // 本账号的其他管理成员, 也是可以登录的
      _id: {
        type: String,
        default: shortid.generate,
      },
      name: String, // 姓名
      phoneNum: String,
      userName: String, // 默认是手机号
      jobTitle: String, // 职位
      password: {
        type: String,
        set(val) {
          return CryptoJS.SHA256(val + app.config.salt_sha2_key).toString();
        },
      },
    }],
    powerStatus: { type: Boolean, default: false }, // 是否已开启菜单权限配置
    power: [{ type: String, ref: 'AdminResource' }], // 菜单权限
  }, { timestamps: true });


  return mongoose.model('SuperUser', SuperUserSchema, 'superusers');

};
