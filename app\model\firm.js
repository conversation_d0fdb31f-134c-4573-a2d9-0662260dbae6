/**
 * Created by Administrator on 2015/4/15.
 * 管理员用户组对象
 */


module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  require('./adminResource');

  const FirmSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    pwrq: Date, // 赋码日期
    jjhy: String, //	经济行业
    jyfw: String, //	经营范围
    zycp1: String, //	主要产品编码1
    jglx: String, //	机构类型
    yzbm: String, //	邮政编码
    njqx: Date, //	质监年检期限
    hbzl: String, //	货币种类
    jjlx: String, //	经济类型
    pzwh: String, //	质监批准文号
    xzqh: String, //	行政区划
    jgmc: String, //	机构名称
    zycp3: String, // 主要产品编码3
    zycp2: String, // 主要产品编码2
    lastdate: Date, //		维护日期
    jgxydm: { type: String }, //	统一社会信用代码
    wftzgb: String, //	外方国别
    jgdm: { type: String }, // 组织机构代码
    zgrs: Date, //	职工人数
    zfrq: Date, //		质监作废日期
    bzjgdm: String, // 发证单位
    njrq: Date, //		质监年检日期
    jgdz: String, // 机构地址
    bzrq: Date, //		发证日期
    zgdm: String, // 主管部门
    zch: String, // 营业执照注册号
    fddbr: String, // 法定代表人（负责人）

  });


  return mongoose.model('Firm', FirmSchema, 'Firms');

};
