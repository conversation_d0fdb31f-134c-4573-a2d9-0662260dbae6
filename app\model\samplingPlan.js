
module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const ctx = app.createAnonymousContext();
  const permissionPlugin = require('../utils/permission.js');
  const SamplingPlansSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    samplingSchemesID: { // 对应方案id
      type: String,
      ref: 'SamplingSchemes',
      default: '',
    },
    EnterpriseID: { type: String }, // 企业id
    adminOrgName: { type: String }, // 企业名称
    serviceID: { type: String }, // 机构id
    jobHealthId: { type: String }, // 检测项目id
    projectName: { type: String }, // 检测项目名称
    projectNumber: { type: String }, // 检测项目编号
    serviceType: { type: String }, // 检测类别
    workDayWeek: Number, // 每周工作天数(天)
    time: Number, // 每班接触时间
    sampleDate: { type: Date }, // 采样日期
    workShopName: String, // 厂房
    workspacesName: String, // 车间
    stationsName: String, // 岗位
    workShopId: String, // 厂房Id
    workspacesId: String, // 车间Id
    stationsId: String, // 岗位Id
    pointNum: Number, // 测点编号
    workTypeName: String, // 工种
    category: String, // 所属分类
    harmFactorsName: String, // 检测项目
    count: String, // 点数
    sampleNumber: String, // 样品数
    dayNumber: String, // 天数
    sampleMode: String, // 采样方式
    sampleNum: { type: Object }, // 样品编号
    sampleTime: String, // 采样时段
    sampleFlow: String, // 采样流量
    airCollector: String, // 空气收集器
    sampleEquipment: String, // 采样设备
    sampleStorage: String, // 样品保存期限和保存条件
    remark: String, // 备注
    dustOriginName: [{
      workShopName: String, // 厂房
      workspacesName: String, // 车间
      stationsName: String, // 岗位
      harmFactorsName: String, // 检测项目
    }], // 关联到这个游离sio2的粉尘的数据
    batch: String, // 批次 wzq+
    spotRecordSort: String, // 类型
  });

  SamplingPlansSchema.plugin(permissionPlugin, {
    ctx,
    fieldMapping: {
      enterpriseId: 'EnterpriseID',
    },
  });

  return mongoose.model('SamplingPlans', SamplingPlansSchema, 'samplingPlans');

};
