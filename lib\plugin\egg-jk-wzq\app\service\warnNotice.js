const Service = require('egg').Service;

class WarnNoticeService extends Service {
  async uploadCard(params) {
    // 往名为NotificationCard的表中存入数据
    const { ctx } = this;
    const data = {
      harmFactor: params.harmFactor,
      fileName: params.fileName,
      filePath: params.filePath,
      fileType: params.fileType,
      createTime: params.time,
      updateTime: new Date(),
    };
    // return await new ctx.model.NotificationCard(data).save();
    return await ctx.service.db.create('NotificationCard', data);
    // return data;
  }

  async index(EnterpriseID) {
    // console.log(EnterpriseID, '企业ID');
    const ctx = this.ctx;
    const res = await ctx.service.db.findOne('WarnNotice', { EnterpriseID });
    if (!res) return;
    // 查询NotificationCard表中的数据
    const notificationCard = await ctx.service.db.find('NotificationCard', {});
    // 将查询到的数据添加到res里
    res.notificationCard = notificationCard;
    return res;
    // return await ctx.model.WarnNotice.findOne({ EnterpriseID });
  }
  async index2(params) {
    const ctx = this.ctx;
    const total = await ctx.service.db.find('WarnNotice', { EnterpriseID: params.id });
    const pipeline = [
      {
        $match: {
          EnterpriseID: params.id,
        },
      },
      {
        $unwind: '$tableData',
      },
      // {
      //   $skip: (Number(params.current) - 1) * Number(params.pageSize),
      // },
      // {
      //   $limit: Number(params.pageSize),
      // },
    ];
    const res = await ctx.service.db.aggregate('WarnNotice', pipeline);

    return { res, total: total[0] ? total[0].tableData.length : 0 };
  }
  async find(EnterpriseID) {
    return await this.ctx.service.db.findOne('WarnNotice', { EnterpriseID });
  }
  async create(data, EnterpriseID) {
    const ctx = this.ctx;
    try {
      // 直接使用mongoose模型，避免事务问题
      const res = await ctx.model.WarnNotice.updateOne(
        { EnterpriseID },
        { $push: { tableData: [data] } }
      );
      return res;
    } catch (err) {
      throw err;
    }
  }
  async update(data, EnterpriseID) {
    const ctx = this.ctx;
    try {
      // 直接使用mongoose模型，避免事务问题
      let res = await ctx.model.WarnNotice.updateOne(
        { EnterpriseID, 'tableData._id': data._id },
        { $set: { 'tableData.$': data } },
        { new: true }
      );
      return res;
    } catch (err) {
      throw err;
    }
  }
  async createDoc(EnterpriseID) {
    const { ctx } = this;
    const data = {
      EnterpriseID,
      createTime: new Date(),
      tableData: [],
      total: 0,
    };
    // 直接使用mongoose模型，避免事务问题
    return await new ctx.model.WarnNotice(data).save();
  }

  // 取下功能 - 只改变putUpStatus状态，使用_id查询
  async delete(EnterpriseID, ids) {
    const ctx = this.ctx;
    console.log('取下操作 - 接收到的IDs:', ids);

    const results = [];

    for (const id of ids) {
      console.log('处理取下操作，_id:', id);

      // 直接使用mongoose模型，避免事务问题
      const result = await ctx.model.WarnNotice.updateOne(
        {
          EnterpriseID,
          'tableData._id': id
        },
        {
          $set: {
            'tableData.$.putUpStatus': false
          }
        }
      );
      results.push(result);
    }

    return results;
  }
  async autoBuildWarnSign(params) {
    const { ctx } = this;
    const pipeline = [
      {
        $match: {
          EnterpriseID: params.id,
          $or: [
            { category: 'mill' },
            { category: 'workspaces' },
          ],
        },
      },
      {
        $unwind: '$children',
      },
      {
        $addFields: {
          rootElement: {
            $cond: {
              if: { $eq: [ '$category', 'mill' ] },
              then: {
                $mergeObjects: [
                  '$children',
                  { millID: '$_id' },
                ],
              },
              else: '$$ROOT',
            },
          },
        },
      },
      {
        $replaceRoot: { newRoot: '$rootElement' },
      },
      // {
      //   $group:
      //     {
      //       _id: {
      //         _id: '$_id',
      //         name: '$name',
      //         EnterpriseID: '$EnterpriseID',
      //         millID: '$millID',
      //       },
      //       children: {
      //         $push: '$$ROOT',
      //       },
      //     },
      // },
      // {
      //   $addFields:
      //     {
      //       children: {
      //         $concatArrays: [
      //           [
      //             {
      //               configPlace: '车间入口处',
      //             },
      //           ],
      //           '$children',
      //         ],
      //       },
      //     },
      // },
      {
        $unwind: {
          path: '$children',
        },
      },
      {
        $unwind: '$children',
      },
      {
        $unwind: '$children',
      },
      // {
      //   $replaceRoot:
      //     {
      //       newRoot: '$children',
      //     },
      // },
      // { $facet: {
      //   docs: [{ $skip: (Number(params.current) - 1) * Number(params.pageSize) }, { $limit: Number(params.pageSize) }],
      //   total: [{ $count: 'total' }],
      // } },
    ];

    const docs = await ctx.service.db.aggregate('MillConstruction', pipeline) || [];
    const alternativeField = [ '化学', '粉尘', '生物' ];
    const firstChoiceField = [ '噪声', '高温', '紫外线', '金属烟', '井下不良作业条件' ];
    const alternative = {
      化学: [ '当心中毒', '当心有毒气体', '戴防护镜', '戴防毒面具', '戴防护手套', '穿防护服', '穿防护鞋', '注意通风' ],
      粉尘: [ '注意防尘', '戴防尘口罩', '注意通风' ],
      生物: [ '当心感染' ],
    };
    const firstChoice = {
      噪声: [ '噪声有害', '戴护耳器' ],
      高温: [ '注意高温', '注意通风' ],
      紫外线: [ '当心弧光', '当心电离辐射', '穿防护服', '戴防护镜' ],
      金属烟: [ '注意防尘', '戴防尘口罩', '注意通风' ],
      井下不良作业条件: [ '戴防护镜', '戴防毒面具', '戴防护手套', '穿防护服', '穿防护鞋', '注意通风' ],
    };
    const stations = [];
    // 生成数据
    function buildDate(param, millID = null) {
      let warnContent = '';
      let signNumber = 0;
      const harmFactors = [];
      // 处理危害因素
      const handledData = [];
      param.children.harmFactors.forEach(item => {
        harmFactors.push(item[1]);
        if (firstChoiceField.includes(item[1]) && !handledData.includes(item[1])) {
          warnContent += firstChoice[item[1]] + ',';
          signNumber += firstChoice[item[1]].length;
          handledData.push(item[1]);
        } else if (alternativeField.includes(item[0]) && !handledData.includes(item[0])) {
          warnContent += alternative[item[0]] + ',';
          signNumber += alternative[item[0]].length;
          handledData.push(item[0]);
        }
      });
      // 处理自定义危害因素
      const customizeHarm = param.children.customizeHarm ? param.children.customizeHarm.split(/,|，|-|_|\s|、/gi) : [];
      customizeHarm.forEach(item => {
        if (firstChoiceField.includes(item)) {
          warnContent += firstChoice[item] + ',';
          signNumber += firstChoice[item].length;
        } else if (alternativeField.includes(item)) {
          warnContent += alternative[item] + ',';
          signNumber += alternative[item].length;
        }
      });

      warnContent = warnContent.slice(0, warnContent.length - 1);

      const station = {
        millID,
        workPlace: param.name,
        workPlaceID: param._id,
        configPlace: param.children.name,
        configPlaceID: param.children._id,
        informProject: customizeHarm.length === 0 ? harmFactors.join(',') : (harmFactors + ',' + customizeHarm),
        signNumber,
        warnContent,
        notificationCard: [],
        responPerson: '',
        putUpDate: '',
        putUpStatus: false,
      };
      stations.push(station);
    }

    docs.forEach(item => {
      if (item.children) {
        buildDate(item, item.millID);
      }
    });
    // 车间分组函数
    const groupWorkPlace = function(arr) {
      const newData = {};
      arr.forEach(item => {
        if (!newData[item.workPlaceID]) {
          newData[item.workPlaceID] = {
            millID: item.millID,
            workPlace: item.workPlace,
            workPlaceID: item.workPlaceID,
            configPlace: '车间入口处',
            configPlaceID: '',
            informProject: '',
            signNumber: 0,
            warnContent: '',
            notificationCard: [],
            responPerson: '',
            putUpDate: '',
            putUpStatus: false,
          };
        }
        const informProjectArr = newData[item.workPlaceID].informProject.split(',');
        const itemInformProjectArr = item.informProject.split(',');
        itemInformProjectArr.forEach(item2 => {
          if (!informProjectArr.includes(item2)) {
            if (newData[item.workPlaceID].informProject.length === 0) {
              newData[item.workPlaceID].informProject += item2;
            } else {
              newData[item.workPlaceID].informProject += ',' + item2;
            }
          }
        });
        const warnContentArr = newData[item.workPlaceID].warnContent.split(',');
        const itemWarnContentArr = item.warnContent.split(',');
        itemWarnContentArr.forEach(item2 => {
          if (!warnContentArr.includes(item2)) {
            if (newData[item.workPlaceID].warnContent.length === 0) {
              newData[item.workPlaceID].warnContent += item2;
            } else {
              newData[item.workPlaceID].warnContent += ',' + item2;
            }
            newData[item.workPlaceID].signNumber++;
          }
        });
      });
      for (const field in newData) {
        const insertIndex = stations.findIndex(item => {
          return newData[field].workPlace === item.workPlace;
        });
        stations.splice(insertIndex, 0, newData[field]);
      }
    };

    groupWorkPlace(stations);
    return stations;
  }
  buildDate(param, wP) {
    const alternativeField = [ '化学', '粉尘', '生物' ];
    const firstChoiceField = [ '噪声', '高温', '紫外线', '金属烟', '井下不良作业条件' ];
    const alternative = {
      化学: [ '当心中毒', '当心腐蚀', '当心有毒气体', '戴防护镜', '戴防毒面具', '戴防护手套', '穿防护服', '穿防护鞋', '注意通风' ],
      粉尘: [ '注意防尘', '戴防尘口罩', '注意通风' ],
      生物: [ '当心感染' ],
    };
    const firstChoice = {
      噪声: [ '噪声有害', '戴护耳器' ],
      高温: [ '注意高温' ],
      紫外线: [ '当心弧光', '当心电离辐射', '穿防护服', '戴防护镜' ],
      金属烟: [ '注意防尘', '戴防尘口罩', '注意通风' ],
      井下不良作业条件: [ '戴防护镜', '戴防毒面具', '戴防护手套', '穿防护服', '穿防护鞋', '注意通风' ],
    };
    let warnContent = '';

    param.harmFactors && param.harmFactors.forEach(item => {
      if (firstChoiceField.includes(item[1])) {
        warnContent += firstChoice[item[1]];
      } else if (alternativeField.includes(item[1])) {
        warnContent += alternative[item[1]];
      }
    });

    const station = {
      workPlace: wP.name,
      workPlaceID: wP._id,
      configPlace: param.name,
      configPlaceID: param._id,
      informProject: param.harmFactors + param.customizeHarm,
      warnContent,
      notificationCard: [],
      responPerson: '',
      putUpDate: '',
    };
    return station;
  }
  async delWarnNotice(EnterpriseID, params) {

    const { ctx } = this;
    let query = [];
    let field = '';
    if (params.stationId && params.workspaceId) { // 删除岗位
      query = [ params.stationId ];
      field += 'configPlaceID';
      // await ctx.model.WarnNotice.updateOne({ EnterpriseID }, { $pull: { tableData: { [field]: { $in: query } } } }, { multi: true });
      await ctx.service.db.updateOne('WarnNotice', { EnterpriseID }, { $pull: { tableData: { [field]: { $in: query } } } }, { multi: true });
    } else if (params.workspaceId) { // 删除车间
      query = [ params.workspaceId ];
      field += 'workPlaceID';
      // await ctx.model.WarnNotice.updateOne({ EnterpriseID }, { $pull: { tableData: { [field]: { $in: query } } } }, { multi: true });
      await ctx.service.db.updateOne('WarnNotice', { EnterpriseID }, { $pull: { tableData: { [field]: { $in: query } } } }, { multi: true });
    } else if (params.millId) { // 删除车间或厂房
      query = params.millId;
      field += 'workPlaceID';
      const field2 = 'millID';
      // await ctx.model.WarnNotice.updateOne({ EnterpriseID }, { $pull: { tableData: { [field]: { $in: query } } } }, { multi: true });
      await ctx.service.db.updateOne('WarnNotice', { EnterpriseID }, { $pull: { tableData: { [field]: { $in: query } } } }, { multi: true });
      // await ctx.model.WarnNotice.updateOne({ EnterpriseID }, { $pull: { tableData: { [field2]: { $in: query } } } }, { multi: true });
      await ctx.service.db.updateOne('WarnNotice', { EnterpriseID }, { $pull: { tableData: { [field2]: { $in: query } } } }, { multi: true });
    }
  }
}

module.exports = WarnNoticeService;
