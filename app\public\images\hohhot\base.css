 /* header */
 * {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
html{
font-size: 62.5%; /* 1rem = 10px, making rem calculations easier */
}

@font-face {
  font-family: "YouSheBiaoTiHei";
  src: url("./YouSheBiaoTiHei.ttf");
}

body {
  font-family: Arial, sans-serif;
  background: #EFEFEF;
}
.clear, .blank { clear: both; width: 100%; overflow: hidden; }
.blank { height: 20px }

.navbar {
  position: absolute;
  top: 0;
  width: 100%;
  /* height: 7.41vh; */
  height: 72.67px;

  /* background: rgba(255, 255, 255, 0.3); */
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0 0 1.5rem;
  z-index: 10;
}

.navbar>.logo {
  width: 22.81vw;
}

.navbar>.link-wrapper{
display: flex;
height: 100%;
}
.navbar>.link-wrapper>.link{
display: flex;
height: 100%;
align-items: center;
}
.navbar>.link-wrapper>.link:last-child{
background: #0174F9;
}
.navbar>.link-wrapper>.link:hover{
border-radius: 0px 0px 5px 0px;
opacity: 1;
background: linear-gradient(180deg, #D1E4F6 0%, #FFFFFF 100%);
border-bottom: 6px solid #0174F9;
}

.navbar>.link-wrapper>.link>a {
  font-size: 18px;
  line-height: 16px; 
  color: #0174F9;
  font-family: Source Han Sans CN;
  padding: 0 38px;
  display: inline-block;
  text-decoration: none;
}
.navbar>.link-wrapper>.link:last-child>a{
font-size: 18px;
line-height: 24px;
font-weight: bold;
color: #FFFFFF;
font-family: Source Han Sans CN;
padding: 0 38px;
display: inline-block;
}

.navbar>.link-wrapper>.link:last-child>:hover{
  color: #0174F9;
}

.wrapper-homeBG{
position: relative;
width: 100%;
}

.homeBG {
  max-width: 100%;
  /* height: 7.41vh; */
  height: 72.67px;
  object-fit: cover;
  object-position: center center;
  overflow: hidden;
  
}

/* .line-homeBG{
  height: 7.41vh;
  width: 1920px;
  background: linear-gradient(to right, #037ece, #eaf5f7);
} */

/* container */
.container {
margin: 4.62vh auto;
}
.container .wrapper-icon{
display: flex;
justify-content: flex-start;
align-items: center;
margin-bottom: 2.4rem;
}
.wrapper-icon .content-icon {
width: 1.15vw;
height: auto;
margin-right: 0.6rem;
}
.wrapper-icon .icon-txt {
font-size: 2.4rem;
font-weight: 500;
line-height: 1.8rem;
color: #3D3D3D;
}
.container .wrapper-card{
width: 14.06vw;
/* height: 16.55vh; */
background: #E4ECF7;
}
.wrapper-card .card-img {
max-width: 100%;
height: auto;
object-fit: cover;
}

/* footer */
.footer {
height: 12.96vh;
font-size:1.4rem;
line-height: 2.1rem;
padding-top: calc(2.2rem + 2vh);
background: #1374D8;
color: #DCECF5;
text-align: center;
}