/**
 * @file 万华商城订单主表
 * @description 万华商城订单主表模型
 * @createDate 2025-03-14
 */
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  const WhOrderMainSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    // 订单编号
    orderNo: {
      type: String,
      required: true,
      unique: true,
      trim: true,
    },
    // 订单名称
    orderName: {
      type: String,
      required: true,
      trim: true,
    },
    // 人员编号
    personnelCode: {
      type: String,
      trim: true,
    },
    // 人员姓名
    personnelName: {
      type: String,
      trim: true,
    },

    // 订单状态 (已通知供应商、未通知供应商、订单失败、已完成、订单暂停、订单作废、供应商已发货、等待审批、等待处理)
    orderStatus: {
      type: String,
      default: '等待处理',
      trim: true,
    },
    // 订单总价
    orderPrice: {
      type: Number,
      required: true,
      min: 0,
    },
    // 组织编码
    EnterpriseID: {
      type: String,
      trim: true,
    },
    // 部门编码
    departmentCode: {
      type: String,
      trim: true,
    },
    // 组织名称
    orgName: {
      type: String,
      trim: true,
    },
    // 支付金额
    goodsPay: {
      type: Number,
      default: 0,
      min: 0,
    },
    // 删除标记 (normal正常，deleted删除)
    deleteFlag: {
      type: String,
      default: 'normal',
      enum: [ 'normal', 'deleted' ],
      trim: true,
    },
    // 创建时间
    createDate: {
      type: Date,
      default: Date.now,
    },
    // 创建人
    createBy: {
      type: String,
      trim: true,
    },
    // 更新时间
    updateDate: {
      type: Date,
    },
    // 更新人
    updateBy: {
      type: String,
      trim: true,
    },
  }, {
    timestamps: { createdAt: 'createDate', updatedAt: 'updateDate' },
    versionKey: false,
    collection: 'wh_order_main',
    minimize: false,
    strict: true,
  });

  // 创建索引
  WhOrderMainSchema.index({ orderNo: 1 }, { unique: true });
  WhOrderMainSchema.index({ personnelCode: 1 });
  WhOrderMainSchema.index({ orderStatus: 1 });
  WhOrderMainSchema.index({ createDate: -1 });
  WhOrderMainSchema.index({ deleteFlag: 1 });

  // 查询中间件 - 默认只查询未删除的数据
  WhOrderMainSchema.pre('find', function() {
    if (!this._conditions.deleteFlag) {
      this._conditions.deleteFlag = 'normal';
    }
  });

  WhOrderMainSchema.pre('findOne', function() {
    if (!this._conditions.deleteFlag) {
      this._conditions.deleteFlag = 'normal';
    }
  });

  return mongoose.model('WhOrderMain', WhOrderMainSchema);
};
