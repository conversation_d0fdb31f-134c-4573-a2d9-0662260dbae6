/**
 * @file 工伤管理控制器
 * @description 处理工伤管理相关的API请求
 * <AUTHOR>
 * @createDate 2025-03-18
 */

const Controller = require('egg').Controller;

/**
 * @controller 工伤管理
 */
class WhIndustrialInjuryController extends Controller {
  /**
   * @summary 获取工伤列表
   * @description 根据筛选条件获取工伤信息列表
   * @return {Promise<void>} 无返回值
   */
  async getList() {
    const { ctx, service } = this;
    try {
      ctx.logger.info('=== 工伤列表控制器开始执行 ===');
      ctx.logger.info('请求参数:', ctx.query);
      const params = ctx.query;
      const result = await service.whIndustrialInjury.getList(params);

      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.logger.error('获取工伤列表失败', err);
      ctx.helper.renderFail(ctx, {
        message: err.message || '获取工伤列表失败',
      });
    }
  }

  /**
   * @summary 获取工伤详情
   * @description 根据ID获取工伤详细信息
   * @return {Promise<void>} 无返回值
   */
  async getDetail() {
    const { ctx, service } = this;
    try {
      const { id } = ctx.query;

      if (!id) {
        throw new Error('缺少必要参数：id');
      }

      const result = await service.whIndustrialInjury.getDetail(id);

      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.logger.error('获取工伤详情失败', err);
      ctx.helper.renderFail(ctx, {
        message: err.message || '获取工伤详情失败',
      });
    }
  }

  /**
   * @summary 保存工伤信息
   * @description 创建或更新工伤信息
   * @return {Promise<void>} 无返回值
   */
  async save() {
    const { ctx, service } = this;
    try {
      const data = ctx.request.body;

      // 参数校验
      if (!data.company) {
        throw new Error('公司不能为空');
      }

      if (!data.department) {
        throw new Error('部门不能为空');
      }

      // 检查伤员信息
      if (Array.isArray(data.injuredEmployees) && data.injuredEmployees.length === 0) {
        throw new Error('至少需要添加一名伤员');
      }

      // 兼容旧版本的数据结构，如果没有injuredEmployees数组但有employeeName和employeeId，创建伤员数组
      if (!Array.isArray(data.injuredEmployees) && data.employeeName && data.employeeId) {
        data.injuredEmployees = [{
          name: data.employeeName,
          employeeId: data.employeeId,
          injuryPart: data.injuryPart || '',
          treatmentStatus: data.employeeStatus || '治疗期',
          workStatus: data.employeeStatus || '治疗期',
        }];
      }

      if (!data.occurrenceTime) {
        throw new Error('发生时间不能为空');
      }

      if (!data.injuryTitle) {
        throw new Error('事故标题不能为空');
      }

      const result = await service.whIndustrialInjury.save(data);

      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: data.id ? '工伤信息更新成功' : '工伤信息创建成功',
      });
    } catch (err) {
      ctx.logger.error('保存工伤信息失败', err);
      ctx.helper.renderFail(ctx, {
        message: err.message || '保存工伤信息失败',
      });
    }
  }

  /**
   * @summary 获取统计数据
   * @description 获取工伤相关统计数据
   * @return {Promise<void>} 无返回值
   */
  async getStats() {
    const { ctx, service } = this;
    try {
      const result = await service.whIndustrialInjury.getStats();

      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.logger.error('获取统计数据失败', err);
      ctx.helper.renderFail(ctx, {
        message: err.message || '获取统计数据失败',
      });
    }
  }
  /**
   * @summary 更新员工状态
   * @description 更新工伤员工状态
   * @return {Promise<void>} 无返回值
   */
  async updateStatus() {
    const { ctx, service } = this;
    try {
      const data = ctx.request.body;

      // 参数校验
      if (!data.id) {
        throw new Error('缺少必要参数：id');
      }

      if (!data.status) {
        throw new Error('员工状态不能为空');
      }

      // 添加员工ID参数用于更新特定伤员的状态
      if (data.employeeRecordId) {
        // 继承传入的员工记录ID
      }

      const result = await service.whIndustrialInjury.updateStatus(data);

      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '员工状态更新成功',
      });
    } catch (err) {
      ctx.logger.error('更新员工状态失败', err);
      ctx.helper.renderFail(ctx, {
        message: err.message || '更新员工状态失败',
      });
    }
  }

  /**
   * @summary 更新处理状态
   * @description 更新工伤处理状态
   * @return {Promise<void>} 无返回值
   */
  async updateProcessingStatus() {
    const { ctx, service } = this;
    try {
      const data = ctx.request.body;

      // 参数校验
      if (!data.id) {
        throw new Error('缺少必要参数：id');
      }

      if (!data.processingStatus) {
        throw new Error('处理状态不能为空');
      }

      const result = await service.whIndustrialInjury.updateProcessingStatus(data);

      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '处理状态更新成功',
      });
    } catch (err) {
      ctx.logger.error('更新处理状态失败', err);
      ctx.helper.renderFail(ctx, {
        message: err.message || '更新处理状态失败',
      });
    }
  }

  /**
   * @summary 添加跟踪记录
   * @description 为工伤添加跟踪记录
   * @return {Promise<void>} 无返回值
   */
  async addFollowUp() {
    const { ctx, service } = this;
    try {
      const data = ctx.request.body;

      // 参数校验
      if (!data.injuryId) {
        throw new Error('缺少必要参数：injuryId');
      }

      if (!data.employeeStatus) {
        throw new Error('员工状态不能为空');
      }

      if (!data.person) {
        throw new Error('跟踪人不能为空');
      }

      if (!data.form) {
        throw new Error('跟踪形式不能为空');
      }

      if (!data.employeeCondition) {
        throw new Error('员工情况不能为空');
      }

      const result = await service.whIndustrialInjury.addFollowUp(data);

      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '跟踪记录添加成功',
      });
    } catch (err) {
      ctx.logger.error('添加跟踪记录失败', err);
      ctx.helper.renderFail(ctx, {
        message: err.message || '添加跟踪记录失败',
      });
    }
  }

  /**
   * @summary 从Pi系统拉取数据
   * @description 从Pi系统拉取工伤数据并同步到系统中
   * @return {Promise<void>} 无返回值
   */
  async fetchFromPi() {
    const { ctx, service } = this;
    try {
      const result = await service.whIndustrialInjury.fetchFromPiSystem();

      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '从Pi系统拉取数据成功',
      });
    } catch (err) {
      ctx.logger.error('从Pi系统拉取数据失败', err);
      ctx.helper.renderFail(ctx, {
        message: err.message || '从Pi系统拉取数据失败',
      });
    }
  }

  async getDepartmentList() {
    const { ctx } = this;
    try {
      const departmentList = await ctx.model.WhIndustrialInjury.distinct('department', { state: '1' });
      ctx.helper.renderSuccess(ctx, {
        data: departmentList,
      });
    } catch (err) {
      ctx.logger.error('获取部门列表失败', err);
      ctx.helper.renderFail(ctx, {
        message: err.message || '获取部门列表失败',
      });
    }
  }

  async getModuleList() {
    const { ctx } = this;
    try {
      const params = ctx.query;
      const query = { state: '1' };
      if (params.department) {
        query.department = params.department;
      }
      const moduleList = await ctx.model.WhIndustrialInjury.distinct('module', query);
      ctx.helper.renderSuccess(ctx, {
        data: moduleList,
      });
    } catch (err) {
      ctx.logger.error('获取模块列表失败', err);
      ctx.helper.renderFail(ctx, {
        message: err.message || '获取模块列表失败',
      });
    }
  }

}

module.exports = WhIndustrialInjuryController;
