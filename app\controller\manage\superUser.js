const Controller = require('egg').Controller;

const {
  superUserRule,
} = require('@validate');
const {
  validatorUtil,
} = require('@utils');

const _ = require('lodash');


class AdminUserController extends Controller {
  // 行政管理员管理==========================================
  async list() {
    const {
      ctx,
      service,
    } = this;
    try {

      const payload = ctx.query;
      const query = {
        state: '1',
      };
      if (payload.type) {
        query.type = +payload.type;
      } else {
        delete payload.type;
      }
      const collections = [
        {
          name: 'admingroups',
          selfKey: 'group',
          foreignKey: '_id',
          asKey: 'group',
        }, {
          name: 'apiUsers',
          selfKey: '_id',
          foreignKey: 'orgId',
          asKey: 'apiUser',
        },
      ];
      const searchKeys = [ '_id', 'userName', 'phoneNum', 'name', 'cname', 'members.name', 'members.phoneNum', 'members.userName' ];
      const params = {
        collections,
        query,
        sort: { date: -1 },
        searchKeys,
        files: {
          _id: 1,
          userName: 1,
          cname: 1,
          name: 1,
          regAdd: 1,
          phoneNum: 1,
          email: 1,
          enable: 1,
          'group._id': 1,
          'group.name': 1,
          'apiUser._id': 1,
          'apiUser.group': 1,
          'apiUser.status': 1,
          members: 1,
          jobTitle: 1,
          powerStatus: 1,
          type: 1,
        },
      };
      const superUserList = await service.superUser.unionQuery(payload, params);
      ctx.helper.renderSuccess(ctx, {
        data: superUserList,
      });

    } catch (err) {

      ctx.helper.renderFail(ctx, {
        message: err,
      });

    }
  }


  async create() {

    const {
      ctx,
      service,
      config,
    } = this;
    try {

      const fields = ctx.request.body || {},
        logo = fields.logo || '';

      const checkPhoneNum = await service.superUser.item(ctx, {
        query: {
          phoneNum: fields.phoneNum,
        },
      });
      if (checkPhoneNum) {
        if (checkPhoneNum instanceof Array) {
          ctx.helper.renderFail(ctx, {
            message: '该手机号已被使用',
          });
          return;
        }
        ctx.helper.renderFail(ctx, {
          message: '该手机号已被使用',
        });
        return;
      }


      const checkCname = await service.superUser.item(ctx, {
        query: {
          cname: fields.cname,
        },
      });
      if (checkCname) {
        ctx.helper.renderFail(ctx, {
          message: '该机构已存在',
        });
        return;
      }

      const configSuperGroupID = config.groupID.superGroupID;
      if (_.isEmpty(configSuperGroupID)) {
        throw new Error('哎呀！网络开小差了！要不,稍后再试试？');
      }
      fields.group = configSuperGroupID;

      const formObj = {
        userName: fields.userName,
        name: fields.name,
        email: fields.email,
        phoneNum: fields.phoneNum,
        countryCode: fields.countryCode,
        landline: fields.landline,
        confirm: fields.confirm,
        group: fields.group,
        enable: fields.enable,
        comments: fields.comments,
        cname: fields.cname,
        area_code: fields.area_code,
        regAdd: fields.regAdd,
        powerStatus: !!fields.powerStatus,
        type: fields.type,
      };

      if (!_.isEmpty(logo)) {
        formObj.logo = logo;
      }

      ctx.body = {
        status: 200,
      };

      ctx.validate(superUserRule.form(ctx), formObj);

      // 单独判断密码
      if (fields.password) {
        if (!validatorUtil.checkPwd(fields.password)) {
          ctx.__('validate_inputCorrect', [ ctx.__('label_password') ]);
        } else {
          formObj.password = fields.password;
        }
      }

      const oldItem = await service.superUser.item(ctx, {
        query: {
          userName: fields.userName,
        },
      });

      if (!_.isEmpty(oldItem)) {
        ctx.helper.renderFail(ctx, {
          message: ctx.__('validate_hadUse_userName'),
        });
        return;
      }

      await service.superUser.create(formObj);

      ctx.helper.renderSuccess(ctx);
    } catch (err) {

      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }


  async getOne() {
    const {
      ctx,
      service,
    } = this;
    try {
      const _id = ctx.query.id;
      const password = ctx.query.password;
      const queryObj = {
        _id,
      };

      if (password) {
        _.assign(queryObj, {
          password,
        });
      }

      const targetItem = await service.superUser.item(ctx, {
        query: queryObj,
        files: '-password-power',
      });
      // 此时需要根据code查找对应行政区划
      const district = [];
      if (targetItem.area_code !== this.config.China.area_code) {
        let districtLast = await service.district.item(ctx, {
          query: {
            area_code: targetItem.area_code.length === 12 ? targetItem.area_code : targetItem.area_code + '000000',
          },
        });
        district.push(districtLast);
        while (districtLast.level !== '0' && districtLast.parent_code !== '0') {
          districtLast = await service.district.item(ctx, {
            query: {
              area_code: districtLast.parent_code,
            },
          });
          district.push(districtLast);
        }
        district.reverse();
      } else {
        district.push(this.config.China);
      }
      district.reverse();

      ctx.helper.renderSuccess(ctx, {
        data: {
          group: targetItem.group,
          targetItem,
          district,
        },
      });

    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }

  }

  async update() {

    const {
      ctx,
      service,
      config,
    } = this;
    try {

      const fields = ctx.request.body || {};
      // const checkPhoneNum = await service.superUser.item(ctx, {
      //   query: {
      //     $or: [
      //       { phoneNum: { $in: phoneNumArr } },
      //       { members: { $elemMatch: { phoneNum: { $in: phoneNumArr } } } },
      //     ],
      //   },
      // });
      const phoneNumArr = fields.members ? [ fields.phoneNum, ...fields.members.map(ele => ele.phoneNum) ] : [ fields.phoneNum ];
      const findPhoneNum = await ctx.service.db.find('SuperUser', {
        $or: [
          { phoneNum: { $in: phoneNumArr } },
          { members: { $elemMatch: { phoneNum: { $in: phoneNumArr } } } },
        ],
      });
      if (findPhoneNum && findPhoneNum.length) {
        if (findPhoneNum.length > 1) {
          ctx.helper.renderFail(ctx, {
            message: '手机号重复',
          });
          return;
        }
        const checkPhoneNum = findPhoneNum[0];
        if (checkPhoneNum._id !== fields._id) {
          ctx.helper.renderFail(ctx, {
            message: '该手机号已被使用',
          });
          return;
        }
      }

      const checkCname = await service.superUser.item(ctx, {
        query: {
          cname: fields.cname,
        },
      });
      if (checkCname && checkCname._id !== fields._id) {
        ctx.helper.renderFail(ctx, {
          message: '该机构已存在',
        });
        return;
      }

      const configSuperGroupID = config.groupID.superGroupID;
      if (_.isEmpty(configSuperGroupID)) {
        throw new Error('哎呀！网络开小差了！要不,稍后再试试？');
      }
      fields.group = configSuperGroupID;

      const formObj = {
        userName: fields.userName,
        name: fields.name,
        email: fields.email,
        logo: fields.logo,
        phoneNum: fields.phoneNum,
        countryCode: fields.countryCode,
        landline: fields.landline,
        group: fields.group,
        enable: fields.enable,
        comments: fields.comments,
        cname: fields.cname,
        area_code: fields.area_code,
        regAdd: fields.regAdd,
        date: new Date(),
        members: fields.members,
        powerStatus: !!fields.powerStatus,
        type: fields.type,
      };
      ctx.validate(superUserRule.form(ctx), formObj);

      // 单独判断密码
      if (fields.password) {
        if (!validatorUtil.checkPwd(fields.password)) {
          ctx.__('validate_inputCorrect', [ ctx.__('label_password') ]);
        } else {
          formObj.password = fields.password;
        }
      }

      const oldResource = await service.superUser.item(ctx, {
        query: {
          userName: fields.userName,
        },
      });

      if (!_.isEmpty(oldResource) && oldResource._id !== fields._id) {
        ctx.helper.renderFail(ctx, {
          message: ctx.__('validate_hadUse_userName'),
        });
        return;
      }
      const old = await service.superUser.update(ctx, fields._id, formObj);

      // const newData = await ctx.model.SuperUser.findOne({ _id: fields._id });
      const newData = await ctx.service.db.findOne('SuperUser', { _id: fields._id });

      ctx.helper.renderSuccess(ctx, {
        data: {
          formObj,
          old,
          newData,
        },
      }
      );

    } catch (err) {

      ctx.helper.renderFail(ctx, {
        message: err,
      });

    }

  }


  async removes() {
    const {
      ctx,
      service,
    } = this;
    try {
      const targetId = ctx.query.ids;
      // TODO 目前只针对删除单一管理员逻辑
      const oldUser = await service.superUser.item(ctx, {
        query: {
          _id: targetId,
        },
      });
      if (_.isEmpty(oldUser)) {
        throw new Error(ctx.__('validate_error_params'));
      }
      await service.superUser.removes(ctx, targetId);
      ctx.helper.renderSuccess(ctx);

    } catch (err) {

      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }
  // ================================================================

  async getAdministrativeDivision() {
    const { ctx, service } = this;
    try {
      const query = ctx.query || {};
      const payload = {
        current: 1,
        pageSize: 10000,
        isPaging: '0',
      };
      let districtNames = query.districtNames;
      let parent_code = query.area_code || '0';
      if (query.root) {
        parent_code = '0';
      }

      let addlist = await service.district.find(payload, {
        sort: {
          id: -1,
        },
        files: 'id parent_code name lng lat zip_code area_code level merger_name short_name',
        query: { parent_code },
      });
      addlist = JSON.parse(JSON.stringify(addlist));
      const addlistCount = addlist.length;
      if (addlistCount > 0 && addlist[0].level === '2') {
        for (let i = 0; i < addlistCount; i++) {
          const districtCount = await service.district.count({ parent_code: addlist[i].area_code });
          if (districtCount === 0) {
            const item = JSON.parse(JSON.stringify(addlist[i]));
            item.hasChildren = true;
            addlist[i] = JSON.parse(JSON.stringify(item));
          }
        }
      }
      if (!districtNames) {
        for (let i = 0; i < addlistCount; i++) {
          const item = addlist[i];
          item.label = item.name;
          item.value = item.name;
          if (item.level === '3') {
            item.leaf = true;
          }
        }
        ctx.helper.renderSuccess(ctx, {
          message: '地址列表',
          data: addlist,
        });
      } else {
        districtNames = JSON.parse(districtNames);
        let nodes = await ctx.service.db.find('District', { name: { $in: districtNames } });
        nodes = await ctx.service.db.find('District', { parent_code: { $in: nodes.map(item => item.area_code) } });
        nodes = nodes.concat(addlist);
        nodes = JSON.parse(JSON.stringify(nodes));
        nodes.forEach(item => {
          item.label = item.name;
          item.value = item.name;
          if (item.level === '3') {
            item.leaf = true;
          }
        });

        const renderTreeDataArr = JSON.parse(JSON.stringify(nodes));
        // ==== renderTreeData ============
        const childArr = _.filter(renderTreeDataArr, doc => {
          return doc.level !== '0';
        });
        const childArrCount = childArr.length;
        const renderTreeDataArrCount = renderTreeDataArr.length;
        for (let i = 0; i < childArrCount; i++) {
          const child = childArr[i];
          for (let j = 0; j < renderTreeDataArrCount; j++) {
            const treeItem = renderTreeDataArr[j];
            if (treeItem.area_code === child.parent_code) {
              if (!treeItem.children) treeItem.children = [];
              treeItem.children.push(child);
              break;
            }
          }
        }
        const treeData = _.filter(renderTreeDataArr, doc => {
          return doc.level === '0';
        });
        // ================================
        ctx.helper.renderSuccess(ctx, {
          message: '地址列表',
          data: treeData,
        });
      }
    } catch (e) {
      ctx.auditLog('错误', `查询行政地址错误：${e.stack} 。`, 'error');
      ctx.helper.renderFail(ctx);
    }
  }

  async getDefaultGroup() {
    this.ctx.helper.renderSuccess(this.ctx, {
      data: {
        superGroupID: this.config.groupID.superGroupID,
        China: this.config.China,
      },
    });
  }
  // 查询/编辑监管单位的power
  async editPower() {
    const {
      ctx,
    } = this;
    const { _id, power } = ctx.request.body;
    let result;
    if (power) { // 编辑
      // console.log(3333333333, _id, power);
      // result = await ctx.model.SuperUser.update({ _id }, { $set: { power } });
      result = await ctx.service.db.updateOne('SuperUser', { _id }, { $set: { power } });
    } else if (_id) { // 查询
      // result = await ctx.model.SuperUser.findOne({ _id }, { power: 1, powerStatus: 1 });
      result = await ctx.service.db.findOne('SuperUser', { _id }, { power: 1, powerStatus: 1 });
    }
    ctx.helper.renderSuccess(ctx, { data: result });
  }
}

module.exports = AdminUserController;
