/**
 * @file 万华商城订单详情表
 * @description 万华商城订单详情表模型
 * @createDate 2025-03-14
 */
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  const WhOrderDetailSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    // 订单编号 - 关联订单主表
    orderNo: {
      type: String,
      required: true,
      trim: true,
    },
    // 商品SKU
    sku: {
      type: String,
      required: true,
      trim: true,
    },
    // 商品名称
    goodsName: {
      type: String,
      required: true,
      trim: true,
    },
    // 购买数量
    buyNumber: {
      type: Number,
      required: true,
      default: 1,
      min: 1,
    },
    // 已发货商品数量
    sendGoodsNumber: {
      type: Number,
      default: 0,
      min: 0,
    },
    // 商品价格
    goodsPrice: {
      type: Number,
      required: true,
      min: 0,
    },
    // 是否退货 (yes是，no否)
    isReturnGoods: {
      type: String,
      default: 'no',
      enum: [ 'no', 'yes' ],
    },
    // 分类编码
    categoryCode: {
      type: String,
      trim: true,
    },
    // 分类名称
    categoryName: {
      type: String,
      trim: true,
    },
    // 是否验收 (yes是，no否)
    isAcceptanceCheck: {
      type: String,
      default: 'no',
      enum: [ 'no', 'yes' ],
    },
    // 删除标记 (normal正常，deleted删除)
    deleteFlag: {
      type: String,
      default: 'normal',
      enum: [ 'normal', 'deleted' ],
    },
    // 创建时间
    createDate: {
      type: Date,
      default: Date.now,
    },
    // 创建人
    createBy: {
      type: String,
    },
    // 更新时间
    updateDate: {
      type: Date,
    },
    // 更新人
    updateBy: {
      type: String,
    },
  }, {
    timestamps: { createdAt: 'createDate', updatedAt: 'updateDate' },
    versionKey: false,
    collection: 'wh_order_detail',
    minimize: false,
    strict: true,
  });

  // 创建索引
  WhOrderDetailSchema.index({ orderNo: 1 });
  WhOrderDetailSchema.index({ sku: 1 });
  WhOrderDetailSchema.index({ createDate: -1 });
  WhOrderDetailSchema.index({ deleteFlag: 1 });

  // 查询中间件 - 默认只查询未删除的数据
  WhOrderDetailSchema.pre('find', function() {
    if (!this._conditions.deleteFlag) {
      this._conditions.deleteFlag = 'normal';
    }
  });

  WhOrderDetailSchema.pre('findOne', function() {
    if (!this._conditions.deleteFlag) {
      this._conditions.deleteFlag = 'normal';
    }
  });

  return mongoose.model('WhOrderDetail', WhOrderDetailSchema);
};
