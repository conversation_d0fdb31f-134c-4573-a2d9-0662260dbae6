const httpProxy = require('http-proxy');
// import { Base64 } from 'js-base64';
const Base64 = require('js-base64').Base64;
module.exports = () => {

  return async function proxyMiddleware(ctx, next) {
    const { oss, filePreviewHost } = ctx.app.config;
    // let bucketName = '';
    // if (oss && oss.name) {
    //   bucketName = oss.name;
    // } else {
    //   bucketName = oss.buckets.default.name;
    // }
    // 定义代理规则
    const proxyRules = [
      // {
      //   path: '/_AMapService/',
      //   target: 'https://restapi.amap.com/',
      //   appendQueryString: true,
      //   queryString: 'jscode=aaaee764033afb4a143e4acd63389e30',
      //   pathRewrite: {
      //     '^/_AMapService/': '',
      //   },
      // },
      {
        path: '/oss/',
        target: `${oss.endPoint}/`,
        appendQueryString: true,
        pathRewrite: {
          '^/oss/': '',
        },
      },
      {
        path: '/filePreview/',
        target: `${filePreviewHost}/`,
        appendQueryString: true,
        pathRewrite: {
          '^/filePreview/': '',
          '/oss/': function(path) {
            // 给url = 后面的encodeURIComponent的解码
            path = path.replace(/url=([^&]*)/, (match, p1) => {
              return `url=${decodeURIComponent(p1)}`;
            });
            console.log('path', path);
            const rewritePath = path.replace('/oss/', `${oss.endPoint}/`);
            console.log('rewritePath22222', rewritePath);
            // 替换掉 onlinePreview?url= 为空
            const ossUrl = rewritePath.replace(/onlinePreview\?url=/, '');
            return `onlinePreview?url=${encodeURIComponent(
              Base64.encode(ossUrl)
            )}`;
          },
        },
      },
    ];
    const oldPath = ctx.request.url.split('?')[0];
    // 匹配代理规则
    const rule = proxyRules.find(rule => oldPath.startsWith(rule.path));
    if (rule) {
      console.log('匹配到代理规则', rule, oldPath);
      // 设置目标 URL
      const target = rule.target;
      const appendQueryString = rule.appendQueryString || false;
      const queryString = rule.queryString || '';

      let proxyTarget = target;
      let rewritePath = '';
      // 处理路径 按照rules中的pathRewrite规则重写路径
      if (rule.pathRewrite) {
        const pathRewrite = rule.pathRewrite;
        for (const key in pathRewrite) {
          const reg = new RegExp(key);
          console.log('reg', reg);
          // 如果匹配到pathRewrite的规则
          if (reg.test(ctx.request.url)) {
            if (typeof pathRewrite[key] === 'function') {
              rewritePath = pathRewrite[key](rewritePath);
            } else {
              rewritePath = ctx.request.url.replace(reg, pathRewrite[key]);
            }
          }
        }
      }
      proxyTarget = proxyTarget + rewritePath;
      // 拼接参数的
      if (appendQueryString) {
        const delimiter = target.includes('?') ? '&' : '?';
        proxyTarget = proxyTarget + delimiter + queryString;
      }
      // 转发请求
      try {
        // 处理body参数
        // 创建代理服务器
        proxyTarget = proxyTarget.replace(/\?$/, '');
        console.log('proxyTarget', proxyTarget);
        // 正则替换掉最后一个?为空
        const proxyServer = httpProxy.createProxyServer({
          changeOrigin: true,
          ignorePath: true,
          secure: false,
          logLevel: 'debug',
          target: proxyTarget,
        });
        proxyServer.on('proxyReq', function(proxyReq) {
          proxyReq.setHeader('Cookie', '');
          // console.log('proxyReq', proxyReq);
          if (ctx.request.rawBody) {
            const bodyData = ctx.request.rawBody;
            // incase if content-type is application/x-www-form-urlencoded -> we need to change to application/json
            proxyReq.setHeader('Content-Length', Buffer.byteLength(bodyData));
            // stream the content
            proxyReq.write(bodyData);
          }
        });
        await new Promise((resolve, reject) => {
          proxyServer.web(ctx.req, ctx.res, { target: proxyTarget }, err => {
            if (err) {
              reject(err);
            } else {
              resolve();
            }
          });
        });
      } catch (error) {
        ctx.auditLog('代理错误', `${error} 。`, 'error');
      }
    } else {
      // 如果没有匹配的代理规则，继续下一个中间件处理
      await next();
    }
  };
};
