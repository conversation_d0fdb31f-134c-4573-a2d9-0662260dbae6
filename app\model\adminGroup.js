/**
 * Created by Zhanglc on 2022/3/16.
 * 所有端角色对象
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const AdminGroupSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    name: String, // 角色名
    power: [{ // 内部权限 例如：企业角色、行政角色
      type: String,
      ref: 'AdminResource',
    }],
    apiPower: [{ // 对外API权限
      type: String,
      ref: 'ApiResource',
    }],
    date: { // 创建时间
      type: Date,
      default: Date.now,
    },
    comments: String, // 描述
  });

  return mongoose.model('AdminGroup', AdminGroupSchema, 'admingroups');
};
