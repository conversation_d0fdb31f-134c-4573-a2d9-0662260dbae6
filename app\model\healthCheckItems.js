/**
 * 体检项目详情
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  const HealthCheckItemsSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    name: String, // 项目名称
    code: String, // 项目编码
    // className: String, // 分类名称
    classId: { // 分类id
      type: String,
      ref: 'HealthCheckClass',
    },
    unit: String, // 计量单位
    lowerLimit: String, // 下限
    upperLimit: String, // 上限
    valType: String, // 检测结果值类型, string字符型 number数值型
    defaultVal: String, // 检测结果默认值
  }, { timestamps: true });

  return mongoose.model('HealthCheckItems', HealthCheckItemsSchema, 'healthCheckItems');
};
