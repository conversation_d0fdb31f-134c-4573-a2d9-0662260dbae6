const fs = require('fs');
const path = require('path');
const _ = require('lodash');
const CHANGE_STREAMS = Symbol('Application#changeStreams');
const HANDLERS = Symbol('Application#changeStreamHandlers');

module.exports = {

  // 获取插件api白名单
  getExtendApiList() {
    const app = this;
    const pluginFile = path.join(app.config.baseDir, 'config/plugin.js');
    const pluginInfo = require(pluginFile);
    const plugins = [];
    const pluginAdminApiWhiteList = [];
    for (const pluginItem in pluginInfo) {

      // 1、开启插件，2、已成功加载，3、内部(jk)插件
      // hashOwnProperty 判断自身属性是否存在
      if (pluginInfo.hasOwnProperty(pluginItem) && pluginInfo[pluginItem].enable && !_.isEmpty(app.config[pluginItem]) && pluginItem.indexOf('jk') === 0) {

        const {
          adminApi,
        } = app.config[pluginItem];

        // 获取后台接口白名单
        for (const item of adminApi) {
          if (item.noPower && item.url) {
            pluginAdminApiWhiteList.push(item.url);
          }
        }
        plugins.push(pluginItem);
      }
    }
    return {
      plugins,
      adminApiWhiteList: pluginAdminApiWhiteList,
    };
  },

  // 初始化数据模型
  initExtendModel(modelsPath) {
    const app = this;
    fs.readdirSync(modelsPath).forEach(function(extendName) {
      console.log(`挂载 ${path.basename(extendName, '.js')} 模型成功！`);
      if (extendName) {
        const filePath = `${modelsPath}/${extendName}`;
        if (fs.existsSync(filePath)) {
          const modelKey = path.basename(extendName.charAt(0).toUpperCase() + extendName.slice(1), '.js');
          if (_.isEmpty(app.model[modelKey])) {
            const targetModel = app.loader.loadFile(filePath);
            app.model[modelKey] = targetModel;
          }
        }
      }
    });
  },

  // 初始化插件路由
  async initPluginRouter(ctx, pluginConfig = {}, pluginManageController = {}, pluginApiController = {}, next = {}) {
    // console.log('\r\n\r\n3.数据传入公共方法并进行接口验证而后定向请求  app>extend>application>initPluginRouter\r\n');
    const app = this;
    let isFontApi = false;
    let isAdminApi = false;
    let targetControllerName = '';
    let targetApiItem = {};
    if (!_.isEmpty(pluginConfig)) {
      const {
        adminApi,
        fontApi,
      } = pluginConfig;

      const targetRequestUrl = ctx.request.url;
      // console.log(`请求路径：${targetRequestUrl.split('?')[0]}`);
      if (targetRequestUrl.indexOf('/api/') >= 0) {
        // console.log('api路径，进行fontApi处理');
        for (const fontApiItem of fontApi) {
          const {
            url,
            method,
            controllerName,
          } = fontApiItem;

          const targetApi = targetRequestUrl.replace('/api/', '').split('?')[0];
          if (ctx.request.method === method.toUpperCase() && targetApi === url && controllerName) {
            isFontApi = true;
            targetControllerName = controllerName;
            targetApiItem = fontApiItem;
            break;
          }

        }

      } else if (targetRequestUrl.indexOf('/manage/') >= 0) {

        // console.log('manage路径，进行adminApi处理');
        for (const adminApiItem of adminApi) {

          const {
            url,
            method,
            controllerName,
          } = adminApiItem;

          const targetApi = targetRequestUrl.replace('/manage/', '').split('?')[0];
          if (ctx.request.method === method.toUpperCase() && targetApi === url && controllerName) {
            isAdminApi = true;
            targetControllerName = controllerName;
            targetApiItem = adminApiItem;
            break;
          }
        }
      }
    }
    // console.log(isAdminApi);
    // console.log('=============================插件管理控制')
    // console.log(pluginManageController);
    // console.log(targetControllerName);
    // console.log(!_.isEmpty(pluginManageController));
    if (isAdminApi && !_.isEmpty(pluginManageController) && targetControllerName) {
      // console.log('处理成功！请求AdminApi');
      await pluginManageController[targetControllerName](ctx, app);
    } else if (isFontApi && !_.isEmpty(pluginApiController) && targetControllerName) {
      if (targetApiItem.authToken) {
        if (ctx.session.logined) {
          // console.log('处理成功(authToken)！请求FontApi');
          await pluginApiController[targetControllerName](ctx, app, next);
        } else {
          ctx.helper.renderFail(ctx, {
            message: '请先登录',
          });
        }
      } else {
        // console.log('处理成功！请求FontApi');
        await pluginApiController[targetControllerName](ctx, app, next);
      }
    }
  },

  /**
   * 获取或初始化 changeStreams 集合
   */
  get changeStreams() {
    if (!this[CHANGE_STREAMS]) {
      this[CHANGE_STREAMS] = new Map();
    }
    return this[CHANGE_STREAMS];
  },

  /**
   * 获取或初始化 handlers 集合
   */
  get changeStreamHandlers() {
    if (!this[HANDLERS]) {
      this[HANDLERS] = new Map();
    }
    return this[HANDLERS];
  },

  /**
   * 注册集合的变更处理器
   * @param {string} collectionName 集合名称
   * @param {Object} handlerService 处理器服务实例
   */
  registerChangeStreamHandler(collectionName, handlerService) {
    this.changeStreamHandlers.set(collectionName, handlerService);
    this.logger.info(`已注册集合 ${collectionName} 的变更处理器`);
  },

  /**
   * 启动所有已注册的变更流监听
   */
  async startAllChangeStreams() {
    for (const collectionName of this.changeStreamHandlers.keys()) {
      await this.startChangeStream(collectionName);
    }
  },

  // 修改启动变更流的方法，添加更强大的错误处理
  async startChangeStream(collectionName, forceStartFromLatest = false) {
    if (!this.changeStreamHandlers.has(collectionName)) {
      this.logger.warn(`集合 ${collectionName} 未注册处理器，无法启动变更流`);
      return;
    }

    if (this.changeStreams.has(collectionName)) {
      this.logger.info(`集合 ${collectionName} 的变更流已在运行`);
      return;
    }

    try {
    // 创建临时 context 来访问模型和服务
      const ctx = this.createAnonymousContext();

      // 获取处理器
      // const handler = this.changeStreamHandlers.get(collectionName);

      // 获取恢复令牌（除非强制从最新开始）
      let resumeToken = null;
      if (!forceStartFromLatest) {
        resumeToken = await this.getChangeStreamResumeToken(collectionName, ctx);
      }

      if (forceStartFromLatest && resumeToken) {
        this.logger.info(`${collectionName} 强制从最新位置开始，忽略已保存的resume token`);
      }

      // 获取集合引用
      const collection = this.getMongoCollection(collectionName, ctx);
      if (!collection) {
        throw new Error(`找不到集合 ${collectionName}`);
      }

      // 创建变更流选项
      const options = {
        fullDocument: 'updateLookup',
      };
      if (resumeToken && !forceStartFromLatest) {
        options.resumeAfter = resumeToken;
        this.logger.info(`${collectionName} 使用resume token从上次位置继续监听`);
      } else {
        this.logger.info(`${collectionName} 从最新位置开始监听变更流`);
      }

      // 创建变更流
      const changeStream = collection.watch([], options);
      this.changeStreams.set(collectionName, changeStream);

      // 监听变更事件
      changeStream.on('change', async change => {
        try {
        // 每次处理时创建新的上下文，确保请求隔离
          const eventCtx = this.createAnonymousContext();

          // 保存恢复令牌
          await this.saveChangeStreamResumeToken(collectionName, change._id, eventCtx);

          // 确保处理之前有效的处理器和集合
          if (!this.changeStreamHandlers.has(collectionName)) {
            this.logger.warn(`无法处理 ${collectionName} 变更事件：处理器不存在`);
            return;
          }

          // 调用处理器方法
          await this.processChangeStreamEvent(collectionName, change, eventCtx);
        } catch (error) {
          this.logger.error(`处理 ${collectionName} 变更时出错:`, error);
        }
      });

      // 增强错误处理
      changeStream.on('error', async error => {
        this.logger.error(`${collectionName} 变更流出错:`, error);

        // 关闭当前流
        try {
          await this.closeChangeStream(collectionName);
        } catch (closeError) {
          this.logger.error(`关闭 ${collectionName} 变更流失败:`, closeError);
        }

        // 检查是否为ChangeStreamHistoryLost错误（code: 286）
        const isHistoryLostError = error.code === 286 || error.codeName === 'ChangeStreamHistoryLost';
        let shouldClearToken = false;

        if (isHistoryLostError) {
          this.logger.warn(`${collectionName} 变更流历史丢失，将从最新位置重新开始监听`);
          this.logger.info(`错误详情: code=${error.code}, codeName=${error.codeName}, message=${error.message}`);
          shouldClearToken = true;
        } else {
          this.logger.error(`${collectionName} 变更流遇到其他错误: code=${error.code}, codeName=${error.codeName}`);
        }

        // 使用配置中的重试参数
        const retryConfig = this.config.changeStream ? this.config.changeStream.retry : {};
        let retryDelay = retryConfig.initialDelay || 2000; // 初始重试延迟
        const maxRetries = retryConfig.maxRetries || 10; // 最大重试次数
        const maxDelay = retryConfig.maxDelay || 30000; // 最大延迟
        const backoffMultiplier = retryConfig.backoffMultiplier || 1.5; // 退避乘数
        let retryCount = 0;

        const retryConnection = async () => {
          try {
            if (retryCount >= maxRetries) {
              this.logger.error(`${collectionName} 变更流重连失败达到最大次数 ${maxRetries}，不再重试`);
              return;
            }

            retryCount++;
            this.logger.info(`尝试重新连接 ${collectionName} 变更流，第 ${retryCount} 次尝试`);

            // 如果是历史丢失错误，清除resume token
            if (shouldClearToken) {
              const ctx = this.createAnonymousContext();
              await this.clearChangeStreamResumeToken(collectionName, ctx);
              shouldClearToken = false; // 只清除一次
              // 从最新位置重新开始
              await this.startChangeStream(collectionName, true);
              this.logger.info(`${collectionName} 已从最新位置重新启动变更流`);

              // 检查是否需要触发数据同步
              const shouldSync = this.shouldTriggerSync(collectionName);
              if (shouldSync) {
                this.logger.info(`${collectionName} 触发全量数据同步以确保一致性`);
                try {
                  await this.triggerDataSync(collectionName, ctx);
                } catch (syncError) {
                  this.logger.error(`${collectionName} 数据同步失败:`, syncError);
                }
              }
            } else {
              await this.startChangeStream(collectionName);
            }
            this.logger.info(`${collectionName} 变更流重连成功`);
          } catch (retryError) {
            this.logger.error(`${collectionName} 变更流重连失败:`, retryError);

            // 如果重连时仍然是历史丢失错误，确保清除token
            if ((retryError.code === 286 || retryError.codeName === 'ChangeStreamHistoryLost') && !shouldClearToken) {
              this.logger.warn('重连时仍遇到历史丢失错误，将清除resume token并从最新位置开始');
              shouldClearToken = true;
            }

            // 指数退避算法
            retryDelay = Math.min(retryDelay * backoffMultiplier, maxDelay);

            setTimeout(retryConnection, retryDelay);
          }
        };

        // 开始重试过程
        setTimeout(retryConnection, retryDelay);
      });

      // 添加关闭监听
      changeStream.on('close', () => {
        this.logger.info(`${collectionName} 变更流已关闭`);
        this.changeStreams.delete(collectionName);
      });

      // 添加结束监听
      changeStream.on('end', () => {
        this.logger.info(`${collectionName} 变更流已结束`);
        this.changeStreams.delete(collectionName);
      });

      this.logger.info(`已启动 ${collectionName} 变更监听`);
    } catch (error) {
      this.logger.error(`启动 ${collectionName} 变更流失败:`, error);
      throw error;
    }
  },

  // 添加专门的错误处理函数
  handleUnexpectedErrors() {
  // 处理未捕获的Promise异常
    process.on('unhandledRejection', reason => {
      this.logger.error('Change Stream 未处理的Promise拒绝:', reason);

      // 尝试重启所有可能受影响的Change Stream
      for (const collectionName of this.changeStreamHandlers.keys()) {
        if (!this.changeStreams.has(collectionName)) {
          this.startChangeStream(collectionName)
            .catch(err => this.logger.error(`重启 ${collectionName} 变更流失败:`, err));
        }
      }
    });

    // 处理未捕获的异常
    process.on('uncaughtException', error => {
      this.logger.error('Change Stream 未捕获的异常:', error);
      // 注意: 通常在生产环境中，uncaughtException应该导致进程重启
      // 这里只处理特定情况
      if (error.code === 'ECONNRESET') {
        this.logger.warn('检测到连接重置错误，尝试恢复变更流...');
      // 实现恢复逻辑 - 可能是重启应用或特定组件
      }
    });
  },

  /**
   * 关闭单个集合的变更流
   * @param {string} collectionName 集合名称
   */
  async closeChangeStream(collectionName) {
    const changeStream = this.changeStreams.get(collectionName);
    if (changeStream) {
      this.logger.info(`关闭 ${collectionName} 变更流...`);
      await changeStream.close();
      this.changeStreams.delete(collectionName);
    }
  },

  /**
   * 关闭所有变更流
   */
  async closeAllChangeStreams() {
    for (const collectionName of this.changeStreams.keys()) {
      await this.closeChangeStream(collectionName);
    }
  },

  // /**
  //  * 处理变更事件
  //  * @param {string} collectionName 集合名称
  //  * @param {Object} change 变更事件对象
  //  * @param {Object} ctx 上下文对象
  //  */
  async processChangeStreamEvent(collectionName, change, ctx) {
    const handler = this.changeStreamHandlers.get(collectionName);
    if (!handler) return;

    // 验证处理前的基础条件
    const collection = this.getMongoCollection(collectionName, ctx);
    if (!collection) {
      this.logger.error(`处理 ${collectionName} 变更事件失败：无法获取集合引用`);
      return;
    }

    // 为处理器添加集合引用
    handler.collection = collection;

    const { operationType } = change;

    // 根据操作类型调用对应的处理方法
    switch (operationType) {
      case 'insert':
        if (typeof handler.handleInsert === 'function') {
          await handler.handleInsert(change, ctx);
        }
        break;
      case 'update':
        if (typeof handler.handleUpdate === 'function') {
          await handler.handleUpdate(change, ctx);
        }
        break;
      case 'replace':
        if (typeof handler.handleReplace === 'function') {
          await handler.handleReplace(change, ctx);
        }
        break;
      case 'delete':
        if (typeof handler.handleDelete === 'function') {
          await handler.handleDelete(change, ctx);
        }
        break;
      case 'drop':
        if (typeof handler.handleDrop === 'function') {
          await handler.handleDrop(change, ctx);
        }
        break;
      case 'dropDatabase':
        if (typeof handler.handleDropDatabase === 'function') {
          await handler.handleDropDatabase(change, ctx);
        }
        break;
      case 'rename':
        if (typeof handler.handleRename === 'function') {
          await handler.handleRename(change, ctx);
        }
        break;
      default:
        if (typeof handler.handleOther === 'function') {
          await handler.handleOther(change, ctx);
        }
    }

    // 处理所有事件的通用方法
    if (typeof handler.handleAny === 'function') {
      await handler.handleAny(change, ctx);
    }
  },

  //   /**
  //  * 获取MongoDB集合引用
  //  * @param {string} collectionName 集合名称
  //  * @param {Object} ctx 上下文对象
  //  * @returns {Object} 集合引用
  //  */
  getMongoCollection(collectionName, ctx) {
    // 不依赖 camelize 直接尝试获取模型
    // 先尝试常见的命名约定
    const possibleModelNames = [
      collectionName, // 原始名称
      collectionName.charAt(0).toUpperCase() + collectionName.slice(1), // 首字母大写
      collectionName.replace(/^([a-z])|-([a-z])/g, g => g.toUpperCase().replace('-', '')), // 驼峰式
    ];

    // 尝试所有可能的模型名
    for (const modelName of possibleModelNames) {
      if (ctx.model[modelName] && ctx.model[modelName].collection) {
        return ctx.model[modelName].collection;
      }
    }

    // 如果模型不存在或模型没有collection属性，直接从数据库获取
    try {
      if (this.mongoose && this.mongoose.connection) {
        return this.mongoose.connection.collection(collectionName);
      }
      this.logger.error(`无法获取集合 ${collectionName}：mongoose连接不可用`);
      return null;

    } catch (error) {
      this.logger.error(`获取集合 ${collectionName} 失败:`, error);
      return null;
    }
  },

  // /**
  //  * 获取恢复令牌
  //  * @param {string} collectionName 集合名称
  //  * @param {Object} ctx 上下文对象
  //  * @returns {Object} 恢复令牌
  //  */
  async getChangeStreamResumeToken(collectionName, ctx) {
    try {
      // 确保模型存在
      if (!ctx.model.ChangeStreamToken) {
        this.logger.error('ChangeStreamToken 模型未定义');
        return null;
      }

      const tokenRecord = await ctx.model.ChangeStreamToken.findOne({
        _id: collectionName,
      });
      return tokenRecord ? tokenRecord.resumeToken : null;
    } catch (error) {
      this.logger.error(`获取 ${collectionName} 恢复令牌失败:`, error);
      return null;
    }
  },

  // /**
  //  * 保存恢复令牌
  //  * @param {string} collectionName 集合名称
  //  * @param {Object} token 恢复令牌
  //  * @param {Object} ctx 上下文对象
  //  */
  async saveChangeStreamResumeToken(collectionName, token, ctx) {
    try {
      await ctx.model.ChangeStreamToken.updateOne(
        { _id: collectionName },
        {
          resumeToken: token,
          updatedAt: new Date(),
        },
        { upsert: true }
      );
    } catch (error) {
      this.logger.error(`保存 ${collectionName} 恢复令牌失败:`, error);
    }
  },

  /**
   * 清除恢复令牌
   * @param {string} collectionName 集合名称
   * @param {Object} ctx 上下文对象
   */
  async clearChangeStreamResumeToken(collectionName, ctx) {
    try {
      await ctx.model.ChangeStreamToken.deleteOne({ _id: collectionName });
      this.logger.info(`已清除 ${collectionName} 的恢复令牌`);
    } catch (error) {
      this.logger.error(`清除 ${collectionName} 恢复令牌失败:`, error);
    }
  },

  /**
   * 判断是否应该触发数据同步
   * @param {string} collectionName 集合名称
   * @returns {boolean} 是否触发同步
   */
  shouldTriggerSync(collectionName) {
    const config = this.config.changeStream;
    if (!config || !config.syncOnHistoryLost) {
      return false;
    }

    const collectionConfig = config.collections && config.collections[collectionName];
    if (collectionConfig && collectionConfig.syncOnHistoryLost === false) {
      return false;
    }

    return true;
  },

  /**
   * 触发数据同步
   * @param {string} collectionName 集合名称
   * @param {Object} ctx 上下文对象
   */
  async triggerDataSync(collectionName, ctx) {
    try {
      // 根据集合名称调用相应的同步方法
      switch (collectionName) {
        case 'millConstructions':
          if (ctx.service.millChangeStreamHandler &&
              typeof ctx.service.millChangeStreamHandler.performInitialSync === 'function') {
            const result = await ctx.service.millChangeStreamHandler.performInitialSync();
            this.logger.info(`${collectionName} 全量同步完成:`, result);
          } else {
            this.logger.warn(`${collectionName} 未找到performInitialSync方法`);
          }
          break;
        default:
          this.logger.warn(`${collectionName} 未配置数据同步方法`);
      }
    } catch (error) {
      this.logger.error(`${collectionName} 数据同步过程中出错:`, error);
      throw error;
    }
  },

};
