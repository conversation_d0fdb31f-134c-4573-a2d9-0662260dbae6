// 机构资质 Controller

const Controller = require('egg').Controller;
const moment = require('moment');

class OrgQualifiesController extends Controller {

  // 机构资质上传页面
  async orgQualifies() {
    const { ctx, app } = this;
    const serviceUserInfo = ctx.session.serviceUserInfo;
    if (!serviceUserInfo) {
      ctx.redirect(`${ctx.session.basePath}/login`);
      return;
    }
    const siteSeo = this.app.config.siteSeo;
    await ctx.render('manage/orgQualifies', {
      staticRootPath: app.config.static.prefix,
      pageTitle: siteSeo.title,
      pageDescription: siteSeo.description,
      pageKeywords: siteSeo.keywords,
      pageAuthor: siteSeo.author,
    });
  }

  // 机构审核页面
  async orgReview() {
    const { ctx, app } = this;
    const serviceUserInfo = ctx.session.serviceUserInfo;
    if (!serviceUserInfo) {
      ctx.redirect(`${ctx.session.basePath}/login`);
      return;
    }
    await ctx.render('manage/orgReview', {
      staticRootPath: app.config.static.prefix,
      siteSeo: this.app.config.siteSeo,
    });
  }

  // 添加资质
  async create() {
    const { ctx, app } = this;
    const params = ctx.request.body;
    // params.lineOfBusiness = params.lineOfBusiness.toString();
    console.log(1111111, params);
    // const static_path = app.config.static.prefix + app.config.upload_http_path;
    // const newDetectionMechanism = new ctx.model.DetectionMechanism(params);
    // await newDetectionMechanism.save()
    await ctx.service.db.create('DetectionMechanism', params)
      .then(async detectionMechanism => {
        const result = JSON.parse(JSON.stringify(detectionMechanism));
        // result.img = static_path + detectionMechanism.img;
        result.img = await ctx.helper.concatenatePath({
          path: app.config.upload_http_path + detectionMechanism.img,
        });
        ctx.body = {
          status: 200,
          data: result,
          msg: '资质添加成功',
        };
      })
      .catch(err => console.log(err));
  }

  // 修改更新资质信息
  async update() {
    const { ctx } = this;
    const params = ctx.request.body;
    const _id = params._id || '';
    const qualifies = params.qualifies;
    const org_status = params.org_status;
    delete params._id;
    delete params.qualifies;
    delete params.org_status;
    // const qualifile = await ctx.model.DetectionMechanism.findOne({ _id });
    const qualifile = await ctx.service.db.findOne('DetectionMechanism', { _id });
    if (qualifile) {
      // await ctx.model.DetectionMechanism.update(
      //   { _id },
      //   { ...params, ctime: Date.now() },
      //   { new: true }
      // )
      await ctx.service.db.updateOne('DetectionMechanism', { _id }, { ...params, ctime: Date.now() }, { new: true })
        .then(res => {
          if (res.ok === 1) {
            if (qualifies.length && org_status !== 3) { // 更新机构状态
              // ctx.model.ServiceOrg.update(
              //   { _id: ctx.session.serviceUserInfo.EnterpriseID },
              //   { status: 2, ctime: Date.now() }
              // )
              ctx.service.db.updateOne('ServiceOrg', { _id: ctx.session.serviceUserInfo.EnterpriseID }, { status: 2, ctime: Date.now() })
                .then(res => {
                  console.log('机构状态已更改:', res);
                });
            }
            // 返回数据
            ctx.body = {
              status: 200,
              msg: '资质修改成功',
            };
          } else {
            ctx.body = {
              status: 500,
              msg: '资质修改失败',
            };
          }
        })
        .catch(err => console.log(err));
    } else {
      ctx.body = {
        status: 500,
        msg: '资质不存在',
      };
    }


  }

  // 通过机构名称获取相关资质
  async getByName() {
    const { ctx, app } = this;
    const params = ctx.request.body;
    const result = await ctx.service.db.find('DetectionMechanism', { $and: [{ name: params.name }, { status: true }] });
    if (result && result.length > 0) {
      ctx.body = {
        status: true,
        data: result,
        msg: '数据获取成功',
        static_path: app.config.static.prefix + app.config.upload_http_path,
      };
    } else {
      ctx.body = {
        status: false,
        data: [],
        msg: '未查到相关数据',
      };
    }
  }

  // 通过机构id获取相关资质
  async getById() {
    const { ctx, app } = this;
    const _id = ctx.query.id;
    const result = await ctx.service.db.find('DetectionMechanism', { $and: [{ _id }, { status: true }] });
    if (result && result.length > 0) {
      ctx.body = {
        status: true,
        data: result[0],
        msg: '数据获取成功',
        static_path: app.config.static.prefix + app.config.upload_http_path,
      };
    } else {
      ctx.body = {
        status: false,
        data: [],
        msg: '未查到相关数据',
      };
    }
  }

  // 获取所有资质
  async findAll() {
    const { ctx, app } = this;
    const result = await ctx.service.db.find('DetectionMechanism', {});
    if (result && result.length > 0) {
      ctx.body = {
        status: true,
        data: result,
        msg: '数据获取成功',
        static_path: app.config.static.prefix + app.config.upload_http_path,
      };
    } else {
      ctx.body = {
        status: false,
        data: [],
        msg: '数据获取失败',
      };
    }
  }

  // 获取所有资质
  async findByIdArr() {
    const { ctx, app } = this;
    const idArr = ctx.request.body.idArr || [];
    const result = await ctx.service.db.find('DetectionMechanism', { _id: { $in: idArr }, status: true });
    if (result) {
      const static_path = app.config.static.prefix + app.config.upload_http_path;
      const response = result.map(ele => {
        const newEle = JSON.parse(JSON.stringify(ele));
        if (ele.ctime) newEle.ctime = moment(ele.ctime).format('YYYY-MM-DD hh:mm');
        if (ele.validTime) newEle.validTime = moment(ele.validTime).format('YYYY-MM-DD');
        if (ele.img) newEle.img = static_path + ele.img;
        return newEle;
      });
      ctx.body = {
        status: 200,
        data: response,
        msg: '数据获取成功',

      };
    } else {
      ctx.body = {
        status: false,
        data: [],
        msg: '数据获取失败',
      };
    }
  }

  // 确认上传机构资质，提交审核
  async submit() {
    const { ctx } = this;
    const params = ctx.request.body;
    // const result = await ctx.model.ServiceOrg.update(
    //   { _id: params._id },
    //   { qualifies: params.qualifies },
    //   { new: true }
    // );
    const result = await ctx.service.db.updateOne('ServiceOrg', { _id: params._id }, { qualifies: params.qualifies }, { new: true });
    if (result.ok === 1) {

      if (!params.status) { // 更改机构审核状态
        // await ctx.model.ServiceOrg.update(
        //   { _id: params._id },
        //   { status: 2, ctime: Date.now() },
        //   { new: true }
        // );
        await ctx.service.db.updateOne('ServiceOrg', { _id: params._id }, { status: 2, ctime: Date.now() }, { new: true });
      }
      ctx.auditLog('添加机构', `当前用户未注册机构，提交_id为 ${params._id} 的机构信息成功。`);
      // 返回数据
      ctx.body = {
        status: true,
        data: result,
        msg: '机构资质提交成功',
      };
    } else {
      ctx.body = {
        status: false,
        data: result,
        msg: '机构资质提交失败',
      };
    }

  }

  // 删除某个资质
  async delete() {
    const { ctx } = this;
    const _id = ctx.query._id || '';
    if (!_id) {
      ctx.body = {
        status: 400,
        msg: '_id必须传',
      };
    }
    // 查询
    // const result = await ctx.model.DetectionMechanism.findOne({ _id });
    const result = await ctx.service.db.findOne('DetectionMechanism', { _id });
    if (result && result._id) {
      // 更改资质状态为false
      // await ctx.model.DetectionMechanism.updateOne({ _id }, { status: false, ctime: Date.now() })
      await ctx.service.db.updateOne('DetectionMechanism', { _id }, { status: false, ctime: Date.now() })
        .then(res => {
          // 在机构信息中删除该资质
          if (res.ok === 1) {
            ctx.body = {
              status: 200,
              msg: '资质删除成功',
            };
          } else {
            ctx.body = {
              status: 500,
              msg: '资质删除失败',
            };
          }
        });
    } else {
      ctx.body = {
        status: 500,
        msg: '未找到相关资质',
      };
    }

  }


}

module.exports = OrgQualifiesController;
