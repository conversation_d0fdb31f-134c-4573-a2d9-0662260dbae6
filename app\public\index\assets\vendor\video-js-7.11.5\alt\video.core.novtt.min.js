/**
 * @license
 * Video.js 7.11.5 <http://videojs.com/>
 * Copyright Brightcove, Inc. <https://www.brightcove.com/>
 * Available under Apache License Version 2.0
 * <https://github.com/videojs/video.js/blob/main/LICENSE>
 *
 * Includes vtt.js <https://github.com/mozilla/vtt.js>
 * Available under Apache License Version 2.0
 * <https://github.com/mozilla/vtt.js/blob/main/LICENSE>
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e=e||self).videojs=t()}(this,function(){"use strict";var h="7.11.5",e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function t(e,t){return e(t={exports:{}},t.exports),t.exports}for(var n,d="undefined"!=typeof window?window:"undefined"!=typeof e?e:"undefined"!=typeof self?self:{},i="undefined"!=typeof e?e:"undefined"!=typeof window?window:{},p="undefined"!=typeof document?document:i["__GLOBAL_DOCUMENT_CACHE@4"]||(i["__GLOBAL_DOCUMENT_CACHE@4"]={}),f={prefixed:!0},r=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror","fullscreen"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror","-webkit-full-screen"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror","-moz-full-screen"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError","-ms-fullscreen"]],s=r[0],o=0;o<r.length;o++)if(r[o][1]in p){n=r[o];break}if(n){for(var a=0;a<n.length;a++)f[s[a]]=n[a];f.prefixed=n[0]!==s[0]}var c=[],l=function(a,l){return function(e,t,n){var i=l.levels[t],r=new RegExp("^("+i+")$");if("log"!==e&&n.unshift(e.toUpperCase()+":"),n.unshift(a+":"),c){c.push([].concat(n));var s=c.length-1e3;c.splice(0,0<s?s:0)}if(d.console){var o=d.console[e];o||"debug"!==e||(o=d.console.info||d.console.log),o&&i&&r.test(e)&&o[Array.isArray(n)?"apply":"call"](d.console,n)}}};var v=function t(n){function i(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];r("log",s,t)}var r,s="info";return r=l(n,i),i.createLogger=function(e){return t(n+": "+e)},i.levels={all:"debug|log|warn|error",off:"",debug:"debug|log|warn|error",info:"log|warn|error",warn:"warn|error",error:"error",DEFAULT:s},i.level=function(e){if("string"==typeof e){if(!i.levels.hasOwnProperty(e))throw new Error('"'+e+'" in not a valid log level');s=e}return s},(i.history=function(){return c?[].concat(c):[]}).filter=function(t){return(c||[]).filter(function(e){return new RegExp(".*"+t+".*").test(e[0])})},i.history.clear=function(){c&&(c.length=0)},i.history.disable=function(){null!==c&&(c.length=0,c=null)},i.history.enable=function(){null===c&&(c=[])},i.error=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return r("error",s,t)},i.warn=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return r("warn",s,t)},i.debug=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return r("debug",s,t)},i}("VIDEOJS"),g=v.createLogger,u=t(function(e){function t(){return e.exports=t=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},t.apply(this,arguments)}e.exports=t}),_=Object.prototype.toString,y=function(e){return T(e)?Object.keys(e):[]};function m(t,n){y(t).forEach(function(e){return n(t[e],e)})}function b(n){for(var e=arguments.length,t=new Array(1<e?e-1:0),i=1;i<e;i++)t[i-1]=arguments[i];return Object.assign?u.apply(void 0,[n].concat(t)):(t.forEach(function(e){e&&m(e,function(e,t){n[t]=e})}),n)}function T(e){return!!e&&"object"==typeof e}function k(e){return T(e)&&"[object Object]"===_.call(e)&&e.constructor===Object}function C(e,t){if(!e||!t)return"";if("function"!=typeof d.getComputedStyle)return"";var n=d.getComputedStyle(e);return n?n.getPropertyValue(t)||n[t]:""}var E,S,w,x,P=d.navigator&&d.navigator.userAgent||"",j=/AppleWebKit\/([\d.]+)/i.exec(P),A=j?parseFloat(j.pop()):null,I=/iPod/i.test(P),L=(E=P.match(/OS (\d+)_/i))&&E[1]?E[1]:null,N=/Android/i.test(P),O=function(){var e=P.match(/Android (\d+)(?:\.(\d+))?(?:\.(\d+))*/i);if(!e)return null;var t=e[1]&&parseFloat(e[1]),n=e[2]&&parseFloat(e[2]);return t&&n?parseFloat(e[1]+"."+e[2]):t||null}(),M=N&&O<5&&A<537,D=/Firefox/i.test(P),F=/Edg/i.test(P),R=!F&&(/Chrome/i.test(P)||/CriOS/i.test(P)),B=(S=P.match(/(Chrome|CriOS)\/(\d+)/))&&S[2]?parseFloat(S[2]):null,H=(w=/MSIE\s(\d+)\.\d/.exec(P),!(x=w&&parseFloat(w[1]))&&/Trident\/7.0/i.test(P)&&/rv:11.0/.test(P)&&(x=11),x),V=/Safari/i.test(P)&&!R&&!N&&!F,K=/Windows/i.test(P),z=Boolean(Q()&&("ontouchstart"in d||d.navigator.maxTouchPoints||d.DocumentTouch&&d.document instanceof d.DocumentTouch)),W=/iPad/i.test(P)||V&&z&&!/iPhone/i.test(P),U=/iPhone/i.test(P)&&!W,q=U||W||I,X=(V||q)&&!R,G=Object.freeze({__proto__:null,IS_IPOD:I,IOS_VERSION:L,IS_ANDROID:N,ANDROID_VERSION:O,IS_NATIVE_ANDROID:M,IS_FIREFOX:D,IS_EDGE:F,IS_CHROME:R,CHROME_VERSION:B,IE_VERSION:H,IS_SAFARI:V,IS_WINDOWS:K,TOUCH_ENABLED:z,IS_IPAD:W,IS_IPHONE:U,IS_IOS:q,IS_ANY_SAFARI:X});function $(e){return"string"==typeof e&&Boolean(e.trim())}function Y(e){if(0<=e.indexOf(" "))throw new Error("class has illegal whitespace characters")}function Q(){return p===d.document}function J(e){return T(e)&&1===e.nodeType}function Z(){try{return d.parent!==d.self}catch(e){return!0}}function ee(i){return function(e,t){if(!$(e))return p[i](null);$(t)&&(t=p.querySelector(t));var n=J(t)?t:p;return n[i]&&n[i](e)}}function te(e,n,t,i){void 0===e&&(e="div"),void 0===n&&(n={}),void 0===t&&(t={});var r=p.createElement(e);return Object.getOwnPropertyNames(n).forEach(function(e){var t=n[e];-1!==e.indexOf("aria-")||"role"===e||"type"===e?(v.warn("Setting attributes in the second argument of createEl()\nhas been deprecated. Use the third argument instead.\ncreateEl(type, properties, attributes). Attempting to set "+e+" to "+t+"."),r.setAttribute(e,t)):"textContent"===e?ne(r,t):r[e]===t&&"tabIndex"!==e||(r[e]=t)}),Object.getOwnPropertyNames(t).forEach(function(e){r.setAttribute(e,t[e])}),i&&Te(r,i),r}function ne(e,t){return"undefined"==typeof e.textContent?e.innerText=t:e.textContent=t,e}function ie(e,t){t.firstChild?t.insertBefore(e,t.firstChild):t.appendChild(e)}function re(e,t){return Y(t),e.classList?e.classList.contains(t):function(e){return new RegExp("(^|\\s)"+e+"($|\\s)")}(t).test(e.className)}function se(e,t){return e.classList?e.classList.add(t):re(e,t)||(e.className=(e.className+" "+t).trim()),e}function oe(e,t){return e.classList?e.classList.remove(t):(Y(t),e.className=e.className.split(/\s+/).filter(function(e){return e!==t}).join(" ")),e}function ae(e,t,n){var i=re(e,t);if("function"==typeof n&&(n=n(e,t)),"boolean"!=typeof n&&(n=!i),n!==i)return n?se(e,t):oe(e,t),e}function le(n,i){Object.getOwnPropertyNames(i).forEach(function(e){var t=i[e];null===t||"undefined"==typeof t||!1===t?n.removeAttribute(e):n.setAttribute(e,!0===t?"":t)})}function ce(e){var t={},n=",autoplay,controls,playsinline,loop,muted,default,defaultMuted,";if(e&&e.attributes&&0<e.attributes.length)for(var i=e.attributes,r=i.length-1;0<=r;r--){var s=i[r].name,o=i[r].value;"boolean"!=typeof e[s]&&-1===n.indexOf(","+s+",")||(o=null!==o),t[s]=o}return t}function ue(e,t){return e.getAttribute(t)}function he(e,t,n){e.setAttribute(t,n)}function de(e,t){e.removeAttribute(t)}function pe(){p.body.focus(),p.onselectstart=function(){return!1}}function fe(){p.onselectstart=function(){return!0}}function ve(e){if(e&&e.getBoundingClientRect&&e.parentNode){var t=e.getBoundingClientRect(),n={};return["bottom","height","left","right","top","width"].forEach(function(e){void 0!==t[e]&&(n[e]=t[e])}),n.height||(n.height=parseFloat(C(e,"height"))),n.width||(n.width=parseFloat(C(e,"width"))),n}}function ge(e){if(!e||e&&!e.offsetParent)return{left:0,top:0,width:0,height:0};for(var t=e.offsetWidth,n=e.offsetHeight,i=0,r=0;e.offsetParent&&e!==p[f.fullscreenElement];)i+=e.offsetLeft,r+=e.offsetTop,e=e.offsetParent;return{left:i,top:r,width:t,height:n}}function _e(e,t){var n={x:0,y:0};if(q)for(var i=e;i&&"html"!==i.nodeName.toLowerCase();){var r=C(i,"transform");if(/^matrix/.test(r)){var s=r.slice(7,-1).split(/,\s/).map(Number);n.x+=s[4],n.y+=s[5]}else if(/^matrix3d/.test(r)){var o=r.slice(9,-1).split(/,\s/).map(Number);n.x+=o[12],n.y+=o[13]}i=i.parentNode}var a={},l=ge(t.target),c=ge(e),u=c.width,h=c.height,d=t.offsetY-(c.top-l.top),p=t.offsetX-(c.left-l.left);return t.changedTouches&&(p=t.changedTouches[0].pageX-c.left,d=t.changedTouches[0].pageY+c.top,q&&(p-=n.x,d-=n.y)),a.y=1-Math.max(0,Math.min(1,d/h)),a.x=Math.max(0,Math.min(1,p/u)),a}function ye(e){return T(e)&&3===e.nodeType}function me(e){for(;e.firstChild;)e.removeChild(e.firstChild);return e}function be(e){return"function"==typeof e&&(e=e()),(Array.isArray(e)?e:[e]).map(function(e){return"function"==typeof e&&(e=e()),J(e)||ye(e)?e:"string"==typeof e&&/\S/.test(e)?p.createTextNode(e):void 0}).filter(function(e){return e})}function Te(t,e){return be(e).forEach(function(e){return t.appendChild(e)}),t}function ke(e,t){return Te(me(e),t)}function Ce(e){return void 0===e.button&&void 0===e.buttons||(0===e.button&&void 0===e.buttons||("mouseup"===e.type&&0===e.button&&0===e.buttons||0===e.button&&1===e.buttons))}var Ee,Se=ee("querySelector"),we=ee("querySelectorAll"),xe=Object.freeze({__proto__:null,isReal:Q,isEl:J,isInFrame:Z,createEl:te,textContent:ne,prependTo:ie,hasClass:re,addClass:se,removeClass:oe,toggleClass:ae,setAttributes:le,getAttributes:ce,getAttribute:ue,setAttribute:he,removeAttribute:de,blockTextSelection:pe,unblockTextSelection:fe,getBoundingClientRect:ve,findPosition:ge,getPointerPosition:_e,isTextNode:ye,emptyEl:me,normalizeContent:be,appendContent:Te,insertContent:ke,isSingleLeftClick:Ce,$:Se,$$:we}),Pe=!1,je=function(){if(Q()&&!1!==Ee.options.autoSetup){var e=Array.prototype.slice.call(p.getElementsByTagName("video")),t=Array.prototype.slice.call(p.getElementsByTagName("audio")),n=Array.prototype.slice.call(p.getElementsByTagName("video-js")),i=e.concat(t,n);if(i&&0<i.length)for(var r=0,s=i.length;r<s;r++){var o=i[r];if(!o||!o.getAttribute){Ae(1);break}void 0===o.player&&null!==o.getAttribute("data-setup")&&Ee(o)}else Pe||Ae(1)}};function Ae(e,t){t&&(Ee=t),d.setTimeout(je,e)}function Ie(){Pe=!0,d.removeEventListener("load",Ie)}Q()&&("complete"===p.readyState?Ie():d.addEventListener("load",Ie));function Le(e){var t=p.createElement("style");return t.className=e,t}function Ne(e,t){e.styleSheet?e.styleSheet.cssText=t:e.textContent=t}var Oe,Me=3;function De(){return Me++}d.WeakMap||(Oe=function(){function e(){this.vdata="vdata"+Math.floor(d.performance&&d.performance.now()||Date.now()),this.data={}}var t=e.prototype;return t.set=function(e,t){var n=e[this.vdata]||De();return e[this.vdata]||(e[this.vdata]=n),this.data[n]=t,this},t.get=function(e){var t=e[this.vdata];if(t)return this.data[t];v("We have no data for this element",e)},t.has=function(e){return e[this.vdata]in this.data},t.delete=function(e){var t=e[this.vdata];t&&(delete this.data[t],delete e[this.vdata])},e}());var Fe,Re=d.WeakMap?new WeakMap:new Oe;function Be(e,t){if(Re.has(e)){var n=Re.get(e);0===n.handlers[t].length&&(delete n.handlers[t],e.removeEventListener?e.removeEventListener(t,n.dispatcher,!1):e.detachEvent&&e.detachEvent("on"+t,n.dispatcher)),Object.getOwnPropertyNames(n.handlers).length<=0&&(delete n.handlers,delete n.dispatcher,delete n.disabled),0===Object.getOwnPropertyNames(n).length&&Re.delete(e)}}function He(t,n,e,i){e.forEach(function(e){t(n,e,i)})}function Ve(e){if(e.fixed_)return e;function t(){return!0}function n(){return!1}if(!e||!e.isPropagationStopped){var i=e||d.event;for(var r in e={},i)"layerX"!==r&&"layerY"!==r&&"keyLocation"!==r&&"webkitMovementX"!==r&&"webkitMovementY"!==r&&("returnValue"===r&&i.preventDefault||(e[r]=i[r]));if(e.target||(e.target=e.srcElement||p),e.relatedTarget||(e.relatedTarget=e.fromElement===e.target?e.toElement:e.fromElement),e.preventDefault=function(){i.preventDefault&&i.preventDefault(),e.returnValue=!1,i.returnValue=!1,e.defaultPrevented=!0},e.defaultPrevented=!1,e.stopPropagation=function(){i.stopPropagation&&i.stopPropagation(),e.cancelBubble=!0,i.cancelBubble=!0,e.isPropagationStopped=t},e.isPropagationStopped=n,e.stopImmediatePropagation=function(){i.stopImmediatePropagation&&i.stopImmediatePropagation(),e.isImmediatePropagationStopped=t,e.stopPropagation()},e.isImmediatePropagationStopped=n,null!==e.clientX&&void 0!==e.clientX){var s=p.documentElement,o=p.body;e.pageX=e.clientX+(s&&s.scrollLeft||o&&o.scrollLeft||0)-(s&&s.clientLeft||o&&o.clientLeft||0),e.pageY=e.clientY+(s&&s.scrollTop||o&&o.scrollTop||0)-(s&&s.clientTop||o&&o.clientTop||0)}e.which=e.charCode||e.keyCode,null!==e.button&&void 0!==e.button&&(e.button=1&e.button?0:4&e.button?1:2&e.button?2:0)}return e.fixed_=!0,e}var Ke=function(){if("boolean"!=typeof Fe){Fe=!1;try{var e=Object.defineProperty({},"passive",{get:function(){Fe=!0}});d.addEventListener("test",null,e),d.removeEventListener("test",null,e)}catch(e){}}return Fe},ze=["touchstart","touchmove"];function We(o,e,t){if(Array.isArray(e))return He(We,o,e,t);Re.has(o)||Re.set(o,{});var a=Re.get(o);if(a.handlers||(a.handlers={}),a.handlers[e]||(a.handlers[e]=[]),t.guid||(t.guid=De()),a.handlers[e].push(t),a.dispatcher||(a.disabled=!1,a.dispatcher=function(e,t){if(!a.disabled){e=Ve(e);var n=a.handlers[e.type];if(n)for(var i=n.slice(0),r=0,s=i.length;r<s&&!e.isImmediatePropagationStopped();r++)try{i[r].call(o,e,t)}catch(e){v.error(e)}}}),1===a.handlers[e].length)if(o.addEventListener){var n=!1;Ke()&&-1<ze.indexOf(e)&&(n={passive:!0}),o.addEventListener(e,a.dispatcher,n)}else o.attachEvent&&o.attachEvent("on"+e,a.dispatcher)}function Ue(e,t,n){if(Re.has(e)){var i=Re.get(e);if(i.handlers){if(Array.isArray(t))return He(Ue,e,t,n);var r=function(e,t){i.handlers[t]=[],Be(e,t)};if(void 0!==t){var s=i.handlers[t];if(s)if(n){if(n.guid)for(var o=0;o<s.length;o++)s[o].guid===n.guid&&s.splice(o--,1);Be(e,t)}else r(e,t)}else for(var a in i.handlers)Object.prototype.hasOwnProperty.call(i.handlers||{},a)&&r(e,a)}}}function qe(e,t,n){var i=Re.has(e)?Re.get(e):{},r=e.parentNode||e.ownerDocument;if("string"==typeof t?t={type:t,target:e}:t.target||(t.target=e),t=Ve(t),i.dispatcher&&i.dispatcher.call(e,t,n),r&&!t.isPropagationStopped()&&!0===t.bubbles)qe.call(null,r,t,n);else if(!r&&!t.defaultPrevented&&t.target&&t.target[t.type]){Re.has(t.target)||Re.set(t.target,{});var s=Re.get(t.target);t.target[t.type]&&(s.disabled=!0,"function"==typeof t.target[t.type]&&t.target[t.type](),s.disabled=!1)}return!t.defaultPrevented}function Xe(e,t,n){if(Array.isArray(t))return He(Xe,e,t,n);function i(){Ue(e,t,i),n.apply(this,arguments)}i.guid=n.guid=n.guid||De(),We(e,t,i)}function Ge(e,t,n){function i(){Ue(e,t,i),n.apply(this,arguments)}i.guid=n.guid=n.guid||De(),We(e,t,i)}function $e(e,t,n){t.guid||(t.guid=De());var i=t.bind(e);return i.guid=n?n+"_"+t.guid:t.guid,i}function Ye(t,n){var i=d.performance.now();return function(){var e=d.performance.now();n<=e-i&&(t.apply(void 0,arguments),i=e)}}function Qe(){}var Je,Ze=Object.freeze({__proto__:null,fixEvent:Ve,on:We,off:Ue,trigger:qe,one:Xe,any:Ge});Qe.prototype.allowedEvents_={},Qe.prototype.addEventListener=Qe.prototype.on=function(e,t){var n=this.addEventListener;this.addEventListener=function(){},We(this,e,t),this.addEventListener=n},Qe.prototype.removeEventListener=Qe.prototype.off=function(e,t){Ue(this,e,t)},Qe.prototype.one=function(e,t){var n=this.addEventListener;this.addEventListener=function(){},Xe(this,e,t),this.addEventListener=n},Qe.prototype.any=function(e,t){var n=this.addEventListener;this.addEventListener=function(){},Ge(this,e,t),this.addEventListener=n},Qe.prototype.dispatchEvent=Qe.prototype.trigger=function(e){var t=e.type||e;"string"==typeof e&&(e={type:t}),e=Ve(e),this.allowedEvents_[t]&&this["on"+t]&&this["on"+t](e),qe(this,e)},Qe.prototype.queueTrigger=function(e){var t=this;Je=Je||new Map;var n=e.type||e,i=Je.get(this);i||(i=new Map,Je.set(this,i));var r=i.get(n);i.delete(n),d.clearTimeout(r);var s=d.setTimeout(function(){0===i.size&&(i=null,Je.delete(t)),t.trigger(e)},0);i.set(n,s)};function et(e){return"function"==typeof e.name?e.name():"string"==typeof e.name?e.name:e.name_?e.name_:e.constructor&&e.constructor.name?e.constructor.name:typeof e}function tt(e){return"string"==typeof e&&/\S/.test(e)||Array.isArray(e)&&!!e.length}function nt(e,t,n){if(!e||!e.nodeName&&!at(e))throw new Error("Invalid target for "+et(t)+"#"+n+"; must be a DOM node or evented object.")}function it(e,t,n){if(!tt(e))throw new Error("Invalid event type for "+et(t)+"#"+n+"; must be a non-empty string or array.")}function rt(e,t,n){if("function"!=typeof e)throw new Error("Invalid listener for "+et(t)+"#"+n+"; must be a function.")}function st(e,t,n){var i,r,s,o=t.length<3||t[0]===e||t[0]===e.eventBusEl_;return s=o?(i=e.eventBusEl_,3<=t.length&&t.shift(),r=t[0],t[1]):(i=t[0],r=t[1],t[2]),nt(i,e,n),it(r,e,n),rt(s,e,n),{isTargetingSelf:o,target:i,type:r,listener:s=$e(e,s)}}function ot(e,t,n,i){nt(e,e,t),e.nodeName?Ze[t](e,n,i):e[t](n,i)}var at=function(t){return t instanceof Qe||!!t.eventBusEl_&&["on","one","off","trigger"].every(function(e){return"function"==typeof t[e]})},lt={on:function(){for(var e=this,t=arguments.length,n=new Array(t),i=0;i<t;i++)n[i]=arguments[i];var r=st(this,n,"on"),s=r.isTargetingSelf,o=r.target,a=r.type,l=r.listener;if(ot(o,"on",a,l),!s){var c=function(){return e.off(o,a,l)};c.guid=l.guid;var u=function(){return e.off("dispose",c)};u.guid=l.guid,ot(this,"on","dispose",c),ot(o,"on","dispose",u)}},one:function(){for(var r=this,e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var i=st(this,t,"one"),s=i.isTargetingSelf,o=i.target,a=i.type,l=i.listener;if(s)ot(o,"one",a,l);else{var c=function e(){r.off(o,a,e);for(var t=arguments.length,n=new Array(t),i=0;i<t;i++)n[i]=arguments[i];l.apply(null,n)};c.guid=l.guid,ot(o,"one",a,c)}},any:function(){for(var r=this,e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var i=st(this,t,"any"),s=i.isTargetingSelf,o=i.target,a=i.type,l=i.listener;if(s)ot(o,"any",a,l);else{var c=function e(){r.off(o,a,e);for(var t=arguments.length,n=new Array(t),i=0;i<t;i++)n[i]=arguments[i];l.apply(null,n)};c.guid=l.guid,ot(o,"any",a,c)}},off:function(e,t,n){if(!e||tt(e))Ue(this.eventBusEl_,e,t);else{var i=e,r=t;nt(i,this,"off"),it(r,this,"off"),rt(n,this,"off"),n=$e(this,n),this.off("dispose",n),i.nodeName?(Ue(i,r,n),Ue(i,"dispose",n)):at(i)&&(i.off(r,n),i.off("dispose",n))}},trigger:function(e,t){nt(this.eventBusEl_,this,"trigger");var n=e&&"string"!=typeof e?e.type:e;if(!tt(n)){var i="Invalid event type for "+et(this)+"#trigger; must be a non-empty string or object with a type key that has a non-empty value.";if(!e)throw new Error(i);(this.log||v).error(i)}return qe(this.eventBusEl_,e,t)}};function ct(e,t){void 0===t&&(t={});var n=t.eventBusKey;if(n){if(!e[n].nodeName)throw new Error('The eventBusKey "'+n+'" does not refer to an element.');e.eventBusEl_=e[n]}else e.eventBusEl_=te("span",{className:"vjs-event-bus"});return b(e,lt),e.eventedCallbacks&&e.eventedCallbacks.forEach(function(e){e()}),e.on("dispose",function(){e.off(),d.setTimeout(function(){e.eventBusEl_=null},0)}),e}var ut={state:{},setState:function(e){var n,i=this;return"function"==typeof e&&(e=e()),m(e,function(e,t){i.state[t]!==e&&((n=n||{})[t]={from:i.state[t],to:e}),i.state[t]=e}),n&&at(this)&&this.trigger({changes:n,type:"statechanged"}),n}};function ht(e,t){return b(e,ut),e.state=b({},e.state,t),"function"==typeof e.handleStateChanged&&at(e)&&e.on("statechanged",e.handleStateChanged),e}function dt(e){return"string"!=typeof e?e:e.replace(/./,function(e){return e.toLowerCase()})}function pt(e){return"string"!=typeof e?e:e.replace(/./,function(e){return e.toUpperCase()})}function ft(){for(var n={},e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];return t.forEach(function(e){e&&m(e,function(e,t){k(e)?(k(n[t])||(n[t]={}),n[t]=ft(n[t],e)):n[t]=e})}),n}var vt=function(){function e(){this.map_={}}var t=e.prototype;return t.has=function(e){return e in this.map_},t.delete=function(e){var t=this.has(e);return delete this.map_[e],t},t.set=function(e,t){return this.map_[e]=t,this},t.forEach=function(e,t){for(var n in this.map_)e.call(t,this.map_[n],n,this)},e}(),gt=d.Map?d.Map:vt,_t=function(){function e(){this.set_={}}var t=e.prototype;return t.has=function(e){return e in this.set_},t.delete=function(e){var t=this.has(e);return delete this.set_[e],t},t.add=function(e){return this.set_[e]=1,this},t.forEach=function(e,t){for(var n in this.set_)e.call(t,n,n,this)},e}(),yt=d.Set?d.Set:_t,mt=function(){function c(e,t,n){if(!e&&this.play?this.player_=e=this:this.player_=e,this.isDisposed_=!1,this.parentComponent_=null,this.options_=ft({},this.options_),t=this.options_=ft(this.options_,t),this.id_=t.id||t.el&&t.el.id,!this.id_){var i=e&&e.id&&e.id()||"no_player";this.id_=i+"_component_"+De()}this.name_=t.name||null,t.el?this.el_=t.el:!1!==t.createEl&&(this.el_=this.createEl()),!1!==t.evented&&(ct(this,{eventBusKey:this.el_?"el_":null}),this.handleLanguagechange=this.handleLanguagechange.bind(this),this.on(this.player_,"languagechange",this.handleLanguagechange)),ht(this,this.constructor.defaultState),this.children_=[],this.childIndex_={},this.childNameIndex_={},this.setTimeoutIds_=new yt,this.setIntervalIds_=new yt,this.rafIds_=new yt,this.namedRafs_=new gt,(this.clearingTimersOnDispose_=!1)!==t.initChildren&&this.initChildren(),this.ready(n),!1!==t.reportTouchActivity&&this.enableTouchActivity()}var e=c.prototype;return e.dispose=function(){if(!this.isDisposed_){if(this.readyQueue_&&(this.readyQueue_.length=0),this.trigger({type:"dispose",bubbles:!1}),this.isDisposed_=!0,this.children_)for(var e=this.children_.length-1;0<=e;e--)this.children_[e].dispose&&this.children_[e].dispose();this.children_=null,this.childIndex_=null,this.childNameIndex_=null,this.parentComponent_=null,this.el_&&(this.el_.parentNode&&this.el_.parentNode.removeChild(this.el_),Re.has(this.el_)&&Re.delete(this.el_),this.el_=null),this.player_=null}},e.isDisposed=function(){return Boolean(this.isDisposed_)},e.player=function(){return this.player_},e.options=function(e){return e&&(this.options_=ft(this.options_,e)),this.options_},e.el=function(){return this.el_},e.createEl=function(e,t,n){return te(e,t,n)},e.localize=function(e,r,t){void 0===t&&(t=e);var n=this.player_.language&&this.player_.language(),i=this.player_.languages&&this.player_.languages(),s=i&&i[n],o=n&&n.split("-")[0],a=i&&i[o],l=t;return s&&s[e]?l=s[e]:a&&a[e]&&(l=a[e]),r&&(l=l.replace(/\{(\d+)\}/g,function(e,t){var n=r[t-1],i=n;return"undefined"==typeof n&&(i=e),i})),l},e.handleLanguagechange=function(){},e.contentEl=function(){return this.contentEl_||this.el_},e.id=function(){return this.id_},e.name=function(){return this.name_},e.children=function(){return this.children_},e.getChildById=function(e){return this.childIndex_[e]},e.getChild=function(e){if(e)return this.childNameIndex_[e]},e.getDescendant=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];t=t.reduce(function(e,t){return e.concat(t)},[]);for(var i=this,r=0;r<t.length;r++)if(!(i=i.getChild(t[r]))||!i.getChild)return;return i},e.addChild=function(e,t,n){var i,r;if(void 0===t&&(t={}),void 0===n&&(n=this.children_.length),"string"==typeof e){r=pt(e);var s=t.componentClass||r;t.name=r;var o=c.getComponent(s);if(!o)throw new Error("Component "+s+" does not exist");if("function"!=typeof o)return null;i=new o(this.player_||this,t)}else i=e;if(i.parentComponent_&&i.parentComponent_.removeChild(i),this.children_.splice(n,0,i),i.parentComponent_=this,"function"==typeof i.id&&(this.childIndex_[i.id()]=i),(r=r||i.name&&pt(i.name()))&&(this.childNameIndex_[r]=i,this.childNameIndex_[dt(r)]=i),"function"==typeof i.el&&i.el()){var a=null;this.children_[n+1]&&(this.children_[n+1].el_?a=this.children_[n+1].el_:J(this.children_[n+1])&&(a=this.children_[n+1])),this.contentEl().insertBefore(i.el(),a)}return i},e.removeChild=function(e){if("string"==typeof e&&(e=this.getChild(e)),e&&this.children_){for(var t=!1,n=this.children_.length-1;0<=n;n--)if(this.children_[n]===e){t=!0,this.children_.splice(n,1);break}if(t){e.parentComponent_=null,this.childIndex_[e.id()]=null,this.childNameIndex_[pt(e.name())]=null,this.childNameIndex_[dt(e.name())]=null;var i=e.el();i&&i.parentNode===this.contentEl()&&this.contentEl().removeChild(e.el())}}},e.initChildren=function(){var r=this,i=this.options_.children;if(i){var e,s=this.options_,n=c.getComponent("Tech");(e=Array.isArray(i)?i:Object.keys(i)).concat(Object.keys(this.options_).filter(function(t){return!e.some(function(e){return"string"==typeof e?t===e:t===e.name})})).map(function(e){var t,n;return n="string"==typeof e?i[t=e]||r.options_[t]||{}:(t=e.name,e),{name:t,opts:n}}).filter(function(e){var t=c.getComponent(e.opts.componentClass||pt(e.name));return t&&!n.isTech(t)}).forEach(function(e){var t=e.name,n=e.opts;if(void 0!==s[t]&&(n=s[t]),!1!==n){!0===n&&(n={}),n.playerOptions=r.options_.playerOptions;var i=r.addChild(t,n);i&&(r[t]=i)}})}},e.buildCSSClass=function(){return""},e.ready=function(e,t){if(void 0===t&&(t=!1),e)return this.isReady_?void(t?e.call(this):this.setTimeout(e,1)):(this.readyQueue_=this.readyQueue_||[],void this.readyQueue_.push(e))},e.triggerReady=function(){this.isReady_=!0,this.setTimeout(function(){var e=this.readyQueue_;this.readyQueue_=[],e&&0<e.length&&e.forEach(function(e){e.call(this)},this),this.trigger("ready")},1)},e.$=function(e,t){return Se(e,t||this.contentEl())},e.$$=function(e,t){return we(e,t||this.contentEl())},e.hasClass=function(e){return re(this.el_,e)},e.addClass=function(e){se(this.el_,e)},e.removeClass=function(e){oe(this.el_,e)},e.toggleClass=function(e,t){ae(this.el_,e,t)},e.show=function(){this.removeClass("vjs-hidden")},e.hide=function(){this.addClass("vjs-hidden")},e.lockShowing=function(){this.addClass("vjs-lock-showing")},e.unlockShowing=function(){this.removeClass("vjs-lock-showing")},e.getAttribute=function(e){return ue(this.el_,e)},e.setAttribute=function(e,t){he(this.el_,e,t)},e.removeAttribute=function(e){de(this.el_,e)},e.width=function(e,t){return this.dimension("width",e,t)},e.height=function(e,t){return this.dimension("height",e,t)},e.dimensions=function(e,t){this.width(e,!0),this.height(t)},e.dimension=function(e,t,n){if(void 0!==t)return null!==t&&t==t||(t=0),-1!==(""+t).indexOf("%")||-1!==(""+t).indexOf("px")?this.el_.style[e]=t:this.el_.style[e]="auto"===t?"":t+"px",void(n||this.trigger("componentresize"));if(!this.el_)return 0;var i=this.el_.style[e],r=i.indexOf("px");return-1!==r?parseInt(i.slice(0,r),10):parseInt(this.el_["offset"+pt(e)],10)},e.currentDimension=function(e){var t=0;if("width"!==e&&"height"!==e)throw new Error("currentDimension only accepts width or height value");if(t=C(this.el_,e),0===(t=parseFloat(t))||isNaN(t)){var n="offset"+pt(e);t=this.el_[n]}return t},e.currentDimensions=function(){return{width:this.currentDimension("width"),height:this.currentDimension("height")}},e.currentWidth=function(){return this.currentDimension("width")},e.currentHeight=function(){return this.currentDimension("height")},e.focus=function(){this.el_.focus()},e.blur=function(){this.el_.blur()},e.handleKeyDown=function(e){this.player_&&(e.stopPropagation(),this.player_.handleKeyDown(e))},e.handleKeyPress=function(e){this.handleKeyDown(e)},e.emitTapEvents=function(){var i,t=0,r=null;this.on("touchstart",function(e){1===e.touches.length&&(r={pageX:e.touches[0].pageX,pageY:e.touches[0].pageY},t=d.performance.now(),i=!0)}),this.on("touchmove",function(e){if(1<e.touches.length)i=!1;else if(r){var t=e.touches[0].pageX-r.pageX,n=e.touches[0].pageY-r.pageY;10<Math.sqrt(t*t+n*n)&&(i=!1)}});function e(){i=!1}this.on("touchleave",e),this.on("touchcancel",e),this.on("touchend",function(e){!(r=null)===i&&d.performance.now()-t<200&&(e.preventDefault(),this.trigger("tap"))})},e.enableTouchActivity=function(){if(this.player()&&this.player().reportUserActivity){var t,n=$e(this.player(),this.player().reportUserActivity);this.on("touchstart",function(){n(),this.clearInterval(t),t=this.setInterval(n,250)});var e=function(e){n(),this.clearInterval(t)};this.on("touchmove",n),this.on("touchend",e),this.on("touchcancel",e)}},e.setTimeout=function(e,t){var n,i=this;return e=$e(this,e),this.clearTimersOnDispose_(),n=d.setTimeout(function(){i.setTimeoutIds_.has(n)&&i.setTimeoutIds_.delete(n),e()},t),this.setTimeoutIds_.add(n),n},e.clearTimeout=function(e){return this.setTimeoutIds_.has(e)&&(this.setTimeoutIds_.delete(e),d.clearTimeout(e)),e},e.setInterval=function(e,t){e=$e(this,e),this.clearTimersOnDispose_();var n=d.setInterval(e,t);return this.setIntervalIds_.add(n),n},e.clearInterval=function(e){return this.setIntervalIds_.has(e)&&(this.setIntervalIds_.delete(e),d.clearInterval(e)),e},e.requestAnimationFrame=function(e){var t,n=this;return this.supportsRaf_?(this.clearTimersOnDispose_(),e=$e(this,e),t=d.requestAnimationFrame(function(){n.rafIds_.has(t)&&n.rafIds_.delete(t),e()}),this.rafIds_.add(t),t):this.setTimeout(e,1e3/60)},e.requestNamedAnimationFrame=function(e,t){var n=this;if(!this.namedRafs_.has(e)){this.clearTimersOnDispose_(),t=$e(this,t);var i=this.requestAnimationFrame(function(){t(),n.namedRafs_.has(e)&&n.namedRafs_.delete(e)});return this.namedRafs_.set(e,i),e}},e.cancelNamedAnimationFrame=function(e){this.namedRafs_.has(e)&&(this.cancelAnimationFrame(this.namedRafs_.get(e)),this.namedRafs_.delete(e))},e.cancelAnimationFrame=function(e){return this.supportsRaf_?(this.rafIds_.has(e)&&(this.rafIds_.delete(e),d.cancelAnimationFrame(e)),e):this.clearTimeout(e)},e.clearTimersOnDispose_=function(){var i=this;this.clearingTimersOnDispose_||(this.clearingTimersOnDispose_=!0,this.one("dispose",function(){[["namedRafs_","cancelNamedAnimationFrame"],["rafIds_","cancelAnimationFrame"],["setTimeoutIds_","clearTimeout"],["setIntervalIds_","clearInterval"]].forEach(function(e){var t=e[0],n=e[1];i[t].forEach(function(e,t){return i[n](t)})}),i.clearingTimersOnDispose_=!1}))},c.registerComponent=function(e,t){if("string"!=typeof e||!e)throw new Error('Illegal component name, "'+e+'"; must be a non-empty string.');var n,i=c.getComponent("Tech"),r=i&&i.isTech(t),s=c===t||c.prototype.isPrototypeOf(t.prototype);if(r||!s)throw n=r?"techs must be registered using Tech.registerTech()":"must be a Component subclass",new Error('Illegal component, "'+e+'"; '+n+".");e=pt(e),c.components_||(c.components_={});var o=c.getComponent("Player");if("Player"===e&&o&&o.players){var a=o.players,l=Object.keys(a);if(a&&0<l.length&&l.map(function(e){return a[e]}).every(Boolean))throw new Error("Can not register Player component after player has been created.")}return c.components_[e]=t,c.components_[dt(e)]=t},c.getComponent=function(e){if(e&&c.components_)return c.components_[e]},c}();mt.prototype.supportsRaf_="function"==typeof d.requestAnimationFrame&&"function"==typeof d.cancelAnimationFrame,mt.registerComponent("Component",mt);var bt=function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e};t(function(t){function n(e){return"function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?t.exports=n=function(e){return typeof e}:t.exports=n=function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}t.exports=n}),t(function(t){function n(e){return t.exports=n=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},n(e)}t.exports=n});var Tt=function(e,t){e.prototype=Object.create(t.prototype),(e.prototype.constructor=e).__proto__=t};function kt(e,t,n,i){return function(e,t,n){if("number"!=typeof t||t<0||n<t)throw new Error("Failed to execute '"+e+"' on 'TimeRanges': The index provided ("+t+") is non-numeric or out of bounds (0-"+n+").")}(e,i,n.length-1),n[i][t]}function Ct(e){return void 0===e||0===e.length?{length:0,start:function(){throw new Error("This TimeRanges object is empty")},end:function(){throw new Error("This TimeRanges object is empty")}}:{length:e.length,start:kt.bind(null,"start",0,e),end:kt.bind(null,"end",1,e)}}function Et(e,t){return Array.isArray(e)?Ct(e):void 0===e||void 0===t?Ct():Ct([[e,t]])}function St(e,t){var n,i,r=0;if(!t)return 0;e&&e.length||(e=Et(0,0));for(var s=0;s<e.length;s++)n=e.start(s),t<(i=e.end(s))&&(i=t),r+=i-n;return r/t}function wt(e){if(e instanceof wt)return e;"number"==typeof e?this.code=e:"string"==typeof e?this.message=e:T(e)&&("number"==typeof e.code&&(this.code=e.code),b(this,e)),this.message||(this.message=wt.defaultMessages[this.code]||"")}wt.prototype.code=0,wt.prototype.message="",wt.prototype.status=null,wt.errorTypes=["MEDIA_ERR_CUSTOM","MEDIA_ERR_ABORTED","MEDIA_ERR_NETWORK","MEDIA_ERR_DECODE","MEDIA_ERR_SRC_NOT_SUPPORTED","MEDIA_ERR_ENCRYPTED"],wt.defaultMessages={1:"You aborted the media playback",2:"A network error caused the media download to fail part-way.",3:"The media playback was aborted due to a corruption problem or because the media used features your browser did not support.",4:"The media could not be loaded, either because the server or network failed or because the format is not supported.",5:"The media is encrypted and we do not have the keys to decrypt it."};for(var xt=0;xt<wt.errorTypes.length;xt++)wt[wt.errorTypes[xt]]=xt,wt.prototype[wt.errorTypes[xt]]=xt;var Pt=function(e,t){var n,i=null;try{n=JSON.parse(e,t)}catch(e){i=e}return[i,n]};function jt(e){return null!=e&&"function"==typeof e.then}function At(e){jt(e)&&e.then(null,function(e){})}function It(i){return["kind","label","language","id","inBandMetadataTrackDispatchType","mode","src"].reduce(function(e,t,n){return i[t]&&(e[t]=i[t]),e},{cues:i.cues&&Array.prototype.map.call(i.cues,function(e){return{startTime:e.startTime,endTime:e.endTime,text:e.text,id:e.id}})})}var Lt=function(e){var t=e.$$("track"),n=Array.prototype.map.call(t,function(e){return e.track});return Array.prototype.map.call(t,function(e){var t=It(e.track);return e.src&&(t.src=e.src),t}).concat(Array.prototype.filter.call(e.textTracks(),function(e){return-1===n.indexOf(e)}).map(It))},Nt=function(e,n){return e.forEach(function(e){var t=n.addRemoteTextTrack(e).track;!e.src&&e.cues&&e.cues.forEach(function(e){return t.addCue(e)})}),n.textTracks()},Ot=t(function(e,t){function n(e){if(e&&"object"==typeof e){var t=e.which||e.keyCode||e.charCode;t&&(e=t)}if("number"==typeof e)return o[e];var n,i=String(e);return(n=r[i.toLowerCase()])?n:(n=s[i.toLowerCase()])||(1===i.length?i.charCodeAt(0):void 0)}n.isEventKey=function(e,t){if(e&&"object"==typeof e){var n=e.which||e.keyCode||e.charCode;if(null==n)return!1;if("string"==typeof t){var i;if(i=r[t.toLowerCase()])return i===n;if(i=s[t.toLowerCase()])return i===n}else if("number"==typeof t)return t===n;return!1}};var r=(t=e.exports=n).code=t.codes={backspace:8,tab:9,enter:13,shift:16,ctrl:17,alt:18,"pause/break":19,"caps lock":20,esc:27,space:32,"page up":33,"page down":34,end:35,home:36,left:37,up:38,right:39,down:40,insert:45,delete:46,command:91,"left command":91,"right command":93,"numpad *":106,"numpad +":107,"numpad -":109,"numpad .":110,"numpad /":111,"num lock":144,"scroll lock":145,"my computer":182,"my calculator":183,";":186,"=":187,",":188,"-":189,".":190,"/":191,"`":192,"[":219,"\\":220,"]":221,"'":222},s=t.aliases={windows:91,"⇧":16,"⌥":18,"⌃":17,"⌘":91,ctl:17,control:17,option:18,pause:19,break:19,caps:20,return:13,escape:27,spc:32,spacebar:32,pgup:33,pgdn:34,ins:45,del:46,cmd:91};for(i=97;i<123;i++)r[String.fromCharCode(i)]=i-32;for(var i=48;i<58;i++)r[i-48]=i;for(i=1;i<13;i++)r["f"+i]=i+111;for(i=0;i<10;i++)r["numpad "+i]=i+96;var o=t.names=t.title={};for(i in r)o[r[i]]=i;for(var a in s)r[a]=s[a]}),Mt=(Ot.code,Ot.codes,Ot.aliases,Ot.names,Ot.title,"vjs-modal-dialog"),Dt=function(i){function e(e,t){var n;return(n=i.call(this,e,t)||this).opened_=n.hasBeenOpened_=n.hasBeenFilled_=!1,n.closeable(!n.options_.uncloseable),n.content(n.options_.content),n.contentEl_=te("div",{className:Mt+"-content"},{role:"document"}),n.descEl_=te("p",{className:Mt+"-description vjs-control-text",id:n.el().getAttribute("aria-describedby")}),ne(n.descEl_,n.description()),n.el_.appendChild(n.descEl_),n.el_.appendChild(n.contentEl_),n}Tt(e,i);var t=e.prototype;return t.createEl=function(){return i.prototype.createEl.call(this,"div",{className:this.buildCSSClass(),tabIndex:-1},{"aria-describedby":this.id()+"_description","aria-hidden":"true","aria-label":this.label(),role:"dialog"})},t.dispose=function(){this.contentEl_=null,this.descEl_=null,this.previouslyActiveEl_=null,i.prototype.dispose.call(this)},t.buildCSSClass=function(){return Mt+" vjs-hidden "+i.prototype.buildCSSClass.call(this)},t.label=function(){return this.localize(this.options_.label||"Modal Window")},t.description=function(){var e=this.options_.description||this.localize("This is a modal window.");return this.closeable()&&(e+=" "+this.localize("This modal can be closed by pressing the Escape key or activating the close button.")),e},t.open=function(){if(!this.opened_){var e=this.player();this.trigger("beforemodalopen"),this.opened_=!0,!this.options_.fillAlways&&(this.hasBeenOpened_||this.hasBeenFilled_)||this.fill(),this.wasPlaying_=!e.paused(),this.options_.pauseOnOpen&&this.wasPlaying_&&e.pause(),this.on("keydown",this.handleKeyDown),this.hadControls_=e.controls(),e.controls(!1),this.show(),this.conditionalFocus_(),this.el().setAttribute("aria-hidden","false"),this.trigger("modalopen"),this.hasBeenOpened_=!0}},t.opened=function(e){return"boolean"==typeof e&&this[e?"open":"close"](),this.opened_},t.close=function(){if(this.opened_){var e=this.player();this.trigger("beforemodalclose"),this.opened_=!1,this.wasPlaying_&&this.options_.pauseOnOpen&&e.play(),this.off("keydown",this.handleKeyDown),this.hadControls_&&e.controls(!0),this.hide(),this.el().setAttribute("aria-hidden","true"),this.trigger("modalclose"),this.conditionalBlur_(),this.options_.temporary&&this.dispose()}},t.closeable=function(e){if("boolean"==typeof e){var t=this.closeable_=!!e,n=this.getChild("closeButton");if(t&&!n){var i=this.contentEl_;this.contentEl_=this.el_,n=this.addChild("closeButton",{controlText:"Close Modal Dialog"}),this.contentEl_=i,this.on(n,"close",this.close)}!t&&n&&(this.off(n,"close",this.close),this.removeChild(n),n.dispose())}return this.closeable_},t.fill=function(){this.fillWith(this.content())},t.fillWith=function(e){var t=this.contentEl(),n=t.parentNode,i=t.nextSibling;this.trigger("beforemodalfill"),this.hasBeenFilled_=!0,n.removeChild(t),this.empty(),ke(t,e),this.trigger("modalfill"),i?n.insertBefore(t,i):n.appendChild(t);var r=this.getChild("closeButton");r&&n.appendChild(r.el_)},t.empty=function(){this.trigger("beforemodalempty"),me(this.contentEl()),this.trigger("modalempty")},t.content=function(e){return"undefined"!=typeof e&&(this.content_=e),this.content_},t.conditionalFocus_=function(){var e=p.activeElement,t=this.player_.el_;this.previouslyActiveEl_=null,!t.contains(e)&&t!==e||(this.previouslyActiveEl_=e,this.focus())},t.conditionalBlur_=function(){this.previouslyActiveEl_&&(this.previouslyActiveEl_.focus(),this.previouslyActiveEl_=null)},t.handleKeyDown=function(e){if(e.stopPropagation(),Ot.isEventKey(e,"Escape")&&this.closeable())return e.preventDefault(),void this.close();if(Ot.isEventKey(e,"Tab")){for(var t,n=this.focusableEls_(),i=this.el_.querySelector(":focus"),r=0;r<n.length;r++)if(i===n[r]){t=r;break}p.activeElement===this.el_&&(t=0),e.shiftKey&&0===t?(n[n.length-1].focus(),e.preventDefault()):e.shiftKey||t!==n.length-1||(n[0].focus(),e.preventDefault())}},t.focusableEls_=function(){var e=this.el_.querySelectorAll("*");return Array.prototype.filter.call(e,function(e){return(e instanceof d.HTMLAnchorElement||e instanceof d.HTMLAreaElement)&&e.hasAttribute("href")||(e instanceof d.HTMLInputElement||e instanceof d.HTMLSelectElement||e instanceof d.HTMLTextAreaElement||e instanceof d.HTMLButtonElement)&&!e.hasAttribute("disabled")||e instanceof d.HTMLIFrameElement||e instanceof d.HTMLObjectElement||e instanceof d.HTMLEmbedElement||e.hasAttribute("tabindex")&&-1!==e.getAttribute("tabindex")||e.hasAttribute("contenteditable")})},e}(mt);Dt.prototype.options_={pauseOnOpen:!0,temporary:!0},mt.registerComponent("ModalDialog",Dt);var Ft=function(i){function e(e){var t;void 0===e&&(e=[]),(t=i.call(this)||this).tracks_=[],Object.defineProperty(bt(t),"length",{get:function(){return this.tracks_.length}});for(var n=0;n<e.length;n++)t.addTrack(e[n]);return t}Tt(e,i);var t=e.prototype;return t.addTrack=function(e){var t=this,n=this.tracks_.length;""+n in this||Object.defineProperty(this,n,{get:function(){return this.tracks_[n]}}),-1===this.tracks_.indexOf(e)&&(this.tracks_.push(e),this.trigger({track:e,type:"addtrack",target:this})),e.labelchange_=function(){t.trigger({track:e,type:"labelchange",target:t})},at(e)&&e.addEventListener("labelchange",e.labelchange_)},t.removeTrack=function(e){for(var t,n=0,i=this.length;n<i;n++)if(this[n]===e){(t=this[n]).off&&t.off(),this.tracks_.splice(n,1);break}t&&this.trigger({track:t,type:"removetrack",target:this})},t.getTrackById=function(e){for(var t=null,n=0,i=this.length;n<i;n++){var r=this[n];if(r.id===e){t=r;break}}return t},e}(Qe);for(var Rt in Ft.prototype.allowedEvents_={change:"change",addtrack:"addtrack",removetrack:"removetrack",labelchange:"labelchange"},Ft.prototype.allowedEvents_)Ft.prototype["on"+Rt]=null;function Bt(e,t){for(var n=0;n<e.length;n++)Object.keys(e[n]).length&&t.id!==e[n].id&&(e[n].enabled=!1)}function Ht(e,t){for(var n=0;n<e.length;n++)Object.keys(e[n]).length&&t.id!==e[n].id&&(e[n].selected=!1)}function Vt(e){var t=["protocol","hostname","port","pathname","search","hash","host"],n=p.createElement("a");n.href=e;var i,r=""===n.host&&"file:"!==n.protocol;r&&((i=p.createElement("div")).innerHTML='<a href="'+e+'"></a>',n=i.firstChild,i.setAttribute("style","display:none; position:absolute;"),p.body.appendChild(i));for(var s={},o=0;o<t.length;o++)s[t[o]]=n[t[o]];return"http:"===s.protocol&&(s.host=s.host.replace(/:80$/,"")),"https:"===s.protocol&&(s.host=s.host.replace(/:443$/,"")),s.protocol||(s.protocol=d.location.protocol),r&&p.body.removeChild(i),s}function Kt(e){if(!e.match(/^https?:\/\//)){var t=p.createElement("div");t.innerHTML='<a href="'+e+'">x</a>',e=t.firstChild.href}return e}function zt(e){if("string"==typeof e){var t=/^(\/?)([\s\S]*?)((?:\.{1,2}|[^\/]+?)(\.([^\.\/\?]+)))(?:[\/]*|[\?].*)$/.exec(e);if(t)return t.pop().toLowerCase()}return""}function Wt(e,t){void 0===t&&(t=d.location);var n=Vt(e);return(":"===n.protocol?t.protocol:n.protocol)+n.host!==t.protocol+t.host}var Ut=function(i){function e(e){var t;void 0===e&&(e=[]);for(var n=e.length-1;0<=n;n--)if(e[n].enabled){Bt(e,e[n]);break}return(t=i.call(this,e)||this).changing_=!1,t}Tt(e,i);var t=e.prototype;return t.addTrack=function(e){var t=this;e.enabled&&Bt(this,e),i.prototype.addTrack.call(this,e),e.addEventListener&&(e.enabledChange_=function(){t.changing_||(t.changing_=!0,Bt(t,e),t.changing_=!1,t.trigger("change"))},e.addEventListener("enabledchange",e.enabledChange_))},t.removeTrack=function(e){i.prototype.removeTrack.call(this,e),e.removeEventListener&&e.enabledChange_&&(e.removeEventListener("enabledchange",e.enabledChange_),e.enabledChange_=null)},e}(Ft),qt=function(i){function e(e){var t;void 0===e&&(e=[]);for(var n=e.length-1;0<=n;n--)if(e[n].selected){Ht(e,e[n]);break}return(t=i.call(this,e)||this).changing_=!1,Object.defineProperty(bt(t),"selectedIndex",{get:function(){for(var e=0;e<this.length;e++)if(this[e].selected)return e;return-1},set:function(){}}),t}Tt(e,i);var t=e.prototype;return t.addTrack=function(e){var t=this;e.selected&&Ht(this,e),i.prototype.addTrack.call(this,e),e.addEventListener&&(e.selectedChange_=function(){t.changing_||(t.changing_=!0,Ht(t,e),t.changing_=!1,t.trigger("change"))},e.addEventListener("selectedchange",e.selectedChange_))},t.removeTrack=function(e){i.prototype.removeTrack.call(this,e),e.removeEventListener&&e.selectedChange_&&(e.removeEventListener("selectedchange",e.selectedChange_),e.selectedChange_=null)},e}(Ft),Xt=function(n){function e(){return n.apply(this,arguments)||this}Tt(e,n);var t=e.prototype;return t.addTrack=function(e){var t=this;n.prototype.addTrack.call(this,e),this.queueChange_||(this.queueChange_=function(){return t.queueTrigger("change")}),this.triggerSelectedlanguagechange||(this.triggerSelectedlanguagechange_=function(){return t.trigger("selectedlanguagechange")}),e.addEventListener("modechange",this.queueChange_);-1===["metadata","chapters"].indexOf(e.kind)&&e.addEventListener("modechange",this.triggerSelectedlanguagechange_)},t.removeTrack=function(e){n.prototype.removeTrack.call(this,e),e.removeEventListener&&(this.queueChange_&&e.removeEventListener("modechange",this.queueChange_),this.selectedlanguagechange_&&e.removeEventListener("modechange",this.triggerSelectedlanguagechange_))},e}(Ft),Gt=function(){function e(e){void 0===e&&(e=[]),this.trackElements_=[],Object.defineProperty(this,"length",{get:function(){return this.trackElements_.length}});for(var t=0,n=e.length;t<n;t++)this.addTrackElement_(e[t])}var t=e.prototype;return t.addTrackElement_=function(e){var t=this.trackElements_.length;""+t in this||Object.defineProperty(this,t,{get:function(){return this.trackElements_[t]}}),-1===this.trackElements_.indexOf(e)&&this.trackElements_.push(e)},t.getTrackElementByTrack_=function(e){for(var t,n=0,i=this.trackElements_.length;n<i;n++)if(e===this.trackElements_[n].track){t=this.trackElements_[n];break}return t},t.removeTrackElement_=function(e){for(var t=0,n=this.trackElements_.length;t<n;t++)if(e===this.trackElements_[t]){this.trackElements_[t].track&&"function"==typeof this.trackElements_[t].track.off&&this.trackElements_[t].track.off(),"function"==typeof this.trackElements_[t].off&&this.trackElements_[t].off(),this.trackElements_.splice(t,1);break}},e}(),$t=function(){function t(e){t.prototype.setCues_.call(this,e),Object.defineProperty(this,"length",{get:function(){return this.length_}})}var e=t.prototype;return e.setCues_=function(e){var t=this.length||0,n=0,i=e.length;this.cues_=e,this.length_=e.length;function r(e){""+e in this||Object.defineProperty(this,""+e,{get:function(){return this.cues_[e]}})}if(t<i)for(n=t;n<i;n++)r.call(this,n)},e.getCueById=function(e){for(var t=null,n=0,i=this.length;n<i;n++){var r=this[n];if(r.id===e){t=r;break}}return t},t}(),Yt={alternative:"alternative",captions:"captions",main:"main",sign:"sign",subtitles:"subtitles",commentary:"commentary"},Qt={alternative:"alternative",descriptions:"descriptions",main:"main","main-desc":"main-desc",translation:"translation",commentary:"commentary"},Jt={subtitles:"subtitles",captions:"captions",descriptions:"descriptions",chapters:"chapters",metadata:"metadata"},Zt={disabled:"disabled",hidden:"hidden",showing:"showing"},en=function(o){function e(e){var t;void 0===e&&(e={}),t=o.call(this)||this;function n(e){Object.defineProperty(bt(t),e,{get:function(){return i[e]},set:function(){}})}var i={id:e.id||"vjs_track_"+De(),kind:e.kind||"",language:e.language||""},r=e.label||"";for(var s in i)n(s);return Object.defineProperty(bt(t),"label",{get:function(){return r},set:function(e){e!==r&&(r=e,this.trigger("labelchange"))}}),t}return Tt(e,o),e}(Qe),tn=Object.freeze({__proto__:null,parseUrl:Vt,getAbsoluteURL:Kt,getFileExtension:zt,isCrossOrigin:Wt}),nn="undefined"!=typeof window?window:"undefined"!=typeof e?e:"undefined"!=typeof self?self:{},rn=function(e){var t=sn.call(e);return"[object Function]"===t||"function"==typeof e&&"[object RegExp]"!==t||"undefined"!=typeof window&&(e===window.setTimeout||e===window.alert||e===window.confirm||e===window.prompt)}
/**
   * @license
   * slighly modified parse-headers 2.0.2 <https://github.com/kesla/parse-headers/>
   * Copyright (c) 2014 David Björklund
   * Available under the MIT license
   * <https://github.com/kesla/parse-headers/blob/master/LICENCE>
   */,sn=Object.prototype.toString;var on=function(e){var r={};return e&&e.trim().split("\n").forEach(function(e){var t=e.indexOf(":"),n=e.slice(0,t).trim().toLowerCase(),i=e.slice(t+1).trim();"undefined"==typeof r[n]?r[n]=i:Array.isArray(r[n])?r[n].push(i):r[n]=[r[n],i]}),r},an=un,ln=un;function cn(e,t,n){var i=e;return rn(t)?(n=t,"string"==typeof e&&(i={uri:e})):i=u({},t,{uri:e}),i.callback=n,i}function un(e,t,n){return hn(t=cn(e,t,n))}function hn(i){if("undefined"==typeof i.callback)throw new Error("callback argument missing");var r=!1,s=function(e,t,n){r||(r=!0,i.callback(e,t,n))};function t(e){return clearTimeout(a),e instanceof Error||(e=new Error(""+(e||"Unknown XMLHttpRequest Error"))),e.statusCode=0,s(e,v)}function e(){if(!o){var e;clearTimeout(a),e=i.useXDR&&void 0===l.status?200:1223===l.status?204:l.status;var t=v,n=null;return 0!==e?(t={body:function(){var e=void 0;if(e=l.response?l.response:l.responseText||function(e){try{if("document"===e.responseType)return e.responseXML;var t=e.responseXML&&"parsererror"===e.responseXML.documentElement.nodeName;if(""===e.responseType&&!t)return e.responseXML}catch(e){}return null}(l),f)try{e=JSON.parse(e)}catch(e){}return e}(),statusCode:e,method:u,headers:{},url:c,rawRequest:l},l.getAllResponseHeaders&&(t.headers=on(l.getAllResponseHeaders()))):n=new Error("Internal XMLHttpRequest Error"),s(n,t,t.body)}}var n,o,a,l=i.xhr||null,c=(l=l||(i.cors||i.useXDR?new un.XDomainRequest:new un.XMLHttpRequest)).url=i.uri||i.url,u=l.method=i.method||"GET",h=i.body||i.data,d=l.headers=i.headers||{},p=!!i.sync,f=!1,v={body:void 0,headers:{},statusCode:0,method:u,url:c,rawRequest:l};if("json"in i&&!1!==i.json&&(f=!0,d.accept||d.Accept||(d.Accept="application/json"),"GET"!==u&&"HEAD"!==u&&(d["content-type"]||d["Content-Type"]||(d["Content-Type"]="application/json"),h=JSON.stringify(!0===i.json?h:i.json))),l.onreadystatechange=function(){4===l.readyState&&setTimeout(e,0)},l.onload=e,l.onerror=t,l.onprogress=function(){},l.onabort=function(){o=!0},l.ontimeout=t,l.open(u,c,!p,i.username,i.password),p||(l.withCredentials=!!i.withCredentials),!p&&0<i.timeout&&(a=setTimeout(function(){if(!o){o=!0,l.abort("timeout");var e=new Error("XMLHttpRequest timeout");e.code="ETIMEDOUT",t(e)}},i.timeout)),l.setRequestHeader)for(n in d)d.hasOwnProperty(n)&&l.setRequestHeader(n,d[n]);else if(i.headers&&!function(e){for(var t in e)if(e.hasOwnProperty(t))return!1;return!0}(i.headers))throw new Error("Headers cannot be set on an XDomainRequest object");return"responseType"in i&&(l.responseType=i.responseType),"beforeSend"in i&&"function"==typeof i.beforeSend&&i.beforeSend(l),l.send(h||null),l}un.XMLHttpRequest=nn.XMLHttpRequest||function(){},un.XDomainRequest="withCredentials"in new un.XMLHttpRequest?un.XMLHttpRequest:nn.XDomainRequest,function(e,t){for(var n=0;n<e.length;n++)t(e[n])}(["get","put","post","patch","head","delete"],function(i){un["delete"===i?"del":i]=function(e,t,n){return(t=cn(e,t,n)).method=i.toUpperCase(),hn(t)}}),an.default=ln;function dn(e,t){var n=new d.WebVTT.Parser(d,d.vttjs,d.WebVTT.StringDecoder()),i=[];n.oncue=function(e){t.addCue(e)},n.onparsingerror=function(e){i.push(e)},n.onflush=function(){t.trigger({type:"loadeddata",target:t})},n.parse(e),0<i.length&&(d.console&&d.console.groupCollapsed&&d.console.groupCollapsed("Text Track parsing errors for "+t.src),i.forEach(function(e){return v.error(e)}),d.console&&d.console.groupEnd&&d.console.groupEnd()),n.flush()}function pn(e,i){var t={uri:e},n=Wt(e);n&&(t.cors=n);var r="use-credentials"===i.tech_.crossOrigin();r&&(t.withCredentials=r),an(t,$e(this,function(e,t,n){if(e)return v.error(e,t);i.loaded_=!0,"function"!=typeof d.WebVTT?i.tech_&&i.tech_.any(["vttjsloaded","vttjserror"],function(e){if("vttjserror"!==e.type)return dn(n,i);v.error("vttjs failed to load, stopping trying to process "+i.src)}):dn(n,i)}))}var fn=function(c){function e(e){var t;if(void 0===e&&(e={}),!e.tech)throw new Error("A tech was not provided.");var n=ft(e,{kind:Jt[e.kind]||"subtitles",language:e.language||e.srclang||""}),i=Zt[n.mode]||"disabled",r=n.default;"metadata"!==n.kind&&"chapters"!==n.kind||(i="hidden"),(t=c.call(this,n)||this).tech_=n.tech,t.cues_=[],t.activeCues_=[],t.preload_=!1!==t.tech_.preloadTextTracks;var s=new $t(t.cues_),o=new $t(t.activeCues_),a=!1,l=$e(bt(t),function(){this.tech_.isReady_&&!this.tech_.isDisposed()&&(this.activeCues=this.activeCues,a&&(this.trigger("cuechange"),a=!1))});return t.tech_.one("dispose",function(){t.tech_.off("timeupdate",l)}),"disabled"!==i&&t.tech_.on("timeupdate",l),Object.defineProperties(bt(t),{default:{get:function(){return r},set:function(){}},mode:{get:function(){return i},set:function(e){Zt[e]&&i!==e&&(i=e,this.preload_||"disabled"===i||0!==this.cues.length||pn(this.src,this),this.tech_.off("timeupdate",l),"disabled"!==i&&this.tech_.on("timeupdate",l),this.trigger("modechange"))}},cues:{get:function(){return this.loaded_?s:null},set:function(){}},activeCues:{get:function(){if(!this.loaded_)return null;if(0===this.cues.length)return o;for(var e=this.tech_.currentTime(),t=[],n=0,i=this.cues.length;n<i;n++){var r=this.cues[n];r.startTime<=e&&r.endTime>=e?t.push(r):r.startTime===r.endTime&&r.startTime<=e&&r.startTime+.5>=e&&t.push(r)}if(a=!1,t.length!==this.activeCues_.length)a=!0;else for(var s=0;s<t.length;s++)-1===this.activeCues_.indexOf(t[s])&&(a=!0);return this.activeCues_=t,o.setCues_(this.activeCues_),o},set:function(){}}}),n.src?(t.src=n.src,t.preload_||(t.loaded_=!0),(t.preload_||r||"subtitles"!==n.kind&&"captions"!==n.kind)&&pn(t.src,bt(t))):t.loaded_=!0,t}Tt(e,c);var t=e.prototype;return t.addCue=function(e){var t=e;if(d.vttjs&&!(e instanceof d.vttjs.VTTCue)){for(var n in t=new d.vttjs.VTTCue(e.startTime,e.endTime,e.text),e)n in t||(t[n]=e[n]);t.id=e.id,t.originalCue_=e}for(var i=this.tech_.textTracks(),r=0;r<i.length;r++)i[r]!==this&&i[r].removeCue(t);this.cues_.push(t),this.cues.setCues_(this.cues_)},t.removeCue=function(e){for(var t=this.cues_.length;t--;){var n=this.cues_[t];if(n===e||n.originalCue_&&n.originalCue_===e){this.cues_.splice(t,1),this.cues.setCues_(this.cues_);break}}},e}(en);fn.prototype.allowedEvents_={cuechange:"cuechange"};var vn=function(r){function e(e){var t;void 0===e&&(e={});var n=ft(e,{kind:Qt[e.kind]||""});t=r.call(this,n)||this;var i=!1;return Object.defineProperty(bt(t),"enabled",{get:function(){return i},set:function(e){"boolean"==typeof e&&e!==i&&(i=e,this.trigger("enabledchange"))}}),n.enabled&&(t.enabled=n.enabled),t.loaded_=!0,t}return Tt(e,r),e}(en),gn=function(r){function e(e){var t;void 0===e&&(e={});var n=ft(e,{kind:Yt[e.kind]||""});t=r.call(this,n)||this;var i=!1;return Object.defineProperty(bt(t),"selected",{get:function(){return i},set:function(e){"boolean"==typeof e&&e!==i&&(i=e,this.trigger("selectedchange"))}}),n.selected&&(t.selected=n.selected),t}return Tt(e,r),e}(en),_n=function(r){function e(e){var t,n;void 0===e&&(e={}),t=r.call(this)||this;var i=new fn(e);return t.kind=i.kind,t.src=i.src,t.srclang=i.language,t.label=i.label,t.default=i.default,Object.defineProperties(bt(t),{readyState:{get:function(){return n}},track:{get:function(){return i}}}),n=0,i.addEventListener("loadeddata",function(){n=2,t.trigger({type:"load",target:bt(t)})}),t}return Tt(e,r),e}(Qe);_n.prototype.allowedEvents_={load:"load"},_n.NONE=0,_n.LOADING=1,_n.LOADED=2,_n.ERROR=3;var yn={audio:{ListClass:Ut,TrackClass:vn,capitalName:"Audio"},video:{ListClass:qt,TrackClass:gn,capitalName:"Video"},text:{ListClass:Xt,TrackClass:fn,capitalName:"Text"}};Object.keys(yn).forEach(function(e){yn[e].getterName=e+"Tracks",yn[e].privateName=e+"Tracks_"});var mn={remoteText:{ListClass:Xt,TrackClass:fn,capitalName:"RemoteText",getterName:"remoteTextTracks",privateName:"remoteTextTracks_"},remoteTextEl:{ListClass:Gt,TrackClass:_n,capitalName:"RemoteTextTrackEls",getterName:"remoteTextTrackEls",privateName:"remoteTextTrackEls_"}},bn=u({},yn,mn);mn.names=Object.keys(mn),yn.names=Object.keys(yn),bn.names=[].concat(mn.names).concat(yn.names);var Tn={};var kn=function(t){function n(n,e){var i;return void 0===n&&(n={}),void 0===e&&(e=function(){}),n.reportTouchActivity=!1,(i=t.call(this,null,n,e)||this).hasStarted_=!1,i.on("playing",function(){this.hasStarted_=!0}),i.on("loadstart",function(){this.hasStarted_=!1}),bn.names.forEach(function(e){var t=bn[e];n&&n[t.getterName]&&(i[t.privateName]=n[t.getterName])}),i.featuresProgressEvents||i.manualProgressOn(),i.featuresTimeupdateEvents||i.manualTimeUpdatesOn(),["Text","Audio","Video"].forEach(function(e){!1===n["native"+e+"Tracks"]&&(i["featuresNative"+e+"Tracks"]=!1)}),!1===n.nativeCaptions||!1===n.nativeTextTracks?i.featuresNativeTextTracks=!1:!0!==n.nativeCaptions&&!0!==n.nativeTextTracks||(i.featuresNativeTextTracks=!0),i.featuresNativeTextTracks||i.emulateTextTracks(),i.preloadTextTracks=!1!==n.preloadTextTracks,i.autoRemoteTextTracks_=new bn.text.ListClass,i.initTrackListeners(),n.nativeControlsForTouch||i.emitTapEvents(),i.constructor&&(i.name_=i.constructor.name||"Unknown Tech"),i}Tt(n,t);var e=n.prototype;return e.triggerSourceset=function(e){var t=this;this.isReady_||this.one("ready",function(){return t.setTimeout(function(){return t.triggerSourceset(e)},1)}),this.trigger({src:e,type:"sourceset"})},e.manualProgressOn=function(){this.on("durationchange",this.onDurationChange),this.manualProgress=!0,this.one("ready",this.trackProgress)},e.manualProgressOff=function(){this.manualProgress=!1,this.stopTrackingProgress(),this.off("durationchange",this.onDurationChange)},e.trackProgress=function(e){this.stopTrackingProgress(),this.progressInterval=this.setInterval($e(this,function(){var e=this.bufferedPercent();this.bufferedPercent_!==e&&this.trigger("progress"),1===(this.bufferedPercent_=e)&&this.stopTrackingProgress()}),500)},e.onDurationChange=function(e){this.duration_=this.duration()},e.buffered=function(){return Et(0,0)},e.bufferedPercent=function(){return St(this.buffered(),this.duration_)},e.stopTrackingProgress=function(){this.clearInterval(this.progressInterval)},e.manualTimeUpdatesOn=function(){this.manualTimeUpdates=!0,this.on("play",this.trackCurrentTime),this.on("pause",this.stopTrackingCurrentTime)},e.manualTimeUpdatesOff=function(){this.manualTimeUpdates=!1,this.stopTrackingCurrentTime(),this.off("play",this.trackCurrentTime),this.off("pause",this.stopTrackingCurrentTime)},e.trackCurrentTime=function(){this.currentTimeInterval&&this.stopTrackingCurrentTime(),this.currentTimeInterval=this.setInterval(function(){this.trigger({type:"timeupdate",target:this,manuallyTriggered:!0})},250)},e.stopTrackingCurrentTime=function(){this.clearInterval(this.currentTimeInterval),this.trigger({type:"timeupdate",target:this,manuallyTriggered:!0})},e.dispose=function(){this.clearTracks(yn.names),this.manualProgress&&this.manualProgressOff(),this.manualTimeUpdates&&this.manualTimeUpdatesOff(),t.prototype.dispose.call(this)},e.clearTracks=function(e){var r=this;(e=[].concat(e)).forEach(function(e){for(var t=r[e+"Tracks"]()||[],n=t.length;n--;){var i=t[n];"text"===e&&r.removeRemoteTextTrack(i),t.removeTrack(i)}})},e.cleanupAutoTextTracks=function(){for(var e=this.autoRemoteTextTracks_||[],t=e.length;t--;){var n=e[t];this.removeRemoteTextTrack(n)}},e.reset=function(){},e.crossOrigin=function(){},e.setCrossOrigin=function(){},e.error=function(e){return void 0!==e&&(this.error_=new wt(e),this.trigger("error")),this.error_},e.played=function(){return this.hasStarted_?Et(0,0):Et()},e.play=function(){},e.setScrubbing=function(){},e.scrubbing=function(){},e.setCurrentTime=function(){this.manualTimeUpdates&&this.trigger({type:"timeupdate",target:this,manuallyTriggered:!0})},e.initTrackListeners=function(){var r=this;yn.names.forEach(function(e){function t(){r.trigger(e+"trackchange")}var n=yn[e],i=r[n.getterName]();i.addEventListener("removetrack",t),i.addEventListener("addtrack",t),r.on("dispose",function(){i.removeEventListener("removetrack",t),i.removeEventListener("addtrack",t)})})},e.addWebVttScript_=function(){var e=this;if(!d.WebVTT)if(p.body.contains(this.el())){if(!this.options_["vtt.js"]&&k(Tn)&&0<Object.keys(Tn).length)return void this.trigger("vttjsloaded");var t=p.createElement("script");t.src=this.options_["vtt.js"]||"https://vjs.zencdn.net/vttjs/0.14.1/vtt.min.js",t.onload=function(){e.trigger("vttjsloaded")},t.onerror=function(){e.trigger("vttjserror")},this.on("dispose",function(){t.onload=null,t.onerror=null}),d.WebVTT=!0,this.el().parentNode.appendChild(t)}else this.ready(this.addWebVttScript_)},e.emulateTextTracks=function(){function t(e){return i.addTrack(e.track)}function n(e){return i.removeTrack(e.track)}var e=this,i=this.textTracks(),r=this.remoteTextTracks();r.on("addtrack",t),r.on("removetrack",n),this.addWebVttScript_();function s(){return e.trigger("texttrackchange")}function o(){s();for(var e=0;e<i.length;e++){var t=i[e];t.removeEventListener("cuechange",s),"showing"===t.mode&&t.addEventListener("cuechange",s)}}o(),i.addEventListener("change",o),i.addEventListener("addtrack",o),i.addEventListener("removetrack",o),this.on("dispose",function(){r.off("addtrack",t),r.off("removetrack",n),i.removeEventListener("change",o),i.removeEventListener("addtrack",o),i.removeEventListener("removetrack",o);for(var e=0;e<i.length;e++){i[e].removeEventListener("cuechange",s)}})},e.addTextTrack=function(e,t,n){if(!e)throw new Error("TextTrack kind is required but was not provided");return function(e,t,n,i,r){void 0===r&&(r={});var s=e.textTracks();r.kind=t,n&&(r.label=n),i&&(r.language=i),r.tech=e;var o=new bn.text.TrackClass(r);return s.addTrack(o),o}(this,e,t,n)},e.createRemoteTextTrack=function(e){var t=ft(e,{tech:this});return new mn.remoteTextEl.TrackClass(t)},e.addRemoteTextTrack=function(e,t){var n=this;void 0===e&&(e={});var i=this.createRemoteTextTrack(e);return!0!==t&&!1!==t&&(v.warn('Calling addRemoteTextTrack without explicitly setting the "manualCleanup" parameter to `true` is deprecated and default to `false` in future version of video.js'),t=!0),this.remoteTextTrackEls().addTrackElement_(i),this.remoteTextTracks().addTrack(i.track),!0!==t&&this.ready(function(){return n.autoRemoteTextTracks_.addTrack(i.track)}),i},e.removeRemoteTextTrack=function(e){var t=this.remoteTextTrackEls().getTrackElementByTrack_(e);this.remoteTextTrackEls().removeTrackElement_(t),this.remoteTextTracks().removeTrack(e),this.autoRemoteTextTracks_.removeTrack(e)},e.getVideoPlaybackQuality=function(){return{}},e.requestPictureInPicture=function(){var e=this.options_.Promise||d.Promise;if(e)return e.reject()},e.disablePictureInPicture=function(){return!0},e.setDisablePictureInPicture=function(){},e.setPoster=function(){},e.playsinline=function(){},e.setPlaysinline=function(){},e.overrideNativeAudioTracks=function(){},e.overrideNativeVideoTracks=function(){},e.canPlayType=function(){return""},n.canPlayType=function(){return""},n.canPlaySource=function(e,t){return n.canPlayType(e.type)},n.isTech=function(e){return e.prototype instanceof n||e instanceof n||e===n},n.registerTech=function(e,t){if(n.techs_||(n.techs_={}),!n.isTech(t))throw new Error("Tech "+e+" must be a Tech");if(!n.canPlayType)throw new Error("Techs must have a static canPlayType method on them");if(!n.canPlaySource)throw new Error("Techs must have a static canPlaySource method on them");return e=pt(e),n.techs_[e]=t,n.techs_[dt(e)]=t,"Tech"!==e&&n.defaultTechOrder_.push(e),t},n.getTech=function(e){if(e)return n.techs_&&n.techs_[e]?n.techs_[e]:(e=pt(e),d&&d.videojs&&d.videojs[e]?(v.warn("The "+e+" tech was added to the videojs object when it should be registered using videojs.registerTech(name, tech)"),d.videojs[e]):void 0)},n}(mt);bn.names.forEach(function(e){var t=bn[e];kn.prototype[t.getterName]=function(){return this[t.privateName]=this[t.privateName]||new t.ListClass,this[t.privateName]}}),kn.prototype.featuresVolumeControl=!0,kn.prototype.featuresMuteControl=!0,kn.prototype.featuresFullscreenResize=!1,kn.prototype.featuresPlaybackRate=!1,kn.prototype.featuresProgressEvents=!1,kn.prototype.featuresSourceset=!1,kn.prototype.featuresTimeupdateEvents=!1,kn.prototype.featuresNativeTextTracks=!1,kn.withSourceHandlers=function(r){r.registerSourceHandler=function(e,t){var n=r.sourceHandlers;n=n||(r.sourceHandlers=[]),void 0===t&&(t=n.length),n.splice(t,0,e)},r.canPlayType=function(e){for(var t,n=r.sourceHandlers||[],i=0;i<n.length;i++)if(t=n[i].canPlayType(e))return t;return""},r.selectSourceHandler=function(e,t){for(var n=r.sourceHandlers||[],i=0;i<n.length;i++)if(n[i].canHandleSource(e,t))return n[i];return null},r.canPlaySource=function(e,t){var n=r.selectSourceHandler(e,t);return n?n.canHandleSource(e,t):""};["seekable","seeking","duration"].forEach(function(e){var t=this[e];"function"==typeof t&&(this[e]=function(){return this.sourceHandler_&&this.sourceHandler_[e]?this.sourceHandler_[e].apply(this.sourceHandler_,arguments):t.apply(this,arguments)})},r.prototype),r.prototype.setSource=function(e){var t=r.selectSourceHandler(e,this.options_);t||(r.nativeSourceHandler?t=r.nativeSourceHandler:v.error("No source handler found for the current source.")),this.disposeSourceHandler(),this.off("dispose",this.disposeSourceHandler),t!==r.nativeSourceHandler&&(this.currentSource_=e),this.sourceHandler_=t.handleSource(e,this,this.options_),this.one("dispose",this.disposeSourceHandler)},r.prototype.disposeSourceHandler=function(){this.currentSource_&&(this.clearTracks(["audio","video"]),this.currentSource_=null),this.cleanupAutoTextTracks(),this.sourceHandler_&&(this.sourceHandler_.dispose&&this.sourceHandler_.dispose(),this.sourceHandler_=null)}},mt.registerComponent("Tech",kn),kn.registerTech("Tech",kn),kn.defaultTechOrder_=[];var Cn={},En={},Sn={};function wn(e,t,n){e.setTimeout(function(){return function n(i,e,r,s,o,a){void 0===i&&(i={});void 0===e&&(e=[]);void 0===o&&(o=[]);void 0===a&&(a=!1);var t=e,l=t[0],c=t.slice(1);if("string"==typeof l)n(i,Cn[l],r,s,o,a);else if(l){var u=Ln(s,l);if(!u.setSource)return o.push(u),n(i,c,r,s,o,a);u.setSource(b({},i),function(e,t){if(e)return n(i,c,r,s,o,a);o.push(u),n(t,i.type===t.type?c:Cn[t.type],r,s,o,a)})}else c.length?n(i,c,r,s,o,a):a?r(i,o):n(i,Cn["*"],r,s,o,!0)}(t,Cn[t.type],n,e)},1)}function xn(e,t,n,i){void 0===i&&(i=null);var r="call"+pt(n),s=e.reduce(In(r),i),o=s===Sn,a=o?null:t[n](s);return function(e,t,n,i){for(var r=e.length-1;0<=r;r--){var s=e[r];s[t]&&s[t](i,n)}}(e,n,a,o),a}var Pn={buffered:1,currentTime:1,duration:1,muted:1,played:1,paused:1,seekable:1,volume:1},jn={setCurrentTime:1,setMuted:1,setVolume:1},An={play:1,pause:1};function In(n){return function(e,t){return e===Sn?Sn:t[n]?t[n](e):e}}function Ln(e,t){var n=En[e.id()],i=null;if(null==n)return i=t(e),En[e.id()]=[[t,i]],i;for(var r=0;r<n.length;r++){var s=n[r],o=s[0],a=s[1];o===t&&(i=a)}return null===i&&(i=t(e),n.push([t,i])),i}function Nn(e){void 0===e&&(e="");var t=zt(e);return On[t.toLowerCase()]||""}var On={opus:"video/ogg",ogv:"video/ogg",mp4:"video/mp4",mov:"video/mp4",m4v:"video/mp4",mkv:"video/x-matroska",m4a:"audio/mp4",mp3:"audio/mpeg",aac:"audio/aac",caf:"audio/x-caf",flac:"audio/flac",oga:"audio/ogg",wav:"audio/wav",m3u8:"application/x-mpegURL",jpg:"image/jpeg",jpeg:"image/jpeg",gif:"image/gif",png:"image/png",svg:"image/svg+xml",webp:"image/webp"};function Mn(e){if(!e.type){var t=Nn(e.src);t&&(e.type=t)}return e}var Dn=function(c){function e(e,t,n){var i,r=ft({createEl:!1},t);if(i=c.call(this,e,r,n)||this,t.playerOptions.sources&&0!==t.playerOptions.sources.length)e.src(t.playerOptions.sources);else for(var s=0,o=t.playerOptions.techOrder;s<o.length;s++){var a=pt(o[s]),l=kn.getTech(a);if(a||(l=mt.getComponent(a)),l&&l.isSupported()){e.loadTech_(a);break}}return i}return Tt(e,c),e}(mt);mt.registerComponent("MediaLoader",Dn);var Fn=function(r){function e(e,t){var n;return(n=r.call(this,e,t)||this).emitTapEvents(),n.enable(),n}Tt(e,r);var t=e.prototype;return t.createEl=function(e,t,n){void 0===e&&(e="div"),void 0===t&&(t={}),void 0===n&&(n={}),t=b({innerHTML:'<span aria-hidden="true" class="vjs-icon-placeholder"></span>',className:this.buildCSSClass(),tabIndex:0},t),"button"===e&&v.error("Creating a ClickableComponent with an HTML element of "+e+" is not supported; use a Button instead."),n=b({role:"button"},n),this.tabIndex_=t.tabIndex;var i=r.prototype.createEl.call(this,e,t,n);return this.createControlTextEl(i),i},t.dispose=function(){this.controlTextEl_=null,r.prototype.dispose.call(this)},t.createControlTextEl=function(e){return this.controlTextEl_=te("span",{className:"vjs-control-text"},{"aria-live":"polite"}),e&&e.appendChild(this.controlTextEl_),this.controlText(this.controlText_,e),this.controlTextEl_},t.controlText=function(e,t){if(void 0===t&&(t=this.el()),void 0===e)return this.controlText_||"Need Text";var n=this.localize(e);this.controlText_=e,ne(this.controlTextEl_,n),this.nonIconControl||t.setAttribute("title",n)},t.buildCSSClass=function(){return"vjs-control vjs-button "+r.prototype.buildCSSClass.call(this)},t.enable=function(){this.enabled_||(this.enabled_=!0,this.removeClass("vjs-disabled"),this.el_.setAttribute("aria-disabled","false"),"undefined"!=typeof this.tabIndex_&&this.el_.setAttribute("tabIndex",this.tabIndex_),this.on(["tap","click"],this.handleClick),this.on("keydown",this.handleKeyDown))},t.disable=function(){this.enabled_=!1,this.addClass("vjs-disabled"),this.el_.setAttribute("aria-disabled","true"),"undefined"!=typeof this.tabIndex_&&this.el_.removeAttribute("tabIndex"),this.off("mouseover",this.handleMouseOver),this.off("mouseout",this.handleMouseOut),this.off(["tap","click"],this.handleClick),this.off("keydown",this.handleKeyDown)},t.handleLanguagechange=function(){this.controlText(this.controlText_)},t.handleClick=function(e){this.options_.clickHandler&&this.options_.clickHandler.call(this,arguments)},t.handleKeyDown=function(e){Ot.isEventKey(e,"Space")||Ot.isEventKey(e,"Enter")?(e.preventDefault(),e.stopPropagation(),this.trigger("click")):r.prototype.handleKeyDown.call(this,e)},e}(mt);mt.registerComponent("ClickableComponent",Fn);var Rn=function(i){function e(e,t){var n;return(n=i.call(this,e,t)||this).update(),e.on("posterchange",$e(bt(n),n.update)),n}Tt(e,i);var t=e.prototype;return t.dispose=function(){this.player().off("posterchange",this.update),i.prototype.dispose.call(this)},t.createEl=function(){return te("div",{className:"vjs-poster",tabIndex:-1})},t.update=function(e){var t=this.player().poster();this.setSrc(t),t?this.show():this.hide()},t.setSrc=function(e){var t="";e&&(t='url("'+e+'")'),this.el_.style.backgroundImage=t},t.handleClick=function(e){if(this.player_.controls()){var t=this.player_.usingPlugin("eme")&&this.player_.eme.sessions&&0<this.player_.eme.sessions.length;!this.player_.tech(!0)||(H||F)&&t||this.player_.tech(!0).focus(),this.player_.paused()?At(this.player_.play()):this.player_.pause()}},e}(Fn);mt.registerComponent("PosterImage",Rn);var Bn="#222",Hn={monospace:"monospace",sansSerif:"sans-serif",serif:"serif",monospaceSansSerif:'"Andale Mono", "Lucida Console", monospace',monospaceSerif:'"Courier New", monospace',proportionalSansSerif:"sans-serif",proportionalSerif:"serif",casual:'"Comic Sans MS", Impact, fantasy',script:'"Monotype Corsiva", cursive',smallcaps:'"Andale Mono", "Lucida Console", monospace, sans-serif'};function Vn(e,t){var n;if(4===e.length)n=e[1]+e[1]+e[2]+e[2]+e[3]+e[3];else{if(7!==e.length)throw new Error("Invalid color code provided, "+e+"; must be formatted as e.g. #f0e or #f604e2.");n=e.slice(1)}return"rgba("+parseInt(n.slice(0,2),16)+","+parseInt(n.slice(2,4),16)+","+parseInt(n.slice(4,6),16)+","+t+")"}function Kn(e,t,n){try{e.style[t]=n}catch(e){return}}var zn=function(s){function e(n,e,t){var i;i=s.call(this,n,e,t)||this;var r=$e(bt(i),i.updateDisplay);return n.on("loadstart",$e(bt(i),i.toggleDisplay)),n.on("texttrackchange",r),n.on("loadedmetadata",$e(bt(i),i.preselectTrack)),n.ready($e(bt(i),function(){if(n.tech_&&n.tech_.featuresNativeTextTracks)this.hide();else{n.on("fullscreenchange",r),n.on("playerresize",r),d.addEventListener("orientationchange",r),n.on("dispose",function(){return d.removeEventListener("orientationchange",r)});for(var e=this.options_.playerOptions.tracks||[],t=0;t<e.length;t++)this.player_.addRemoteTextTrack(e[t],!0);this.preselectTrack()}})),i}Tt(e,s);var t=e.prototype;return t.preselectTrack=function(){for(var e,t,n,i={captions:1,subtitles:1},r=this.player_.textTracks(),s=this.player_.cache_.selectedLanguage,o=0;o<r.length;o++){var a=r[o];s&&s.enabled&&s.language&&s.language===a.language&&a.kind in i?n=a.kind===s.kind?a:n||a:s&&!s.enabled?t=e=n=null:a.default&&("descriptions"!==a.kind||e?a.kind in i&&!t&&(t=a):e=a)}n?n.mode="showing":t?t.mode="showing":e&&(e.mode="showing")},t.toggleDisplay=function(){this.player_.tech_&&this.player_.tech_.featuresNativeTextTracks?this.hide():this.show()},t.createEl=function(){return s.prototype.createEl.call(this,"div",{className:"vjs-text-track-display"},{"aria-live":"off","aria-atomic":"true"})},t.clearDisplay=function(){"function"==typeof d.WebVTT&&d.WebVTT.processCues(d,[],this.el_)},t.updateDisplay=function(){var e=this.player_.textTracks(),t=this.options_.allowMultipleShowingTracks;if(this.clearDisplay(),t){for(var n=[],i=0;i<e.length;++i){var r=e[i];"showing"===r.mode&&n.push(r)}this.updateForTrack(n)}else{for(var s=null,o=null,a=e.length;a--;){var l=e[a];"showing"===l.mode&&("descriptions"===l.kind?s=l:o=l)}o?("off"!==this.getAttribute("aria-live")&&this.setAttribute("aria-live","off"),this.updateForTrack(o)):s&&("assertive"!==this.getAttribute("aria-live")&&this.setAttribute("aria-live","assertive"),this.updateForTrack(s))}},t.updateDisplayState=function(e){for(var t=this.player_.textTrackSettings.getValues(),n=e.activeCues,i=n.length;i--;){var r=n[i];if(r){var s=r.displayState;if(t.color&&(s.firstChild.style.color=t.color),t.textOpacity&&Kn(s.firstChild,"color",Vn(t.color||"#fff",t.textOpacity)),t.backgroundColor&&(s.firstChild.style.backgroundColor=t.backgroundColor),t.backgroundOpacity&&Kn(s.firstChild,"backgroundColor",Vn(t.backgroundColor||"#000",t.backgroundOpacity)),t.windowColor&&(t.windowOpacity?Kn(s,"backgroundColor",Vn(t.windowColor,t.windowOpacity)):s.style.backgroundColor=t.windowColor),t.edgeStyle&&("dropshadow"===t.edgeStyle?s.firstChild.style.textShadow="2px 2px 3px #222, 2px 2px 4px #222, 2px 2px 5px "+Bn:"raised"===t.edgeStyle?s.firstChild.style.textShadow="1px 1px #222, 2px 2px #222, 3px 3px "+Bn:"depressed"===t.edgeStyle?s.firstChild.style.textShadow="1px 1px #ccc, 0 1px #ccc, -1px -1px #222, 0 -1px "+Bn:"uniform"===t.edgeStyle&&(s.firstChild.style.textShadow="0 0 4px #222, 0 0 4px #222, 0 0 4px #222, 0 0 4px "+Bn)),t.fontPercent&&1!==t.fontPercent){var o=d.parseFloat(s.style.fontSize);s.style.fontSize=o*t.fontPercent+"px",s.style.height="auto",s.style.top="auto"}t.fontFamily&&"default"!==t.fontFamily&&("small-caps"===t.fontFamily?s.firstChild.style.fontVariant="small-caps":s.firstChild.style.fontFamily=Hn[t.fontFamily])}}},t.updateForTrack=function(e){if(Array.isArray(e)||(e=[e]),"function"==typeof d.WebVTT&&!e.every(function(e){return!e.activeCues})){for(var t=[],n=0;n<e.length;++n)for(var i=e[n],r=0;r<i.activeCues.length;++r)t.push(i.activeCues[r]);d.WebVTT.processCues(d,t,this.el_);for(var s=0;s<e.length;++s){for(var o=e[s],a=0;a<o.activeCues.length;++a){var l=o.activeCues[a].displayState;se(l,"vjs-text-track-cue"),se(l,"vjs-text-track-cue-"+(o.language?o.language:s))}this.player_.textTrackSettings&&this.updateDisplayState(o)}}},e}(mt);mt.registerComponent("TextTrackDisplay",zn);var Wn=function(r){function e(){return r.apply(this,arguments)||this}return Tt(e,r),e.prototype.createEl=function(){var e=this.player_.isAudio(),t=this.localize(e?"Audio Player":"Video Player"),n=te("span",{className:"vjs-control-text",innerHTML:this.localize("{1} is loading.",[t])}),i=r.prototype.createEl.call(this,"div",{className:"vjs-loading-spinner",dir:"ltr"});return i.appendChild(n),i},e}(mt);mt.registerComponent("LoadingSpinner",Wn);var Un=function(t){function e(){return t.apply(this,arguments)||this}Tt(e,t);var n=e.prototype;return n.createEl=function(e,t,n){void 0===t&&(t={}),void 0===n&&(n={}),t=b({innerHTML:'<span aria-hidden="true" class="vjs-icon-placeholder"></span>',className:this.buildCSSClass()},t),n=b({type:"button"},n);var i=mt.prototype.createEl.call(this,"button",t,n);return this.createControlTextEl(i),i},n.addChild=function(e,t){void 0===t&&(t={});var n=this.constructor.name;return v.warn("Adding an actionable (user controllable) child to a Button ("+n+") is not supported; use a ClickableComponent instead."),mt.prototype.addChild.call(this,e,t)},n.enable=function(){t.prototype.enable.call(this),this.el_.removeAttribute("disabled")},n.disable=function(){t.prototype.disable.call(this),this.el_.setAttribute("disabled","disabled")},n.handleKeyDown=function(e){Ot.isEventKey(e,"Space")||Ot.isEventKey(e,"Enter")?e.stopPropagation():t.prototype.handleKeyDown.call(this,e)},e}(Fn);mt.registerComponent("Button",Un);var qn=function(i){function e(e,t){var n;return(n=i.call(this,e,t)||this).mouseused_=!1,n.on("mousedown",n.handleMouseDown),n}Tt(e,i);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-big-play-button"},t.handleClick=function(e){var t=this.player_.play();if(this.mouseused_&&e.clientX&&e.clientY){var n=this.player_.usingPlugin("eme")&&this.player_.eme.sessions&&0<this.player_.eme.sessions.length;return At(t),void(!this.player_.tech(!0)||(H||F)&&n||this.player_.tech(!0).focus())}var i=this.player_.getChild("controlBar"),r=i&&i.getChild("playToggle");if(r){var s=function(){return r.focus()};jt(t)?t.then(s,function(){}):this.setTimeout(s,1)}else this.player_.tech(!0).focus()},t.handleKeyDown=function(e){this.mouseused_=!1,i.prototype.handleKeyDown.call(this,e)},t.handleMouseDown=function(e){this.mouseused_=!0},e}(Un);qn.prototype.controlText_="Play Video",mt.registerComponent("BigPlayButton",qn);var Xn=function(i){function e(e,t){var n;return(n=i.call(this,e,t)||this).controlText(t&&t.controlText||n.localize("Close")),n}Tt(e,i);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-close-button "+i.prototype.buildCSSClass.call(this)},t.handleClick=function(e){this.trigger({type:"close",bubbles:!1})},t.handleKeyDown=function(e){Ot.isEventKey(e,"Esc")?(e.preventDefault(),e.stopPropagation(),this.trigger("click")):i.prototype.handleKeyDown.call(this,e)},e}(Un);mt.registerComponent("CloseButton",Xn);var Gn=function(i){function e(e,t){var n;return void 0===t&&(t={}),n=i.call(this,e,t)||this,t.replay=void 0===t.replay||t.replay,n.on(e,"play",n.handlePlay),n.on(e,"pause",n.handlePause),t.replay&&n.on(e,"ended",n.handleEnded),n}Tt(e,i);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-play-control "+i.prototype.buildCSSClass.call(this)},t.handleClick=function(e){this.player_.paused()?this.player_.play():this.player_.pause()},t.handleSeeked=function(e){this.removeClass("vjs-ended"),this.player_.paused()?this.handlePause(e):this.handlePlay(e)},t.handlePlay=function(e){this.removeClass("vjs-ended"),this.removeClass("vjs-paused"),this.addClass("vjs-playing"),this.controlText("Pause")},t.handlePause=function(e){this.removeClass("vjs-playing"),this.addClass("vjs-paused"),this.controlText("Play")},t.handleEnded=function(e){this.removeClass("vjs-playing"),this.addClass("vjs-ended"),this.controlText("Replay"),this.one(this.player_,"seeked",this.handleSeeked)},e}(Un);Gn.prototype.controlText_="Play",mt.registerComponent("PlayToggle",Gn);function $n(e,t){e=e<0?0:e;var n=Math.floor(e%60),i=Math.floor(e/60%60),r=Math.floor(e/3600),s=Math.floor(t/60%60),o=Math.floor(t/3600);return!isNaN(e)&&e!==1/0||(r=i=n="-"),(r=0<r||0<o?r+":":"")+(i=((r||10<=s)&&i<10?"0"+i:i)+":")+(n=n<10?"0"+n:n)}var Yn=$n;function Qn(e,t){return void 0===t&&(t=e),Yn(e,t)}var Jn=function(i){function e(e,t){var n;return(n=i.call(this,e,t)||this).on(e,["timeupdate","ended"],n.updateContent),n.updateTextNode_(),n}Tt(e,i);var t=e.prototype;return t.createEl=function(){var e=this.buildCSSClass(),t=i.prototype.createEl.call(this,"div",{className:e+" vjs-time-control vjs-control",innerHTML:'<span class="vjs-control-text" role="presentation">'+this.localize(this.labelText_)+" </span>"});return this.contentEl_=te("span",{className:e+"-display"},{"aria-live":"off",role:"presentation"}),t.appendChild(this.contentEl_),t},t.dispose=function(){this.contentEl_=null,this.textNode_=null,i.prototype.dispose.call(this)},t.updateTextNode_=function(e){var t=this;void 0===e&&(e=0),e=Qn(e),this.formattedTime_!==e&&(this.formattedTime_=e,this.requestNamedAnimationFrame("TimeDisplay#updateTextNode_",function(){if(t.contentEl_){var e=t.textNode_;e&&t.contentEl_.firstChild!==e&&(e=null,v.warn("TimeDisplay#updateTextnode_: Prevented replacement of text node element since it was no longer a child of this node. Appending a new node instead.")),t.textNode_=p.createTextNode(t.formattedTime_),t.textNode_&&(e?t.contentEl_.replaceChild(t.textNode_,e):t.contentEl_.appendChild(t.textNode_))}}))},t.updateContent=function(e){},e}(mt);Jn.prototype.labelText_="Time",Jn.prototype.controlText_="Time",mt.registerComponent("TimeDisplay",Jn);var Zn=function(e){function t(){return e.apply(this,arguments)||this}Tt(t,e);var n=t.prototype;return n.buildCSSClass=function(){return"vjs-current-time"},n.updateContent=function(e){var t;t=this.player_.ended()?this.player_.duration():this.player_.scrubbing()?this.player_.getCache().currentTime:this.player_.currentTime(),this.updateTextNode_(t)},t}(Jn);Zn.prototype.labelText_="Current Time",Zn.prototype.controlText_="Current Time",mt.registerComponent("CurrentTimeDisplay",Zn);var ei=function(i){function e(e,t){var n;return(n=i.call(this,e,t)||this).on(e,"durationchange",n.updateContent),n.on(e,"loadstart",n.updateContent),n.on(e,"loadedmetadata",n.updateContent),n}Tt(e,i);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-duration"},t.updateContent=function(e){var t=this.player_.duration();this.updateTextNode_(t)},e}(Jn);ei.prototype.labelText_="Duration",ei.prototype.controlText_="Duration",mt.registerComponent("DurationDisplay",ei);var ti=function(e){function t(){return e.apply(this,arguments)||this}return Tt(t,e),t.prototype.createEl=function(){return e.prototype.createEl.call(this,"div",{className:"vjs-time-control vjs-time-divider",innerHTML:"<div><span>/</span></div>"},{"aria-hidden":!0})},t}(mt);mt.registerComponent("TimeDivider",ti);var ni=function(i){function e(e,t){var n;return(n=i.call(this,e,t)||this).on(e,"durationchange",n.updateContent),n}Tt(e,i);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-remaining-time"},t.createEl=function(){var e=i.prototype.createEl.call(this);return e.insertBefore(te("span",{},{"aria-hidden":!0},"-"),this.contentEl_),e},t.updateContent=function(e){var t;"number"==typeof this.player_.duration()&&(t=this.player_.ended()?0:this.player_.remainingTimeDisplay?this.player_.remainingTimeDisplay():this.player_.remainingTime(),this.updateTextNode_(t))},e}(Jn);ni.prototype.labelText_="Remaining Time",ni.prototype.controlText_="Remaining Time",mt.registerComponent("RemainingTimeDisplay",ni);var ii=function(i){function e(e,t){var n;return(n=i.call(this,e,t)||this).updateShowing(),n.on(n.player(),"durationchange",n.updateShowing),n}Tt(e,i);var t=e.prototype;return t.createEl=function(){var e=i.prototype.createEl.call(this,"div",{className:"vjs-live-control vjs-control"});return this.contentEl_=te("div",{className:"vjs-live-display",innerHTML:'<span class="vjs-control-text">'+this.localize("Stream Type")+" </span>"+this.localize("LIVE")},{"aria-live":"off"}),e.appendChild(this.contentEl_),e},t.dispose=function(){this.contentEl_=null,i.prototype.dispose.call(this)},t.updateShowing=function(e){this.player().duration()===1/0?this.show():this.hide()},e}(mt);mt.registerComponent("LiveDisplay",ii);var ri=function(i){function e(e,t){var n;return(n=i.call(this,e,t)||this).updateLiveEdgeStatus(),n.player_.liveTracker&&n.on(n.player_.liveTracker,"liveedgechange",n.updateLiveEdgeStatus),n}Tt(e,i);var t=e.prototype;return t.createEl=function(){var e=i.prototype.createEl.call(this,"button",{className:"vjs-seek-to-live-control vjs-control"});return this.textEl_=te("span",{className:"vjs-seek-to-live-text",innerHTML:this.localize("LIVE")},{"aria-hidden":"true"}),e.appendChild(this.textEl_),e},t.updateLiveEdgeStatus=function(){!this.player_.liveTracker||this.player_.liveTracker.atLiveEdge()?(this.setAttribute("aria-disabled",!0),this.addClass("vjs-at-live-edge"),this.controlText("Seek to live, currently playing live")):(this.setAttribute("aria-disabled",!1),this.removeClass("vjs-at-live-edge"),this.controlText("Seek to live, currently behind live"))},t.handleClick=function(){this.player_.liveTracker.seekToLiveEdge()},t.dispose=function(){this.player_.liveTracker&&this.off(this.player_.liveTracker,"liveedgechange",this.updateLiveEdgeStatus),this.textEl_=null,i.prototype.dispose.call(this)},e}(Un);ri.prototype.controlText_="Seek to live, currently playing live",mt.registerComponent("SeekToLive",ri);function si(e,t,n){return e=Number(e),Math.min(n,Math.max(t,isNaN(e)?t:e))}var oi=function(i){function e(e,t){var n;return(n=i.call(this,e,t)||this).bar=n.getChild(n.options_.barName),n.vertical(!!n.options_.vertical),n.enable(),n}Tt(e,i);var t=e.prototype;return t.enabled=function(){return this.enabled_},t.enable=function(){this.enabled()||(this.on("mousedown",this.handleMouseDown),this.on("touchstart",this.handleMouseDown),this.on("keydown",this.handleKeyDown),this.on("click",this.handleClick),this.on(this.player_,"controlsvisible",this.update),this.playerEvent&&this.on(this.player_,this.playerEvent,this.update),this.removeClass("disabled"),this.setAttribute("tabindex",0),this.enabled_=!0)},t.disable=function(){if(this.enabled()){var e=this.bar.el_.ownerDocument;this.off("mousedown",this.handleMouseDown),this.off("touchstart",this.handleMouseDown),this.off("keydown",this.handleKeyDown),this.off("click",this.handleClick),this.off(this.player_,"controlsvisible",this.update),this.off(e,"mousemove",this.handleMouseMove),this.off(e,"mouseup",this.handleMouseUp),this.off(e,"touchmove",this.handleMouseMove),this.off(e,"touchend",this.handleMouseUp),this.removeAttribute("tabindex"),this.addClass("disabled"),this.playerEvent&&this.off(this.player_,this.playerEvent,this.update),this.enabled_=!1}},t.createEl=function(e,t,n){return void 0===t&&(t={}),void 0===n&&(n={}),t.className=t.className+" vjs-slider",t=b({tabIndex:0},t),n=b({role:"slider","aria-valuenow":0,"aria-valuemin":0,"aria-valuemax":100,tabIndex:0},n),i.prototype.createEl.call(this,e,t,n)},t.handleMouseDown=function(e){var t=this.bar.el_.ownerDocument;"mousedown"===e.type&&e.preventDefault(),"touchstart"!==e.type||R||e.preventDefault(),pe(),this.addClass("vjs-sliding"),this.trigger("slideractive"),this.on(t,"mousemove",this.handleMouseMove),this.on(t,"mouseup",this.handleMouseUp),this.on(t,"touchmove",this.handleMouseMove),this.on(t,"touchend",this.handleMouseUp),this.handleMouseMove(e)},t.handleMouseMove=function(e){},t.handleMouseUp=function(){var e=this.bar.el_.ownerDocument;fe(),this.removeClass("vjs-sliding"),this.trigger("sliderinactive"),this.off(e,"mousemove",this.handleMouseMove),this.off(e,"mouseup",this.handleMouseUp),this.off(e,"touchmove",this.handleMouseMove),this.off(e,"touchend",this.handleMouseUp),this.update()},t.update=function(){var t=this;if(this.el_&&this.bar){var n=this.getProgress();return n===this.progress_||(this.progress_=n,this.requestNamedAnimationFrame("Slider#update",function(){var e=t.vertical()?"height":"width";t.bar.el().style[e]=(100*n).toFixed(2)+"%"})),n}},t.getProgress=function(){return Number(si(this.getPercent(),0,1).toFixed(4))},t.calculateDistance=function(e){var t=_e(this.el_,e);return this.vertical()?t.y:t.x},t.handleKeyDown=function(e){Ot.isEventKey(e,"Left")||Ot.isEventKey(e,"Down")?(e.preventDefault(),e.stopPropagation(),this.stepBack()):Ot.isEventKey(e,"Right")||Ot.isEventKey(e,"Up")?(e.preventDefault(),e.stopPropagation(),this.stepForward()):i.prototype.handleKeyDown.call(this,e)},t.handleClick=function(e){e.stopPropagation(),e.preventDefault()},t.vertical=function(e){if(void 0===e)return this.vertical_||!1;this.vertical_=!!e,this.vertical_?this.addClass("vjs-slider-vertical"):this.addClass("vjs-slider-horizontal")},e}(mt);mt.registerComponent("Slider",oi);function ai(e,t){return si(e/t*100,0,100).toFixed(2)+"%"}var li=function(r){function e(e,t){var n;return(n=r.call(this,e,t)||this).partEls_=[],n.on(e,"progress",n.update),n}Tt(e,r);var t=e.prototype;return t.createEl=function(){var e=r.prototype.createEl.call(this,"div",{className:"vjs-load-progress"}),t=te("span",{className:"vjs-control-text"}),n=te("span",{textContent:this.localize("Loaded")}),i=p.createTextNode(": ");return this.percentageEl_=te("span",{className:"vjs-control-text-loaded-percentage",textContent:"0%"}),e.appendChild(t),t.appendChild(n),t.appendChild(i),t.appendChild(this.percentageEl_),e},t.dispose=function(){this.partEls_=null,this.percentageEl_=null,r.prototype.dispose.call(this)},t.update=function(e){var h=this;this.requestNamedAnimationFrame("LoadProgressBar#update",function(){var e=h.player_.liveTracker,t=h.player_.buffered(),n=e&&e.isLive()?e.seekableEnd():h.player_.duration(),i=h.player_.bufferedEnd(),r=h.partEls_,s=ai(i,n);h.percent_!==s&&(h.el_.style.width=s,ne(h.percentageEl_,s),h.percent_=s);for(var o=0;o<t.length;o++){var a=t.start(o),l=t.end(o),c=r[o];c||(c=h.el_.appendChild(te()),r[o]=c),c.dataset.start===a&&c.dataset.end===l||(c.dataset.start=a,c.dataset.end=l,c.style.left=ai(a,i),c.style.width=ai(l-a,i))}for(var u=r.length;u>t.length;u--)h.el_.removeChild(r[u-1]);r.length=t.length})},e}(mt);mt.registerComponent("LoadProgressBar",li);var ci=function(i){function e(e,t){var n;return(n=i.call(this,e,t)||this).update=Ye($e(bt(n),n.update),30),n}Tt(e,i);var t=e.prototype;return t.createEl=function(){return i.prototype.createEl.call(this,"div",{className:"vjs-time-tooltip"},{"aria-hidden":"true"})},t.update=function(e,t,n){var i=ge(this.el_),r=ve(this.player_.el()),s=e.width*t;if(r&&i){var o=e.left-r.left+s,a=e.width-s+(r.right-e.right),l=i.width/2;o<l?l+=l-o:a<l&&(l=a),l<0?l=0:l>i.width&&(l=i.width),l=Math.round(l),this.el_.style.right="-"+l+"px",this.write(n)}},t.write=function(e){ne(this.el_,e)},t.updateTime=function(r,s,o,a){var l=this;this.requestNamedAnimationFrame("TimeTooltip#updateTime",function(){var e,t=l.player_.duration();if(l.player_.liveTracker&&l.player_.liveTracker.isLive()){var n=l.player_.liveTracker.liveWindow(),i=n-s*n;e=(i<1?"":"-")+Qn(i,n)}else e=Qn(o,t);l.update(r,s,e),a&&a()})},e}(mt);mt.registerComponent("TimeTooltip",ci);var ui=function(i){function e(e,t){var n;return(n=i.call(this,e,t)||this).update=Ye($e(bt(n),n.update),30),n}Tt(e,i);var t=e.prototype;return t.createEl=function(){return i.prototype.createEl.call(this,"div",{className:"vjs-play-progress vjs-slider-bar"},{"aria-hidden":"true"})},t.update=function(e,t){var n=this.getChild("timeTooltip");if(n){var i=this.player_.scrubbing()?this.player_.getCache().currentTime:this.player_.currentTime();n.updateTime(e,t,i)}},e}(mt);ui.prototype.options_={children:[]},q||N||ui.prototype.options_.children.push("timeTooltip"),mt.registerComponent("PlayProgressBar",ui);var hi=function(i){function e(e,t){var n;return(n=i.call(this,e,t)||this).update=Ye($e(bt(n),n.update),30),n}Tt(e,i);var t=e.prototype;return t.createEl=function(){return i.prototype.createEl.call(this,"div",{className:"vjs-mouse-display"})},t.update=function(e,t){var n=this,i=t*this.player_.duration();this.getChild("timeTooltip").updateTime(e,t,i,function(){n.el_.style.left=e.width*t+"px"})},e}(mt);hi.prototype.options_={children:["timeTooltip"]},mt.registerComponent("MouseTimeDisplay",hi);var di=function(s){function e(e,t){var n;return(n=s.call(this,e,t)||this).setEventHandlers_(),n}Tt(e,s);var t=e.prototype;return t.setEventHandlers_=function(){this.update_=$e(this,this.update),this.update=Ye(this.update_,30),this.on(this.player_,["ended","durationchange","timeupdate"],this.update),this.player_.liveTracker&&this.on(this.player_.liveTracker,"liveedgechange",this.update),this.updateInterval=null,this.on(this.player_,["playing"],this.enableInterval_),this.on(this.player_,["ended","pause","waiting"],this.disableInterval_),"hidden"in p&&"visibilityState"in p&&this.on(p,"visibilitychange",this.toggleVisibility_)},t.toggleVisibility_=function(e){p.hidden?this.disableInterval_(e):(this.enableInterval_(),this.update())},t.enableInterval_=function(){this.updateInterval||(this.updateInterval=this.setInterval(this.update,30))},t.disableInterval_=function(e){this.player_.liveTracker&&this.player_.liveTracker.isLive()&&e&&"ended"!==e.type||this.updateInterval&&(this.clearInterval(this.updateInterval),this.updateInterval=null)},t.createEl=function(){return s.prototype.createEl.call(this,"div",{className:"vjs-progress-holder"},{"aria-label":this.localize("Progress Bar")})},t.update=function(e){var i=this,r=s.prototype.update.call(this);return this.requestNamedAnimationFrame("SeekBar#update",function(){var e=i.player_.ended()?i.player_.duration():i.getCurrentTime_(),t=i.player_.liveTracker,n=i.player_.duration();t&&t.isLive()&&(n=i.player_.liveTracker.liveCurrentTime()),i.percent_!==r&&(i.el_.setAttribute("aria-valuenow",(100*r).toFixed(2)),i.percent_=r),i.currentTime_===e&&i.duration_===n||(i.el_.setAttribute("aria-valuetext",i.localize("progress bar timing: currentTime={1} duration={2}",[Qn(e,n),Qn(n,n)],"{1} of {2}")),i.currentTime_=e,i.duration_=n),i.bar&&i.bar.update(ve(i.el()),i.getProgress())}),r},t.getCurrentTime_=function(){return this.player_.scrubbing()?this.player_.getCache().currentTime:this.player_.currentTime()},t.getPercent=function(){var e,t=this.getCurrentTime_(),n=this.player_.liveTracker;return n&&n.isLive()?(e=(t-n.seekableStart())/n.liveWindow(),n.atLiveEdge()&&(e=1)):e=t/this.player_.duration(),e},t.handleMouseDown=function(e){Ce(e)&&(e.stopPropagation(),this.player_.scrubbing(!0),this.videoWasPlaying=!this.player_.paused(),this.player_.pause(),s.prototype.handleMouseDown.call(this,e))},t.handleMouseMove=function(e){if(Ce(e)){var t,n=this.calculateDistance(e),i=this.player_.liveTracker;if(i&&i.isLive()){if(.99<=n)return void i.seekToLiveEdge();var r=i.seekableStart(),s=i.liveCurrentTime();if(s<=(t=r+n*i.liveWindow())&&(t=s),t<=r&&(t=r+.1),t===1/0)return}else(t=n*this.player_.duration())===this.player_.duration()&&(t-=.1);this.player_.currentTime(t)}},t.enable=function(){s.prototype.enable.call(this);var e=this.getChild("mouseTimeDisplay");e&&e.show()},t.disable=function(){s.prototype.disable.call(this);var e=this.getChild("mouseTimeDisplay");e&&e.hide()},t.handleMouseUp=function(e){s.prototype.handleMouseUp.call(this,e),e&&e.stopPropagation(),this.player_.scrubbing(!1),this.player_.trigger({type:"timeupdate",target:this,manuallyTriggered:!0}),this.videoWasPlaying?At(this.player_.play()):this.update_()},t.stepForward=function(){this.player_.currentTime(this.player_.currentTime()+5)},t.stepBack=function(){this.player_.currentTime(this.player_.currentTime()-5)},t.handleAction=function(e){this.player_.paused()?this.player_.play():this.player_.pause()},t.handleKeyDown=function(e){if(Ot.isEventKey(e,"Space")||Ot.isEventKey(e,"Enter"))e.preventDefault(),e.stopPropagation(),this.handleAction(e);else if(Ot.isEventKey(e,"Home"))e.preventDefault(),e.stopPropagation(),this.player_.currentTime(0);else if(Ot.isEventKey(e,"End"))e.preventDefault(),e.stopPropagation(),this.player_.currentTime(this.player_.duration());else if(/^[0-9]$/.test(Ot(e))){e.preventDefault(),e.stopPropagation();var t=10*(Ot.codes[Ot(e)]-Ot.codes[0])/100;this.player_.currentTime(this.player_.duration()*t)}else Ot.isEventKey(e,"PgDn")?(e.preventDefault(),e.stopPropagation(),this.player_.currentTime(this.player_.currentTime()-60)):Ot.isEventKey(e,"PgUp")?(e.preventDefault(),e.stopPropagation(),this.player_.currentTime(this.player_.currentTime()+60)):s.prototype.handleKeyDown.call(this,e)},t.dispose=function(){this.disableInterval_(),this.off(this.player_,["ended","durationchange","timeupdate"],this.update),this.player_.liveTracker&&this.on(this.player_.liveTracker,"liveedgechange",this.update),this.off(this.player_,["playing"],this.enableInterval_),this.off(this.player_,["ended","pause","waiting"],this.disableInterval_),"hidden"in p&&"visibilityState"in p&&this.off(p,"visibilitychange",this.toggleVisibility_),s.prototype.dispose.call(this)},e}(oi);di.prototype.options_={children:["loadProgressBar","playProgressBar"],barName:"playProgressBar"},q||N||di.prototype.options_.children.splice(1,0,"mouseTimeDisplay"),mt.registerComponent("SeekBar",di);var pi=function(i){function e(e,t){var n;return(n=i.call(this,e,t)||this).handleMouseMove=Ye($e(bt(n),n.handleMouseMove),30),n.throttledHandleMouseSeek=Ye($e(bt(n),n.handleMouseSeek),30),n.enable(),n}Tt(e,i);var t=e.prototype;return t.createEl=function(){return i.prototype.createEl.call(this,"div",{className:"vjs-progress-control vjs-control"})},t.handleMouseMove=function(e){var t=this.getChild("seekBar");if(t){var n=t.getChild("playProgressBar"),i=t.getChild("mouseTimeDisplay");if(n||i){var r=t.el(),s=ge(r),o=_e(r,e).x;o=si(o,0,1),i&&i.update(s,o),n&&n.update(s,t.getProgress())}}},t.handleMouseSeek=function(e){var t=this.getChild("seekBar");t&&t.handleMouseMove(e)},t.enabled=function(){return this.enabled_},t.disable=function(){this.children().forEach(function(e){return e.disable&&e.disable()}),this.enabled()&&(this.off(["mousedown","touchstart"],this.handleMouseDown),this.off(this.el_,"mousemove",this.handleMouseMove),this.handleMouseUp(),this.addClass("disabled"),this.enabled_=!1)},t.enable=function(){this.children().forEach(function(e){return e.enable&&e.enable()}),this.enabled()||(this.on(["mousedown","touchstart"],this.handleMouseDown),this.on(this.el_,"mousemove",this.handleMouseMove),this.removeClass("disabled"),this.enabled_=!0)},t.handleMouseDown=function(e){var t=this.el_.ownerDocument,n=this.getChild("seekBar");n&&n.handleMouseDown(e),this.on(t,"mousemove",this.throttledHandleMouseSeek),this.on(t,"touchmove",this.throttledHandleMouseSeek),this.on(t,"mouseup",this.handleMouseUp),this.on(t,"touchend",this.handleMouseUp)},t.handleMouseUp=function(e){var t=this.el_.ownerDocument,n=this.getChild("seekBar");n&&n.handleMouseUp(e),this.off(t,"mousemove",this.throttledHandleMouseSeek),this.off(t,"touchmove",this.throttledHandleMouseSeek),this.off(t,"mouseup",this.handleMouseUp),this.off(t,"touchend",this.handleMouseUp)},e}(mt);pi.prototype.options_={children:["seekBar"]},mt.registerComponent("ProgressControl",pi);var fi=function(i){function e(e,t){var n;return(n=i.call(this,e,t)||this).on(e,["enterpictureinpicture","leavepictureinpicture"],n.handlePictureInPictureChange),n.on(e,["disablepictureinpicturechanged","loadedmetadata"],n.handlePictureInPictureEnabledChange),n.disable(),n}Tt(e,i);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-picture-in-picture-control "+i.prototype.buildCSSClass.call(this)},t.handlePictureInPictureEnabledChange=function(){p.pictureInPictureEnabled&&!1===this.player_.disablePictureInPicture()?this.enable():this.disable()},t.handlePictureInPictureChange=function(e){this.player_.isInPictureInPicture()?this.controlText("Exit Picture-in-Picture"):this.controlText("Picture-in-Picture"),this.handlePictureInPictureEnabledChange()},t.handleClick=function(e){this.player_.isInPictureInPicture()?this.player_.exitPictureInPicture():this.player_.requestPictureInPicture()},e}(Un);fi.prototype.controlText_="Picture-in-Picture",mt.registerComponent("PictureInPictureToggle",fi);var vi=function(i){function e(e,t){var n;return(n=i.call(this,e,t)||this).on(e,"fullscreenchange",n.handleFullscreenChange),!1===p[e.fsApi_.fullscreenEnabled]&&n.disable(),n}Tt(e,i);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-fullscreen-control "+i.prototype.buildCSSClass.call(this)},t.handleFullscreenChange=function(e){this.player_.isFullscreen()?this.controlText("Non-Fullscreen"):this.controlText("Fullscreen")},t.handleClick=function(e){this.player_.isFullscreen()?this.player_.exitFullscreen():this.player_.requestFullscreen()},e}(Un);vi.prototype.controlText_="Fullscreen",mt.registerComponent("FullscreenToggle",vi);var gi=function(e){function t(){return e.apply(this,arguments)||this}return Tt(t,e),t.prototype.createEl=function(){return e.prototype.createEl.call(this,"div",{className:"vjs-volume-level",innerHTML:'<span class="vjs-control-text"></span>'})},t}(mt);mt.registerComponent("VolumeLevel",gi);var _i=function(i){function e(e,t){var n;return(n=i.call(this,e,t)||this).on("slideractive",n.updateLastVolume_),n.on(e,"volumechange",n.updateARIAAttributes),e.ready(function(){return n.updateARIAAttributes()}),n}Tt(e,i);var t=e.prototype;return t.createEl=function(){return i.prototype.createEl.call(this,"div",{className:"vjs-volume-bar vjs-slider-bar"},{"aria-label":this.localize("Volume Level"),"aria-live":"polite"})},t.handleMouseDown=function(e){Ce(e)&&i.prototype.handleMouseDown.call(this,e)},t.handleMouseMove=function(e){Ce(e)&&(this.checkMuted(),this.player_.volume(this.calculateDistance(e)))},t.checkMuted=function(){this.player_.muted()&&this.player_.muted(!1)},t.getPercent=function(){return this.player_.muted()?0:this.player_.volume()},t.stepForward=function(){this.checkMuted(),this.player_.volume(this.player_.volume()+.1)},t.stepBack=function(){this.checkMuted(),this.player_.volume(this.player_.volume()-.1)},t.updateARIAAttributes=function(e){var t=this.player_.muted()?0:this.volumeAsPercentage_();this.el_.setAttribute("aria-valuenow",t),this.el_.setAttribute("aria-valuetext",t+"%")},t.volumeAsPercentage_=function(){return Math.round(100*this.player_.volume())},t.updateLastVolume_=function(){var e=this,t=this.player_.volume();this.one("sliderinactive",function(){0===e.player_.volume()&&e.player_.lastVolume_(t)})},e}(oi);_i.prototype.options_={children:["volumeLevel"],barName:"volumeLevel"},_i.prototype.playerEvent="volumechange",mt.registerComponent("VolumeBar",_i);var yi=function(i){function e(e,t){var n;return void 0===t&&(t={}),t.vertical=t.vertical||!1,"undefined"!=typeof t.volumeBar&&!k(t.volumeBar)||(t.volumeBar=t.volumeBar||{},t.volumeBar.vertical=t.vertical),n=i.call(this,e,t)||this,function(e,t){t.tech_&&!t.tech_.featuresVolumeControl&&e.addClass("vjs-hidden"),e.on(t,"loadstart",function(){t.tech_.featuresVolumeControl?e.removeClass("vjs-hidden"):e.addClass("vjs-hidden")})}(bt(n),e),n.throttledHandleMouseMove=Ye($e(bt(n),n.handleMouseMove),30),n.on("mousedown",n.handleMouseDown),n.on("touchstart",n.handleMouseDown),n.on(n.volumeBar,["focus","slideractive"],function(){n.volumeBar.addClass("vjs-slider-active"),n.addClass("vjs-slider-active"),n.trigger("slideractive")}),n.on(n.volumeBar,["blur","sliderinactive"],function(){n.volumeBar.removeClass("vjs-slider-active"),n.removeClass("vjs-slider-active"),n.trigger("sliderinactive")}),n}Tt(e,i);var t=e.prototype;return t.createEl=function(){var e="vjs-volume-horizontal";return this.options_.vertical&&(e="vjs-volume-vertical"),i.prototype.createEl.call(this,"div",{className:"vjs-volume-control vjs-control "+e})},t.handleMouseDown=function(e){var t=this.el_.ownerDocument;this.on(t,"mousemove",this.throttledHandleMouseMove),this.on(t,"touchmove",this.throttledHandleMouseMove),this.on(t,"mouseup",this.handleMouseUp),this.on(t,"touchend",this.handleMouseUp)},t.handleMouseUp=function(e){var t=this.el_.ownerDocument;this.off(t,"mousemove",this.throttledHandleMouseMove),this.off(t,"touchmove",this.throttledHandleMouseMove),this.off(t,"mouseup",this.handleMouseUp),this.off(t,"touchend",this.handleMouseUp)},t.handleMouseMove=function(e){this.volumeBar.handleMouseMove(e)},e}(mt);yi.prototype.options_={children:["volumeBar"]},mt.registerComponent("VolumeControl",yi);var mi=function(i){function e(e,t){var n;return n=i.call(this,e,t)||this,function(e,t){t.tech_&&!t.tech_.featuresMuteControl&&e.addClass("vjs-hidden"),e.on(t,"loadstart",function(){t.tech_.featuresMuteControl?e.removeClass("vjs-hidden"):e.addClass("vjs-hidden")})}(bt(n),e),n.on(e,["loadstart","volumechange"],n.update),n}Tt(e,i);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-mute-control "+i.prototype.buildCSSClass.call(this)},t.handleClick=function(e){var t=this.player_.volume(),n=this.player_.lastVolume_();if(0===t){var i=n<.1?.1:n;this.player_.volume(i),this.player_.muted(!1)}else this.player_.muted(!this.player_.muted())},t.update=function(e){this.updateIcon_(),this.updateControlText_()},t.updateIcon_=function(){var e=this.player_.volume(),t=3;q&&this.player_.tech_&&this.player_.tech_.el_&&this.player_.muted(this.player_.tech_.el_.muted),0===e||this.player_.muted()?t=0:e<.33?t=1:e<.67&&(t=2);for(var n=0;n<4;n++)oe(this.el_,"vjs-vol-"+n);se(this.el_,"vjs-vol-"+t)},t.updateControlText_=function(){var e=this.player_.muted()||0===this.player_.volume()?"Unmute":"Mute";this.controlText()!==e&&this.controlText(e)},e}(Un);mi.prototype.controlText_="Mute",mt.registerComponent("MuteToggle",mi);var bi=function(i){function e(e,t){var n;return void 0===t&&(t={}),"undefined"!=typeof t.inline?t.inline=t.inline:t.inline=!0,"undefined"!=typeof t.volumeControl&&!k(t.volumeControl)||(t.volumeControl=t.volumeControl||{},t.volumeControl.vertical=!t.inline),(n=i.call(this,e,t)||this).on(e,["loadstart"],n.volumePanelState_),n.on(n.muteToggle,"keyup",n.handleKeyPress),n.on(n.volumeControl,"keyup",n.handleVolumeControlKeyUp),n.on("keydown",n.handleKeyPress),n.on("mouseover",n.handleMouseOver),n.on("mouseout",n.handleMouseOut),n.on(n.volumeControl,["slideractive"],n.sliderActive_),n.on(n.volumeControl,["sliderinactive"],n.sliderInactive_),n}Tt(e,i);var t=e.prototype;return t.sliderActive_=function(){this.addClass("vjs-slider-active")},t.sliderInactive_=function(){this.removeClass("vjs-slider-active")},t.volumePanelState_=function(){this.volumeControl.hasClass("vjs-hidden")&&this.muteToggle.hasClass("vjs-hidden")&&this.addClass("vjs-hidden"),this.volumeControl.hasClass("vjs-hidden")&&!this.muteToggle.hasClass("vjs-hidden")&&this.addClass("vjs-mute-toggle-only")},t.createEl=function(){var e="vjs-volume-panel-horizontal";return this.options_.inline||(e="vjs-volume-panel-vertical"),i.prototype.createEl.call(this,"div",{className:"vjs-volume-panel vjs-control "+e})},t.dispose=function(){this.handleMouseOut(),i.prototype.dispose.call(this)},t.handleVolumeControlKeyUp=function(e){Ot.isEventKey(e,"Esc")&&this.muteToggle.focus()},t.handleMouseOver=function(e){this.addClass("vjs-hover"),We(p,"keyup",$e(this,this.handleKeyPress))},t.handleMouseOut=function(e){this.removeClass("vjs-hover"),Ue(p,"keyup",$e(this,this.handleKeyPress))},t.handleKeyPress=function(e){Ot.isEventKey(e,"Esc")&&this.handleMouseOut()},e}(mt);bi.prototype.options_={children:["muteToggle","volumeControl"]},mt.registerComponent("VolumePanel",bi);var Ti=function(i){function e(e,t){var n;return n=i.call(this,e,t)||this,t&&(n.menuButton_=t.menuButton),n.focusedChild_=-1,n.on("keydown",n.handleKeyDown),n.boundHandleBlur_=$e(bt(n),n.handleBlur),n.boundHandleTapClick_=$e(bt(n),n.handleTapClick),n}Tt(e,i);var t=e.prototype;return t.addEventListenerForItem=function(e){e instanceof mt&&(this.on(e,"blur",this.boundHandleBlur_),this.on(e,["tap","click"],this.boundHandleTapClick_))},t.removeEventListenerForItem=function(e){e instanceof mt&&(this.off(e,"blur",this.boundHandleBlur_),this.off(e,["tap","click"],this.boundHandleTapClick_))},t.removeChild=function(e){"string"==typeof e&&(e=this.getChild(e)),this.removeEventListenerForItem(e),i.prototype.removeChild.call(this,e)},t.addItem=function(e){var t=this.addChild(e);t&&this.addEventListenerForItem(t)},t.createEl=function(){var e=this.options_.contentElType||"ul";this.contentEl_=te(e,{className:"vjs-menu-content"}),this.contentEl_.setAttribute("role","menu");var t=i.prototype.createEl.call(this,"div",{append:this.contentEl_,className:"vjs-menu"});return t.appendChild(this.contentEl_),We(t,"click",function(e){e.preventDefault(),e.stopImmediatePropagation()}),t},t.dispose=function(){this.contentEl_=null,this.boundHandleBlur_=null,this.boundHandleTapClick_=null,i.prototype.dispose.call(this)},t.handleBlur=function(e){var t=e.relatedTarget||p.activeElement;if(!this.children().some(function(e){return e.el()===t})){var n=this.menuButton_;n&&n.buttonPressed_&&t!==n.el().firstChild&&n.unpressButton()}},t.handleTapClick=function(t){if(this.menuButton_){this.menuButton_.unpressButton();var e=this.children();if(!Array.isArray(e))return;var n=e.filter(function(e){return e.el()===t.target})[0];if(!n)return;"CaptionSettingsMenuItem"!==n.name()&&this.menuButton_.focus()}},t.handleKeyDown=function(e){Ot.isEventKey(e,"Left")||Ot.isEventKey(e,"Down")?(e.preventDefault(),e.stopPropagation(),this.stepForward()):(Ot.isEventKey(e,"Right")||Ot.isEventKey(e,"Up"))&&(e.preventDefault(),e.stopPropagation(),this.stepBack())},t.stepForward=function(){var e=0;void 0!==this.focusedChild_&&(e=this.focusedChild_+1),this.focus(e)},t.stepBack=function(){var e=0;void 0!==this.focusedChild_&&(e=this.focusedChild_-1),this.focus(e)},t.focus=function(e){void 0===e&&(e=0);var t=this.children().slice();t.length&&t[0].hasClass("vjs-menu-title")&&t.shift(),0<t.length&&(e<0?e=0:e>=t.length&&(e=t.length-1),t[this.focusedChild_=e].el_.focus())},e}(mt);mt.registerComponent("Menu",Ti);var ki=function(r){function e(e,t){var n;void 0===t&&(t={}),(n=r.call(this,e,t)||this).menuButton_=new Un(e,t),n.menuButton_.controlText(n.controlText_),n.menuButton_.el_.setAttribute("aria-haspopup","true");var i=Un.prototype.buildCSSClass();return n.menuButton_.el_.className=n.buildCSSClass()+" "+i,n.menuButton_.removeClass("vjs-control"),n.addChild(n.menuButton_),n.update(),n.enabled_=!0,n.on(n.menuButton_,"tap",n.handleClick),n.on(n.menuButton_,"click",n.handleClick),n.on(n.menuButton_,"keydown",n.handleKeyDown),n.on(n.menuButton_,"mouseenter",function(){n.addClass("vjs-hover"),n.menu.show(),We(p,"keyup",$e(bt(n),n.handleMenuKeyUp))}),n.on("mouseleave",n.handleMouseLeave),n.on("keydown",n.handleSubmenuKeyDown),n}Tt(e,r);var t=e.prototype;return t.update=function(){var e=this.createMenu();this.menu&&(this.menu.dispose(),this.removeChild(this.menu)),this.menu=e,this.addChild(e),this.buttonPressed_=!1,this.menuButton_.el_.setAttribute("aria-expanded","false"),this.items&&this.items.length<=this.hideThreshold_?this.hide():this.show()},t.createMenu=function(){var e=new Ti(this.player_,{menuButton:this});if(this.hideThreshold_=0,this.options_.title){var t=te("li",{className:"vjs-menu-title",innerHTML:pt(this.options_.title),tabIndex:-1});this.hideThreshold_+=1;var n=new mt(this.player_,{el:t});e.addItem(n)}if(this.items=this.createItems(),this.items)for(var i=0;i<this.items.length;i++)e.addItem(this.items[i]);return e},t.createItems=function(){},t.createEl=function(){return r.prototype.createEl.call(this,"div",{className:this.buildWrapperCSSClass()},{})},t.buildWrapperCSSClass=function(){var e="vjs-menu-button";return!0===this.options_.inline?e+="-inline":e+="-popup","vjs-menu-button "+e+" "+Un.prototype.buildCSSClass()+" "+r.prototype.buildCSSClass.call(this)},t.buildCSSClass=function(){var e="vjs-menu-button";return!0===this.options_.inline?e+="-inline":e+="-popup","vjs-menu-button "+e+" "+r.prototype.buildCSSClass.call(this)},t.controlText=function(e,t){return void 0===t&&(t=this.menuButton_.el()),this.menuButton_.controlText(e,t)},t.dispose=function(){this.handleMouseLeave(),r.prototype.dispose.call(this)},t.handleClick=function(e){this.buttonPressed_?this.unpressButton():this.pressButton()},t.handleMouseLeave=function(e){this.removeClass("vjs-hover"),Ue(p,"keyup",$e(this,this.handleMenuKeyUp))},t.focus=function(){this.menuButton_.focus()},t.blur=function(){this.menuButton_.blur()},t.handleKeyDown=function(e){Ot.isEventKey(e,"Esc")||Ot.isEventKey(e,"Tab")?(this.buttonPressed_&&this.unpressButton(),Ot.isEventKey(e,"Tab")||(e.preventDefault(),this.menuButton_.focus())):(Ot.isEventKey(e,"Up")||Ot.isEventKey(e,"Down"))&&(this.buttonPressed_||(e.preventDefault(),this.pressButton()))},t.handleMenuKeyUp=function(e){(Ot.isEventKey(e,"Esc")||Ot.isEventKey(e,"Tab"))&&this.removeClass("vjs-hover")},t.handleSubmenuKeyPress=function(e){this.handleSubmenuKeyDown(e)},t.handleSubmenuKeyDown=function(e){(Ot.isEventKey(e,"Esc")||Ot.isEventKey(e,"Tab"))&&(this.buttonPressed_&&this.unpressButton(),Ot.isEventKey(e,"Tab")||(e.preventDefault(),this.menuButton_.focus()))},t.pressButton=function(){if(this.enabled_){if(this.buttonPressed_=!0,this.menu.show(),this.menu.lockShowing(),this.menuButton_.el_.setAttribute("aria-expanded","true"),q&&Z())return;this.menu.focus()}},t.unpressButton=function(){this.enabled_&&(this.buttonPressed_=!1,this.menu.unlockShowing(),this.menu.hide(),this.menuButton_.el_.setAttribute("aria-expanded","false"))},t.disable=function(){this.unpressButton(),this.enabled_=!1,this.addClass("vjs-disabled"),this.menuButton_.disable()},t.enable=function(){this.enabled_=!0,this.removeClass("vjs-disabled"),this.menuButton_.enable()},e}(mt);mt.registerComponent("MenuButton",ki);var Ci=function(s){function e(e,t){var n,i=t.tracks;if((n=s.call(this,e,t)||this).items.length<=1&&n.hide(),!i)return bt(n);var r=$e(bt(n),n.update);return i.addEventListener("removetrack",r),i.addEventListener("addtrack",r),i.addEventListener("labelchange",r),n.player_.on("ready",r),n.player_.on("dispose",function(){i.removeEventListener("removetrack",r),i.removeEventListener("addtrack",r),i.removeEventListener("labelchange",r)}),n}return Tt(e,s),e}(ki);mt.registerComponent("TrackButton",Ci);var Ei=["Tab","Esc","Up","Down","Right","Left"],Si=function(i){function e(e,t){var n;return(n=i.call(this,e,t)||this).selectable=t.selectable,n.isSelected_=t.selected||!1,n.multiSelectable=t.multiSelectable,n.selected(n.isSelected_),n.selectable?n.multiSelectable?n.el_.setAttribute("role","menuitemcheckbox"):n.el_.setAttribute("role","menuitemradio"):n.el_.setAttribute("role","menuitem"),n}Tt(e,i);var t=e.prototype;return t.createEl=function(e,t,n){return this.nonIconControl=!0,i.prototype.createEl.call(this,"li",b({className:"vjs-menu-item",innerHTML:'<span class="vjs-menu-item-text">'+this.localize(this.options_.label)+"</span>",tabIndex:-1},t),n)},t.handleKeyDown=function(t){Ei.some(function(e){return Ot.isEventKey(t,e)})||i.prototype.handleKeyDown.call(this,t)},t.handleClick=function(e){this.selected(!0)},t.selected=function(e){this.selectable&&(e?(this.addClass("vjs-selected"),this.el_.setAttribute("aria-checked","true"),this.controlText(", selected"),this.isSelected_=!0):(this.removeClass("vjs-selected"),this.el_.setAttribute("aria-checked","false"),this.controlText(""),this.isSelected_=!1))},e}(Fn);mt.registerComponent("MenuItem",Si);var wi=function(l){function e(e,t){var i,n=t.track,r=e.textTracks();t.label=n.label||n.language||"Unknown",t.selected="showing"===n.mode,(i=l.call(this,e,t)||this).track=n,i.kinds=(t.kinds||[t.kind||i.track.kind]).filter(Boolean);function s(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];i.handleTracksChange.apply(bt(i),t)}function o(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];i.handleSelectedLanguageChange.apply(bt(i),t)}var a;e.on(["loadstart","texttrackchange"],s),r.addEventListener("change",s),r.addEventListener("selectedlanguagechange",o),i.on("dispose",function(){e.off(["loadstart","texttrackchange"],s),r.removeEventListener("change",s),r.removeEventListener("selectedlanguagechange",o)}),void 0===r.onchange&&i.on(["tap","click"],function(){if("object"!=typeof d.Event)try{a=new d.Event("change")}catch(e){}a||(a=p.createEvent("Event")).initEvent("change",!0,!0),r.dispatchEvent(a)});return i.handleTracksChange(),i}Tt(e,l);var t=e.prototype;return t.handleClick=function(e){var t=this.track,n=this.player_.textTracks();if(l.prototype.handleClick.call(this,e),n)for(var i=0;i<n.length;i++){var r=n[i];-1!==this.kinds.indexOf(r.kind)&&(r===t?"showing"!==r.mode&&(r.mode="showing"):"disabled"!==r.mode&&(r.mode="disabled"))}},t.handleTracksChange=function(e){var t="showing"===this.track.mode;t!==this.isSelected_&&this.selected(t)},t.handleSelectedLanguageChange=function(e){if("showing"===this.track.mode){var t=this.player_.cache_.selectedLanguage;if(t&&t.enabled&&t.language===this.track.language&&t.kind!==this.track.kind)return;this.player_.cache_.selectedLanguage={enabled:!0,language:this.track.language,kind:this.track.kind}}},t.dispose=function(){this.track=null,l.prototype.dispose.call(this)},e}(Si);mt.registerComponent("TextTrackMenuItem",wi);var xi=function(n){function e(e,t){return t.track={player:e,kind:t.kind,kinds:t.kinds,default:!1,mode:"disabled"},t.kinds||(t.kinds=[t.kind]),t.label?t.track.label=t.label:t.track.label=t.kinds.join(" and ")+" off",t.selectable=!0,t.multiSelectable=!1,n.call(this,e,t)||this}Tt(e,n);var t=e.prototype;return t.handleTracksChange=function(e){for(var t=this.player().textTracks(),n=!0,i=0,r=t.length;i<r;i++){var s=t[i];if(-1<this.options_.kinds.indexOf(s.kind)&&"showing"===s.mode){n=!1;break}}n!==this.isSelected_&&this.selected(n)},t.handleSelectedLanguageChange=function(e){for(var t=this.player().textTracks(),n=!0,i=0,r=t.length;i<r;i++){var s=t[i];if(-1<["captions","descriptions","subtitles"].indexOf(s.kind)&&"showing"===s.mode){n=!1;break}}n&&(this.player_.cache_.selectedLanguage={enabled:!1})},e}(wi);mt.registerComponent("OffTextTrackMenuItem",xi);var Pi=function(n){function e(e,t){return void 0===t&&(t={}),t.tracks=e.textTracks(),n.call(this,e,t)||this}return Tt(e,n),e.prototype.createItems=function(e,t){var n;void 0===e&&(e=[]),void 0===t&&(t=wi),this.label_&&(n=this.label_+" off"),e.push(new xi(this.player_,{kinds:this.kinds_,kind:this.kind_,label:n})),this.hideThreshold_+=1;var i=this.player_.textTracks();Array.isArray(this.kinds_)||(this.kinds_=[this.kind_]);for(var r=0;r<i.length;r++){var s=i[r];if(-1<this.kinds_.indexOf(s.kind)){var o=new t(this.player_,{track:s,kinds:this.kinds_,kind:this.kind_,selectable:!0,multiSelectable:!1});o.addClass("vjs-"+s.kind+"-menu-item"),e.push(o)}}return e},e}(Ci);mt.registerComponent("TextTrackButton",Pi);var ji=function(o){function e(e,t){var n,i=t.track,r=t.cue,s=e.currentTime();return t.selectable=!0,t.multiSelectable=!1,t.label=r.text,t.selected=r.startTime<=s&&s<r.endTime,(n=o.call(this,e,t)||this).track=i,n.cue=r,i.addEventListener("cuechange",$e(bt(n),n.update)),n}Tt(e,o);var t=e.prototype;return t.handleClick=function(e){o.prototype.handleClick.call(this),this.player_.currentTime(this.cue.startTime),this.update(this.cue.startTime)},t.update=function(e){var t=this.cue,n=this.player_.currentTime();this.selected(t.startTime<=n&&n<t.endTime)},e}(Si);mt.registerComponent("ChaptersTrackMenuItem",ji);var Ai=function(i){function e(e,t,n){return i.call(this,e,t,n)||this}Tt(e,i);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-chapters-button "+i.prototype.buildCSSClass.call(this)},t.buildWrapperCSSClass=function(){return"vjs-chapters-button "+i.prototype.buildWrapperCSSClass.call(this)},t.update=function(e){this.track_&&(!e||"addtrack"!==e.type&&"removetrack"!==e.type)||this.setTrack(this.findChaptersTrack()),i.prototype.update.call(this)},t.setTrack=function(e){if(this.track_!==e){if(this.updateHandler_||(this.updateHandler_=this.update.bind(this)),this.track_){var t=this.player_.remoteTextTrackEls().getTrackElementByTrack_(this.track_);t&&t.removeEventListener("load",this.updateHandler_),this.track_=null}if(this.track_=e,this.track_){this.track_.mode="hidden";var n=this.player_.remoteTextTrackEls().getTrackElementByTrack_(this.track_);n&&n.addEventListener("load",this.updateHandler_)}}},t.findChaptersTrack=function(){for(var e=this.player_.textTracks()||[],t=e.length-1;0<=t;t--){var n=e[t];if(n.kind===this.kind_)return n}},t.getMenuCaption=function(){return this.track_&&this.track_.label?this.track_.label:this.localize(pt(this.kind_))},t.createMenu=function(){return this.options_.title=this.getMenuCaption(),i.prototype.createMenu.call(this)},t.createItems=function(){var e=[];if(!this.track_)return e;var t=this.track_.cues;if(!t)return e;for(var n=0,i=t.length;n<i;n++){var r=t[n],s=new ji(this.player_,{track:this.track_,cue:r});e.push(s)}return e},e}(Pi);Ai.prototype.kind_="chapters",Ai.prototype.controlText_="Chapters",mt.registerComponent("ChaptersButton",Ai);var Ii=function(o){function e(e,t,n){var i;i=o.call(this,e,t,n)||this;var r=e.textTracks(),s=$e(bt(i),i.handleTracksChange);return r.addEventListener("change",s),i.on("dispose",function(){r.removeEventListener("change",s)}),i}Tt(e,o);var t=e.prototype;return t.handleTracksChange=function(e){for(var t=this.player().textTracks(),n=!1,i=0,r=t.length;i<r;i++){var s=t[i];if(s.kind!==this.kind_&&"showing"===s.mode){n=!0;break}}n?this.disable():this.enable()},t.buildCSSClass=function(){return"vjs-descriptions-button "+o.prototype.buildCSSClass.call(this)},t.buildWrapperCSSClass=function(){return"vjs-descriptions-button "+o.prototype.buildWrapperCSSClass.call(this)},e}(Pi);Ii.prototype.kind_="descriptions",Ii.prototype.controlText_="Descriptions",mt.registerComponent("DescriptionsButton",Ii);var Li=function(i){function e(e,t,n){return i.call(this,e,t,n)||this}Tt(e,i);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-subtitles-button "+i.prototype.buildCSSClass.call(this)},t.buildWrapperCSSClass=function(){return"vjs-subtitles-button "+i.prototype.buildWrapperCSSClass.call(this)},e}(Pi);Li.prototype.kind_="subtitles",Li.prototype.controlText_="Subtitles",mt.registerComponent("SubtitlesButton",Li);var Ni=function(i){function e(e,t){var n;return t.track={player:e,kind:t.kind,label:t.kind+" settings",selectable:!1,default:!1,mode:"disabled"},t.selectable=!1,t.name="CaptionSettingsMenuItem",(n=i.call(this,e,t)||this).addClass("vjs-texttrack-settings"),n.controlText(", opens "+t.kind+" settings dialog"),n}return Tt(e,i),e.prototype.handleClick=function(e){this.player().getChild("textTrackSettings").open()},e}(wi);mt.registerComponent("CaptionSettingsMenuItem",Ni);var Oi=function(i){function e(e,t,n){return i.call(this,e,t,n)||this}Tt(e,i);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-captions-button "+i.prototype.buildCSSClass.call(this)},t.buildWrapperCSSClass=function(){return"vjs-captions-button "+i.prototype.buildWrapperCSSClass.call(this)},t.createItems=function(){var e=[];return this.player().tech_&&this.player().tech_.featuresNativeTextTracks||!this.player().getChild("textTrackSettings")||(e.push(new Ni(this.player_,{kind:this.kind_})),this.hideThreshold_+=1),i.prototype.createItems.call(this,e)},e}(Pi);Oi.prototype.kind_="captions",Oi.prototype.controlText_="Captions",mt.registerComponent("CaptionsButton",Oi);var Mi=function(r){function e(){return r.apply(this,arguments)||this}return Tt(e,r),e.prototype.createEl=function(e,t,n){var i='<span class="vjs-menu-item-text">'+this.localize(this.options_.label);return"captions"===this.options_.track.kind&&(i+='\n        <span aria-hidden="true" class="vjs-icon-placeholder"></span>\n        <span class="vjs-control-text"> '+this.localize("Captions")+"</span>\n      "),i+="</span>",r.prototype.createEl.call(this,e,b({innerHTML:i},t),n)},e}(wi);mt.registerComponent("SubsCapsMenuItem",Mi);var Di=function(i){function e(e,t){var n;return void 0===t&&(t={}),(n=i.call(this,e,t)||this).label_="subtitles",-1<["en","en-us","en-ca","fr-ca"].indexOf(n.player_.language_)&&(n.label_="captions"),n.menuButton_.controlText(pt(n.label_)),n}Tt(e,i);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-subs-caps-button "+i.prototype.buildCSSClass.call(this)},t.buildWrapperCSSClass=function(){return"vjs-subs-caps-button "+i.prototype.buildWrapperCSSClass.call(this)},t.createItems=function(){var e=[];return this.player().tech_&&this.player().tech_.featuresNativeTextTracks||!this.player().getChild("textTrackSettings")||(e.push(new Ni(this.player_,{kind:this.label_})),this.hideThreshold_+=1),e=i.prototype.createItems.call(this,e,Mi)},e}(Pi);Di.prototype.kinds_=["captions","subtitles"],Di.prototype.controlText_="Subtitles",mt.registerComponent("SubsCapsButton",Di);var Fi=function(o){function e(e,t){var i,n=t.track,r=e.audioTracks();t.label=n.label||n.language||"Unknown",t.selected=n.enabled,(i=o.call(this,e,t)||this).track=n,i.addClass("vjs-"+n.kind+"-menu-item");function s(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];i.handleTracksChange.apply(bt(i),t)}return r.addEventListener("change",s),i.on("dispose",function(){r.removeEventListener("change",s)}),i}Tt(e,o);var t=e.prototype;return t.createEl=function(e,t,n){var i='<span class="vjs-menu-item-text">'+this.localize(this.options_.label);return"main-desc"===this.options_.track.kind&&(i+='\n        <span aria-hidden="true" class="vjs-icon-placeholder"></span>\n        <span class="vjs-control-text"> '+this.localize("Descriptions")+"</span>\n      "),i+="</span>",o.prototype.createEl.call(this,e,b({innerHTML:i},t),n)},t.handleClick=function(e){var t=this.player_.audioTracks();o.prototype.handleClick.call(this,e);for(var n=0;n<t.length;n++){var i=t[n];i.enabled=i===this.track}},t.handleTracksChange=function(e){this.selected(this.track.enabled)},e}(Si);mt.registerComponent("AudioTrackMenuItem",Fi);var Ri=function(n){function e(e,t){return void 0===t&&(t={}),t.tracks=e.audioTracks(),n.call(this,e,t)||this}Tt(e,n);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-audio-button "+n.prototype.buildCSSClass.call(this)},t.buildWrapperCSSClass=function(){return"vjs-audio-button "+n.prototype.buildWrapperCSSClass.call(this)},t.createItems=function(e){void 0===e&&(e=[]),this.hideThreshold_=1;for(var t=this.player_.audioTracks(),n=0;n<t.length;n++){var i=t[n];e.push(new Fi(this.player_,{track:i,selectable:!0,multiSelectable:!1}))}return e},e}(Ci);Ri.prototype.controlText_="Audio Track",mt.registerComponent("AudioTrackButton",Ri);var Bi=function(s){function e(e,t){var n,i=t.rate,r=parseFloat(i,10);return t.label=i,t.selected=1===r,t.selectable=!0,t.multiSelectable=!1,(n=s.call(this,e,t)||this).label=i,n.rate=r,n.on(e,"ratechange",n.update),n}Tt(e,s);var t=e.prototype;return t.handleClick=function(e){s.prototype.handleClick.call(this),this.player().playbackRate(this.rate)},t.update=function(e){this.selected(this.player().playbackRate()===this.rate)},e}(Si);Bi.prototype.contentElType="button",mt.registerComponent("PlaybackRateMenuItem",Bi);var Hi=function(i){function e(e,t){var n;return(n=i.call(this,e,t)||this).updateVisibility(),n.updateLabel(),n.on(e,"loadstart",n.updateVisibility),n.on(e,"ratechange",n.updateLabel),n}Tt(e,i);var t=e.prototype;return t.createEl=function(){var e=i.prototype.createEl.call(this);return this.labelEl_=te("div",{className:"vjs-playback-rate-value",innerHTML:"1x"}),e.appendChild(this.labelEl_),e},t.dispose=function(){this.labelEl_=null,i.prototype.dispose.call(this)},t.buildCSSClass=function(){return"vjs-playback-rate "+i.prototype.buildCSSClass.call(this)},t.buildWrapperCSSClass=function(){return"vjs-playback-rate "+i.prototype.buildWrapperCSSClass.call(this)},t.createMenu=function(){var e=new Ti(this.player()),t=this.playbackRates();if(t)for(var n=t.length-1;0<=n;n--)e.addChild(new Bi(this.player(),{rate:t[n]+"x"}));return e},t.updateARIAAttributes=function(){this.el().setAttribute("aria-valuenow",this.player().playbackRate())},t.handleClick=function(e){for(var t=this.player().playbackRate(),n=this.playbackRates(),i=n[0],r=0;r<n.length;r++)if(n[r]>t){i=n[r];break}this.player().playbackRate(i)},t.playbackRates=function(){return this.options_.playbackRates||this.options_.playerOptions&&this.options_.playerOptions.playbackRates},t.playbackRateSupported=function(){return this.player().tech_&&this.player().tech_.featuresPlaybackRate&&this.playbackRates()&&0<this.playbackRates().length},t.updateVisibility=function(e){this.playbackRateSupported()?this.removeClass("vjs-hidden"):this.addClass("vjs-hidden")},t.updateLabel=function(e){this.playbackRateSupported()&&(this.labelEl_.innerHTML=this.player().playbackRate()+"x")},e}(ki);Hi.prototype.controlText_="Playback Rate",mt.registerComponent("PlaybackRateMenuButton",Hi);var Vi=function(e){function t(){return e.apply(this,arguments)||this}Tt(t,e);var n=t.prototype;return n.buildCSSClass=function(){return"vjs-spacer "+e.prototype.buildCSSClass.call(this)},n.createEl=function(){return e.prototype.createEl.call(this,"div",{className:this.buildCSSClass()})},t}(mt);mt.registerComponent("Spacer",Vi);var Ki=function(t){function e(){return t.apply(this,arguments)||this}Tt(e,t);var n=e.prototype;return n.buildCSSClass=function(){return"vjs-custom-control-spacer "+t.prototype.buildCSSClass.call(this)},n.createEl=function(){var e=t.prototype.createEl.call(this,{className:this.buildCSSClass()});return e.innerHTML=" ",e},e}(Vi);mt.registerComponent("CustomControlSpacer",Ki);var zi=function(e){function t(){return e.apply(this,arguments)||this}return Tt(t,e),t.prototype.createEl=function(){return e.prototype.createEl.call(this,"div",{className:"vjs-control-bar",dir:"ltr"})},t}(mt);zi.prototype.options_={children:["playToggle","volumePanel","currentTimeDisplay","timeDivider","durationDisplay","progressControl","liveDisplay","seekToLive","remainingTimeDisplay","customControlSpacer","playbackRateMenuButton","chaptersButton","descriptionsButton","subsCapsButton","audioTrackButton","fullscreenToggle"]},"exitPictureInPicture"in p&&zi.prototype.options_.children.splice(zi.prototype.options_.children.length-1,0,"pictureInPictureToggle"),mt.registerComponent("ControlBar",zi);var Wi=function(i){function e(e,t){var n;return(n=i.call(this,e,t)||this).on(e,"error",n.open),n}Tt(e,i);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-error-display "+i.prototype.buildCSSClass.call(this)},t.content=function(){var e=this.player().error();return e?this.localize(e.message):""},e}(Dt);Wi.prototype.options_=u({},Dt.prototype.options_,{pauseOnOpen:!1,fillAlways:!0,temporary:!1,uncloseable:!0}),mt.registerComponent("ErrorDisplay",Wi);var Ui="vjs-text-track-settings",qi=["#000","Black"],Xi=["#00F","Blue"],Gi=["#0FF","Cyan"],$i=["#0F0","Green"],Yi=["#F0F","Magenta"],Qi=["#F00","Red"],Ji=["#FFF","White"],Zi=["#FF0","Yellow"],er=["1","Opaque"],tr=["0.5","Semi-Transparent"],nr=["0","Transparent"],ir={backgroundColor:{selector:".vjs-bg-color > select",id:"captions-background-color-%s",label:"Color",options:[qi,Ji,Qi,$i,Xi,Zi,Yi,Gi]},backgroundOpacity:{selector:".vjs-bg-opacity > select",id:"captions-background-opacity-%s",label:"Transparency",options:[er,tr,nr]},color:{selector:".vjs-fg-color > select",id:"captions-foreground-color-%s",label:"Color",options:[Ji,qi,Qi,$i,Xi,Zi,Yi,Gi]},edgeStyle:{selector:".vjs-edge-style > select",id:"%s",label:"Text Edge Style",options:[["none","None"],["raised","Raised"],["depressed","Depressed"],["uniform","Uniform"],["dropshadow","Dropshadow"]]},fontFamily:{selector:".vjs-font-family > select",id:"captions-font-family-%s",label:"Font Family",options:[["proportionalSansSerif","Proportional Sans-Serif"],["monospaceSansSerif","Monospace Sans-Serif"],["proportionalSerif","Proportional Serif"],["monospaceSerif","Monospace Serif"],["casual","Casual"],["script","Script"],["small-caps","Small Caps"]]},fontPercent:{selector:".vjs-font-percent > select",id:"captions-font-size-%s",label:"Font Size",options:[["0.50","50%"],["0.75","75%"],["1.00","100%"],["1.25","125%"],["1.50","150%"],["1.75","175%"],["2.00","200%"],["3.00","300%"],["4.00","400%"]],default:2,parser:function(e){return"1.00"===e?null:Number(e)}},textOpacity:{selector:".vjs-text-opacity > select",id:"captions-foreground-opacity-%s",label:"Transparency",options:[er,tr]},windowColor:{selector:".vjs-window-color > select",id:"captions-window-color-%s",label:"Color"},windowOpacity:{selector:".vjs-window-opacity > select",id:"captions-window-opacity-%s",label:"Transparency",options:[nr,tr,er]}};function rr(e,t){if(t&&(e=t(e)),e&&"none"!==e)return e}ir.windowColor.options=ir.backgroundColor.options;var sr=function(i){function e(e,t){var n;return t.temporary=!1,(n=i.call(this,e,t)||this).updateDisplay=$e(bt(n),n.updateDisplay),n.fill(),n.hasBeenOpened_=n.hasBeenFilled_=!0,n.endDialog=te("p",{className:"vjs-control-text",textContent:n.localize("End of dialog window.")}),n.el().appendChild(n.endDialog),n.setDefaults(),void 0===t.persistTextTrackSettings&&(n.options_.persistTextTrackSettings=n.options_.playerOptions.persistTextTrackSettings),n.on(n.$(".vjs-done-button"),"click",function(){n.saveSettings(),n.close()}),n.on(n.$(".vjs-default-button"),"click",function(){n.setDefaults(),n.updateDisplay()}),m(ir,function(e){n.on(n.$(e.selector),"change",n.updateDisplay)}),n.options_.persistTextTrackSettings&&n.restoreSettings(),n}Tt(e,i);var t=e.prototype;return t.dispose=function(){this.endDialog=null,i.prototype.dispose.call(this)},t.createElSelect_=function(e,t,n){var i=this;void 0===t&&(t=""),void 0===n&&(n="label");var r=ir[e],s=r.id.replace("%s",this.id_),o=[t,s].join(" ").trim();return["<"+n+' id="'+s+'" class="'+("label"===n?"vjs-label":"")+'">',this.localize(r.label),"</"+n+">",'<select aria-labelledby="'+o+'">'].concat(r.options.map(function(e){var t=s+"-"+e[1].replace(/\W+/g,"");return['<option id="'+t+'" value="'+e[0]+'" ','aria-labelledby="'+o+" "+t+'">',i.localize(e[1]),"</option>"].join("")})).concat("</select>").join("")},t.createElFgColor_=function(){var e="captions-text-legend-"+this.id_;return['<fieldset class="vjs-fg-color vjs-track-setting">','<legend id="'+e+'">',this.localize("Text"),"</legend>",this.createElSelect_("color",e),'<span class="vjs-text-opacity vjs-opacity">',this.createElSelect_("textOpacity",e),"</span>","</fieldset>"].join("")},t.createElBgColor_=function(){var e="captions-background-"+this.id_;return['<fieldset class="vjs-bg-color vjs-track-setting">','<legend id="'+e+'">',this.localize("Background"),"</legend>",this.createElSelect_("backgroundColor",e),'<span class="vjs-bg-opacity vjs-opacity">',this.createElSelect_("backgroundOpacity",e),"</span>","</fieldset>"].join("")},t.createElWinColor_=function(){var e="captions-window-"+this.id_;return['<fieldset class="vjs-window-color vjs-track-setting">','<legend id="'+e+'">',this.localize("Window"),"</legend>",this.createElSelect_("windowColor",e),'<span class="vjs-window-opacity vjs-opacity">',this.createElSelect_("windowOpacity",e),"</span>","</fieldset>"].join("")},t.createElColors_=function(){return te("div",{className:"vjs-track-settings-colors",innerHTML:[this.createElFgColor_(),this.createElBgColor_(),this.createElWinColor_()].join("")})},t.createElFont_=function(){return te("div",{className:"vjs-track-settings-font",innerHTML:['<fieldset class="vjs-font-percent vjs-track-setting">',this.createElSelect_("fontPercent","","legend"),"</fieldset>",'<fieldset class="vjs-edge-style vjs-track-setting">',this.createElSelect_("edgeStyle","","legend"),"</fieldset>",'<fieldset class="vjs-font-family vjs-track-setting">',this.createElSelect_("fontFamily","","legend"),"</fieldset>"].join("")})},t.createElControls_=function(){var e=this.localize("restore all settings to the default values");return te("div",{className:"vjs-track-settings-controls",innerHTML:['<button type="button" class="vjs-default-button" title="'+e+'">',this.localize("Reset"),'<span class="vjs-control-text"> '+e+"</span>","</button>",'<button type="button" class="vjs-done-button">'+this.localize("Done")+"</button>"].join("")})},t.content=function(){return[this.createElColors_(),this.createElFont_(),this.createElControls_()]},t.label=function(){return this.localize("Caption Settings Dialog")},t.description=function(){return this.localize("Beginning of dialog window. Escape will cancel and close the window.")},t.buildCSSClass=function(){return i.prototype.buildCSSClass.call(this)+" vjs-text-track-settings"},t.getValues=function(){var r=this;return function(n,i,e){return void 0===e&&(e=0),y(n).reduce(function(e,t){return i(e,n[t],t)},e)}(ir,function(e,t,n){var i=function(e,t){return rr(e.options[e.options.selectedIndex].value,t)}(r.$(t.selector),t.parser);return void 0!==i&&(e[n]=i),e},{})},t.setValues=function(n){var i=this;m(ir,function(e,t){!function(e,t,n){if(t)for(var i=0;i<e.options.length;i++)if(rr(e.options[i].value,n)===t){e.selectedIndex=i;break}}(i.$(e.selector),n[t],e.parser)})},t.setDefaults=function(){var n=this;m(ir,function(e){var t=e.hasOwnProperty("default")?e.default:0;n.$(e.selector).selectedIndex=t})},t.restoreSettings=function(){var e;try{e=JSON.parse(d.localStorage.getItem(Ui))}catch(e){v.warn(e)}e&&this.setValues(e)},t.saveSettings=function(){if(this.options_.persistTextTrackSettings){var e=this.getValues();try{Object.keys(e).length?d.localStorage.setItem(Ui,JSON.stringify(e)):d.localStorage.removeItem(Ui)}catch(e){v.warn(e)}}},t.updateDisplay=function(){var e=this.player_.getChild("textTrackDisplay");e&&e.updateDisplay()},t.conditionalBlur_=function(){this.previouslyActiveEl_=null;var e=this.player_.controlBar,t=e&&e.subsCapsButton,n=e&&e.captionsButton;t?t.focus():n&&n.focus()},e}(Dt);mt.registerComponent("TextTrackSettings",sr);var or=function(s){function e(e,t){var n,i=t.ResizeObserver||d.ResizeObserver;null===t.ResizeObserver&&(i=!1);var r=ft({createEl:!i,reportTouchActivity:!1},t);return(n=s.call(this,e,r)||this).ResizeObserver=t.ResizeObserver||d.ResizeObserver,n.loadListener_=null,n.resizeObserver_=null,n.debouncedHandler_=function(i,r,s,o){var a;void 0===o&&(o=d);function e(){var e=this,t=arguments,n=function(){n=a=null,s||i.apply(e,t)};!a&&s&&i.apply(e,t),o.clearTimeout(a),a=o.setTimeout(n,r)}return e.cancel=function(){o.clearTimeout(a),a=null},e}(function(){n.resizeHandler()},100,!1,bt(n)),i?(n.resizeObserver_=new n.ResizeObserver(n.debouncedHandler_),n.resizeObserver_.observe(e.el())):(n.loadListener_=function(){if(n.el_&&n.el_.contentWindow){var e=n.debouncedHandler_,t=n.unloadListener_=function(){Ue(this,"resize",e),Ue(this,"unload",t),t=null};We(n.el_.contentWindow,"unload",t),We(n.el_.contentWindow,"resize",e)}},n.one("load",n.loadListener_)),n}Tt(e,s);var t=e.prototype;return t.createEl=function(){return s.prototype.createEl.call(this,"iframe",{className:"vjs-resize-manager",tabIndex:-1},{"aria-hidden":"true"})},t.resizeHandler=function(){this.player_&&this.player_.trigger&&this.player_.trigger("playerresize")},t.dispose=function(){this.debouncedHandler_&&this.debouncedHandler_.cancel(),this.resizeObserver_&&(this.player_.el()&&this.resizeObserver_.unobserve(this.player_.el()),this.resizeObserver_.disconnect()),this.loadListener_&&this.off("load",this.loadListener_),this.el_&&this.el_.contentWindow&&this.unloadListener_&&this.unloadListener_.call(this.el_.contentWindow),this.ResizeObserver=null,this.resizeObserver=null,this.debouncedHandler_=null,this.loadListener_=null,s.prototype.dispose.call(this)},e}(mt);mt.registerComponent("ResizeManager",or);var ar={trackingThreshold:30,liveTolerance:15},lr=function(r){function e(e,t){var n,i=ft(ar,t,{createEl:!1});return(n=r.call(this,e,i)||this).reset_(),n.on(n.player_,"durationchange",n.handleDurationchange),H&&"hidden"in p&&"visibilityState"in p&&n.on(p,"visibilitychange",n.handleVisibilityChange),n}Tt(e,r);var t=e.prototype;return t.handleVisibilityChange=function(){this.player_.duration()===1/0&&(p.hidden?this.stopTracking():this.startTracking())},t.trackLive_=function(){var e=this.player_.seekable();if(e&&e.length){var t=Number(d.performance.now().toFixed(4)),n=-1===this.lastTime_?0:(t-this.lastTime_)/1e3;this.lastTime_=t,this.pastSeekEnd_=this.pastSeekEnd()+n;var i=this.liveCurrentTime(),r=this.player_.currentTime(),s=this.player_.paused()||this.seekedBehindLive_||Math.abs(i-r)>this.options_.liveTolerance;this.timeupdateSeen_&&i!==1/0||(s=!1),s!==this.behindLiveEdge_&&(this.behindLiveEdge_=s,this.trigger("liveedgechange"))}},t.handleDurationchange=function(){this.player_.duration()===1/0&&this.liveWindow()>=this.options_.trackingThreshold?(this.player_.options_.liveui&&this.player_.addClass("vjs-liveui"),this.startTracking()):(this.player_.removeClass("vjs-liveui"),this.stopTracking())},t.startTracking=function(){this.isTracking()||(this.timeupdateSeen_||(this.timeupdateSeen_=this.player_.hasStarted()),this.trackingInterval_=this.setInterval(this.trackLive_,30),this.trackLive_(),this.on(this.player_,["play","pause"],this.trackLive_),this.timeupdateSeen_?this.on(this.player_,"seeked",this.handleSeeked):(this.one(this.player_,"play",this.handlePlay),this.one(this.player_,"timeupdate",this.handleFirstTimeupdate)))},t.handleFirstTimeupdate=function(){this.timeupdateSeen_=!0,this.on(this.player_,"seeked",this.handleSeeked)},t.handleSeeked=function(){var e=Math.abs(this.liveCurrentTime()-this.player_.currentTime());this.seekedBehindLive_=!this.skipNextSeeked_&&2<e,this.skipNextSeeked_=!1,this.trackLive_()},t.handlePlay=function(){this.one(this.player_,"timeupdate",this.seekToLiveEdge)},t.reset_=function(){this.lastTime_=-1,this.pastSeekEnd_=0,this.lastSeekEnd_=-1,this.behindLiveEdge_=!0,this.timeupdateSeen_=!1,this.seekedBehindLive_=!1,this.skipNextSeeked_=!1,this.clearInterval(this.trackingInterval_),this.trackingInterval_=null,this.off(this.player_,["play","pause"],this.trackLive_),this.off(this.player_,"seeked",this.handleSeeked),this.off(this.player_,"play",this.handlePlay),this.off(this.player_,"timeupdate",this.handleFirstTimeupdate),this.off(this.player_,"timeupdate",this.seekToLiveEdge)},t.stopTracking=function(){this.isTracking()&&(this.reset_(),this.trigger("liveedgechange"))},t.seekableEnd=function(){for(var e=this.player_.seekable(),t=[],n=e?e.length:0;n--;)t.push(e.end(n));return t.length?t.sort()[t.length-1]:1/0},t.seekableStart=function(){for(var e=this.player_.seekable(),t=[],n=e?e.length:0;n--;)t.push(e.start(n));return t.length?t.sort()[0]:0},t.liveWindow=function(){var e=this.liveCurrentTime();return e===1/0?0:e-this.seekableStart()},t.isLive=function(){return this.isTracking()},t.atLiveEdge=function(){return!this.behindLiveEdge()},t.liveCurrentTime=function(){return this.pastSeekEnd()+this.seekableEnd()},t.pastSeekEnd=function(){var e=this.seekableEnd();return-1!==this.lastSeekEnd_&&e!==this.lastSeekEnd_&&(this.pastSeekEnd_=0),this.lastSeekEnd_=e,this.pastSeekEnd_},t.behindLiveEdge=function(){return this.behindLiveEdge_},t.isTracking=function(){return"number"==typeof this.trackingInterval_},t.seekToLiveEdge=function(){this.seekedBehindLive_=!1,this.atLiveEdge()||(this.skipNextSeeked_=!0,this.player_.currentTime(this.liveCurrentTime()))},t.dispose=function(){this.off(p,"visibilitychange",this.handleVisibilityChange),this.stopTracking(),r.prototype.dispose.call(this)},e}(mt);mt.registerComponent("LiveTracker",lr);function cr(e){var t=e.el();if(t.hasAttribute("src"))return e.triggerSourceset(t.src),!0;var n=e.$$("source"),i=[],r="";if(!n.length)return!1;for(var s=0;s<n.length;s++){var o=n[s].src;o&&-1===i.indexOf(o)&&i.push(o)}return!!i.length&&(1===i.length&&(r=i[0]),e.triggerSourceset(r),!0)}function ur(e,t){for(var n={},i=0;i<e.length&&!((n=Object.getOwnPropertyDescriptor(e[i],t))&&n.set&&n.get);i++);return n.enumerable=!0,n.configurable=!0,n}function hr(s){var o=s.el();if(!o.resetSourceWatch_){var t={},e=function(e){return ur([e.el(),d.HTMLMediaElement.prototype,d.Element.prototype,vr],"innerHTML")}(s),n=function(r){return function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var i=r.apply(o,t);return cr(s),i}};["append","appendChild","insertAdjacentHTML"].forEach(function(e){o[e]&&(t[e]=o[e],o[e]=n(t[e]))}),Object.defineProperty(o,"innerHTML",ft(e,{set:n(e.set)})),o.resetSourceWatch_=function(){o.resetSourceWatch_=null,Object.keys(t).forEach(function(e){o[e]=t[e]}),Object.defineProperty(o,"innerHTML",e)},s.one("sourceset",o.resetSourceWatch_)}}function dr(i){if(i.featuresSourceset){var r=i.el();if(!r.resetSourceset_){var n=function(e){return ur([e.el(),d.HTMLMediaElement.prototype,gr],"src")}(i),s=r.setAttribute,t=r.load;Object.defineProperty(r,"src",ft(n,{set:function(e){var t=n.set.call(r,e);return i.triggerSourceset(r.src),t}})),r.setAttribute=function(e,t){var n=s.call(r,e,t);return/src/i.test(e)&&i.triggerSourceset(r.src),n},r.load=function(){var e=t.call(r);return cr(i)||(i.triggerSourceset(""),hr(i)),e},r.currentSrc?i.triggerSourceset(r.currentSrc):cr(i)||hr(i),r.resetSourceset_=function(){r.resetSourceset_=null,r.load=t,r.setAttribute=s,Object.defineProperty(r,"src",n),r.resetSourceWatch_&&r.resetSourceWatch_()}}}}function pr(t,n,i,e){function r(e){return Object.defineProperty(t,n,{value:e,enumerable:!0,writable:!0})}void 0===e&&(e=!0);var s={configurable:!0,enumerable:!0,get:function(){var e=i();return r(e),e}};return e&&(s.set=r),Object.defineProperty(t,n,s)}var fr,vr=Object.defineProperty({},"innerHTML",{get:function(){return this.cloneNode(!0).innerHTML},set:function(e){var t=p.createElement(this.nodeName.toLowerCase());t.innerHTML=e;for(var n=p.createDocumentFragment();t.childNodes.length;)n.appendChild(t.childNodes[0]);return this.innerText="",d.Element.prototype.appendChild.call(this,n),this.innerHTML}}),gr=Object.defineProperty({},"src",{get:function(){return this.hasAttribute("src")?Kt(d.Element.prototype.getAttribute.call(this,"src")):""},set:function(e){return d.Element.prototype.setAttribute.call(this,"src",e),e}}),_r=function(u){function a(e,t){var n;n=u.call(this,e,t)||this;var i=e.source,r=!1;if(i&&(n.el_.currentSrc!==i.src||e.tag&&3===e.tag.initNetworkState_)?n.setSource(i):n.handleLateInit_(n.el_),e.enableSourceset&&n.setupSourcesetHandling_(),n.isScrubbing_=!1,n.el_.hasChildNodes()){for(var s=n.el_.childNodes,o=s.length,a=[];o--;){var l=s[o];"track"===l.nodeName.toLowerCase()&&(n.featuresNativeTextTracks?(n.remoteTextTrackEls().addTrackElement_(l),n.remoteTextTracks().addTrack(l.track),n.textTracks().addTrack(l.track),r||n.el_.hasAttribute("crossorigin")||!Wt(l.src)||(r=!0)):a.push(l))}for(var c=0;c<a.length;c++)n.el_.removeChild(a[c])}return n.proxyNativeTracks_(),n.featuresNativeTextTracks&&r&&v.warn("Text Tracks are being loaded from another origin but the crossorigin attribute isn't used.\nThis may prevent text tracks from loading."),n.restoreMetadataTracksInIOSNativePlayer_(),(z||U||M)&&!0===e.nativeControlsForTouch&&n.setControls(!0),n.proxyWebkitFullscreen_(),n.triggerReady(),n}Tt(a,u);var e=a.prototype;return e.dispose=function(){this.el_&&this.el_.resetSourceset_&&this.el_.resetSourceset_(),a.disposeMediaElement(this.el_),this.options_=null,u.prototype.dispose.call(this)},e.setupSourcesetHandling_=function(){dr(this)},e.restoreMetadataTracksInIOSNativePlayer_=function(){function e(){n=[];for(var e=0;e<i.length;e++){var t=i[e];"metadata"===t.kind&&n.push({track:t,storedMode:t.mode})}}var n,i=this.textTracks();e(),i.addEventListener("change",e),this.on("dispose",function(){return i.removeEventListener("change",e)});function r(){for(var e=0;e<n.length;e++){var t=n[e];"disabled"===t.track.mode&&t.track.mode!==t.storedMode&&(t.track.mode=t.storedMode)}i.removeEventListener("change",r)}this.on("webkitbeginfullscreen",function(){i.removeEventListener("change",e),i.removeEventListener("change",r),i.addEventListener("change",r)}),this.on("webkitendfullscreen",function(){i.removeEventListener("change",e),i.addEventListener("change",e),i.removeEventListener("change",r)})},e.overrideNative_=function(e,t){var n=this;if(t===this["featuresNative"+e+"Tracks"]){var i=e.toLowerCase();this[i+"TracksListeners_"]&&Object.keys(this[i+"TracksListeners_"]).forEach(function(e){n.el()[i+"Tracks"].removeEventListener(e,n[i+"TracksListeners_"][e])}),this["featuresNative"+e+"Tracks"]=!t,this[i+"TracksListeners_"]=null,this.proxyNativeTracksForType_(i)}},e.overrideNativeAudioTracks=function(e){this.overrideNative_("Audio",e)},e.overrideNativeVideoTracks=function(e){this.overrideNative_("Video",e)},e.proxyNativeTracksForType_=function(n){var i=this,e=yn[n],r=this.el()[e.getterName],s=this[e.getterName]();if(this["featuresNative"+e.capitalName+"Tracks"]&&r&&r.addEventListener){var o={change:function(e){var t={type:"change",target:s,currentTarget:s,srcElement:s};s.trigger(t),"text"===n&&i[mn.remoteText.getterName]().trigger(t)},addtrack:function(e){s.addTrack(e.track)},removetrack:function(e){s.removeTrack(e.track)}},t=function(){for(var e=[],t=0;t<s.length;t++){for(var n=!1,i=0;i<r.length;i++)if(r[i]===s[t]){n=!0;break}n||e.push(s[t])}for(;e.length;)s.removeTrack(e.shift())};this[e.getterName+"Listeners_"]=o,Object.keys(o).forEach(function(t){var n=o[t];r.addEventListener(t,n),i.on("dispose",function(e){return r.removeEventListener(t,n)})}),this.on("loadstart",t),this.on("dispose",function(e){return i.off("loadstart",t)})}},e.proxyNativeTracks_=function(){var t=this;yn.names.forEach(function(e){t.proxyNativeTracksForType_(e)})},e.createEl=function(){var e=this.options_.tag;if(!e||!this.options_.playerElIngest&&!this.movingMediaElementInDOM){if(e){var t=e.cloneNode(!0);e.parentNode&&e.parentNode.insertBefore(t,e),a.disposeMediaElement(e),e=t}else{e=p.createElement("video");var n=ft({},this.options_.tag&&ce(this.options_.tag));z&&!0===this.options_.nativeControlsForTouch||delete n.controls,le(e,b(n,{id:this.options_.techId,class:"vjs-tech"}))}e.playerId=this.options_.playerId}"undefined"!=typeof this.options_.preload&&he(e,"preload",this.options_.preload),void 0!==this.options_.disablePictureInPicture&&(e.disablePictureInPicture=this.options_.disablePictureInPicture);for(var i=["loop","muted","playsinline","autoplay"],r=0;r<i.length;r++){var s=i[r],o=this.options_[s];"undefined"!=typeof o&&(o?he(e,s,s):de(e,s),e[s]=o)}return e},e.handleLateInit_=function(e){if(0!==e.networkState&&3!==e.networkState){if(0===e.readyState){var t=!1,n=function(){t=!0};this.on("loadstart",n);var i=function(){t||this.trigger("loadstart")};return this.on("loadedmetadata",i),void this.ready(function(){this.off("loadstart",n),this.off("loadedmetadata",i),t||this.trigger("loadstart")})}var r=["loadstart"];r.push("loadedmetadata"),2<=e.readyState&&r.push("loadeddata"),3<=e.readyState&&r.push("canplay"),4<=e.readyState&&r.push("canplaythrough"),this.ready(function(){r.forEach(function(e){this.trigger(e)},this)})}},e.setScrubbing=function(e){this.isScrubbing_=e},e.scrubbing=function(){return this.isScrubbing_},e.setCurrentTime=function(e){try{this.isScrubbing_&&this.el_.fastSeek&&X?this.el_.fastSeek(e):this.el_.currentTime=e}catch(e){v(e,"Video is not ready. (Video.js)")}},e.duration=function(){var t=this;if(this.el_.duration===1/0&&N&&R&&0===this.el_.currentTime){return this.on("timeupdate",function e(){0<t.el_.currentTime&&(t.el_.duration===1/0&&t.trigger("durationchange"),t.off("timeupdate",e))}),NaN}return this.el_.duration||NaN},e.width=function(){return this.el_.offsetWidth},e.height=function(){return this.el_.offsetHeight},e.proxyWebkitFullscreen_=function(){var e=this;if("webkitDisplayingFullscreen"in this.el_){var t=function(){this.trigger("fullscreenchange",{isFullscreen:!1})},n=function(){"webkitPresentationMode"in this.el_&&"picture-in-picture"!==this.el_.webkitPresentationMode&&(this.one("webkitendfullscreen",t),this.trigger("fullscreenchange",{isFullscreen:!0,nativeIOSFullscreen:!0}))};this.on("webkitbeginfullscreen",n),this.on("dispose",function(){e.off("webkitbeginfullscreen",n),e.off("webkitendfullscreen",t)})}},e.supportsFullScreen=function(){if("function"==typeof this.el_.webkitEnterFullScreen){var e=d.navigator&&d.navigator.userAgent||"";if(/Android/.test(e)||!/Chrome|Mac OS X 10.5/.test(e))return!0}return!1},e.enterFullScreen=function(){var e=this.el_;if(e.paused&&e.networkState<=e.HAVE_METADATA)At(this.el_.play()),this.setTimeout(function(){e.pause();try{e.webkitEnterFullScreen()}catch(e){this.trigger("fullscreenerror",e)}},0);else try{e.webkitEnterFullScreen()}catch(e){this.trigger("fullscreenerror",e)}},e.exitFullScreen=function(){this.el_.webkitDisplayingFullscreen?this.el_.webkitExitFullScreen():this.trigger("fullscreenerror",new Error("The video is not fullscreen"))},e.requestPictureInPicture=function(){return this.el_.requestPictureInPicture()},e.src=function(e){if(void 0===e)return this.el_.src;this.setSrc(e)},e.reset=function(){a.resetMediaElement(this.el_)},e.currentSrc=function(){return this.currentSource_?this.currentSource_.src:this.el_.currentSrc},e.setControls=function(e){this.el_.controls=!!e},e.addTextTrack=function(e,t,n){return this.featuresNativeTextTracks?this.el_.addTextTrack(e,t,n):u.prototype.addTextTrack.call(this,e,t,n)},e.createRemoteTextTrack=function(e){if(!this.featuresNativeTextTracks)return u.prototype.createRemoteTextTrack.call(this,e);var t=p.createElement("track");return e.kind&&(t.kind=e.kind),e.label&&(t.label=e.label),(e.language||e.srclang)&&(t.srclang=e.language||e.srclang),e.default&&(t.default=e.default),e.id&&(t.id=e.id),e.src&&(t.src=e.src),t},e.addRemoteTextTrack=function(e,t){var n=u.prototype.addRemoteTextTrack.call(this,e,t);return this.featuresNativeTextTracks&&this.el().appendChild(n),n},e.removeRemoteTextTrack=function(e){if(u.prototype.removeRemoteTextTrack.call(this,e),this.featuresNativeTextTracks)for(var t=this.$$("track"),n=t.length;n--;)e!==t[n]&&e!==t[n].track||this.el().removeChild(t[n])},e.getVideoPlaybackQuality=function(){if("function"==typeof this.el().getVideoPlaybackQuality)return this.el().getVideoPlaybackQuality();var e={};return"undefined"!=typeof this.el().webkitDroppedFrameCount&&"undefined"!=typeof this.el().webkitDecodedFrameCount&&(e.droppedVideoFrames=this.el().webkitDroppedFrameCount,e.totalVideoFrames=this.el().webkitDecodedFrameCount),d.performance&&"function"==typeof d.performance.now?e.creationTime=d.performance.now():d.performance&&d.performance.timing&&"number"==typeof d.performance.timing.navigationStart&&(e.creationTime=d.Date.now()-d.performance.timing.navigationStart),e},a}(kn);pr(_r,"TEST_VID",function(){if(Q()){var e=p.createElement("video"),t=p.createElement("track");return t.kind="captions",t.srclang="en",t.label="English",e.appendChild(t),e}}),_r.isSupported=function(){try{_r.TEST_VID.volume=.5}catch(e){return!1}return!(!_r.TEST_VID||!_r.TEST_VID.canPlayType)},_r.canPlayType=function(e){return _r.TEST_VID.canPlayType(e)},_r.canPlaySource=function(e,t){return _r.canPlayType(e.type)},_r.canControlVolume=function(){try{var e=_r.TEST_VID.volume;return _r.TEST_VID.volume=e/2+.1,e!==_r.TEST_VID.volume}catch(e){return!1}},_r.canMuteVolume=function(){try{var e=_r.TEST_VID.muted;return _r.TEST_VID.muted=!e,_r.TEST_VID.muted?he(_r.TEST_VID,"muted","muted"):de(_r.TEST_VID,"muted"),e!==_r.TEST_VID.muted}catch(e){return!1}},_r.canControlPlaybackRate=function(){if(N&&R&&B<58)return!1;try{var e=_r.TEST_VID.playbackRate;return _r.TEST_VID.playbackRate=e/2+.1,e!==_r.TEST_VID.playbackRate}catch(e){return!1}},_r.canOverrideAttributes=function(){try{var e=function(){};Object.defineProperty(p.createElement("video"),"src",{get:e,set:e}),Object.defineProperty(p.createElement("audio"),"src",{get:e,set:e}),Object.defineProperty(p.createElement("video"),"innerHTML",{get:e,set:e}),Object.defineProperty(p.createElement("audio"),"innerHTML",{get:e,set:e})}catch(e){return!1}return!0},_r.supportsNativeTextTracks=function(){return X||q&&R},_r.supportsNativeVideoTracks=function(){return!(!_r.TEST_VID||!_r.TEST_VID.videoTracks)},_r.supportsNativeAudioTracks=function(){return!(!_r.TEST_VID||!_r.TEST_VID.audioTracks)},_r.Events=["loadstart","suspend","abort","error","emptied","stalled","loadedmetadata","loadeddata","canplay","canplaythrough","playing","waiting","seeking","seeked","ended","durationchange","timeupdate","progress","play","pause","ratechange","resize","volumechange"],[["featuresVolumeControl","canControlVolume"],["featuresMuteControl","canMuteVolume"],["featuresPlaybackRate","canControlPlaybackRate"],["featuresSourceset","canOverrideAttributes"],["featuresNativeTextTracks","supportsNativeTextTracks"],["featuresNativeVideoTracks","supportsNativeVideoTracks"],["featuresNativeAudioTracks","supportsNativeAudioTracks"]].forEach(function(e){var t=e[0],n=e[1];pr(_r.prototype,t,function(){return _r[n]()},!0)}),_r.prototype.movingMediaElementInDOM=!q,_r.prototype.featuresFullscreenResize=!0,_r.prototype.featuresProgressEvents=!0,_r.prototype.featuresTimeupdateEvents=!0,_r.patchCanPlayType=function(){4<=O&&!D&&!R&&(fr=_r.TEST_VID&&_r.TEST_VID.constructor.prototype.canPlayType,_r.TEST_VID.constructor.prototype.canPlayType=function(e){return e&&/^application\/(?:x-|vnd\.apple\.)mpegurl/i.test(e)?"maybe":fr.call(this,e)})},_r.unpatchCanPlayType=function(){var e=_r.TEST_VID.constructor.prototype.canPlayType;return fr&&(_r.TEST_VID.constructor.prototype.canPlayType=fr),e},_r.patchCanPlayType(),_r.disposeMediaElement=function(e){if(e){for(e.parentNode&&e.parentNode.removeChild(e);e.hasChildNodes();)e.removeChild(e.firstChild);e.removeAttribute("src"),"function"==typeof e.load&&function(){try{e.load()}catch(e){}}()}},_r.resetMediaElement=function(e){if(e){for(var t=e.querySelectorAll("source"),n=t.length;n--;)e.removeChild(t[n]);e.removeAttribute("src"),"function"==typeof e.load&&function(){try{e.load()}catch(e){}}()}},["muted","defaultMuted","autoplay","controls","loop","playsinline"].forEach(function(e){_r.prototype[e]=function(){return this.el_[e]||this.el_.hasAttribute(e)}}),["muted","defaultMuted","autoplay","loop","playsinline"].forEach(function(t){_r.prototype["set"+pt(t)]=function(e){(this.el_[t]=e)?this.el_.setAttribute(t,t):this.el_.removeAttribute(t)}}),["paused","currentTime","buffered","volume","poster","preload","error","seeking","seekable","ended","playbackRate","defaultPlaybackRate","disablePictureInPicture","played","networkState","readyState","videoWidth","videoHeight","crossOrigin"].forEach(function(e){_r.prototype[e]=function(){return this.el_[e]}}),["volume","src","poster","preload","playbackRate","defaultPlaybackRate","disablePictureInPicture","crossOrigin"].forEach(function(t){_r.prototype["set"+pt(t)]=function(e){this.el_[t]=e}}),["pause","load","play"].forEach(function(e){_r.prototype[e]=function(){return this.el_[e]()}}),kn.withSourceHandlers(_r),_r.nativeSourceHandler={},_r.nativeSourceHandler.canPlayType=function(e){try{return _r.TEST_VID.canPlayType(e)}catch(e){return""}},_r.nativeSourceHandler.canHandleSource=function(e,t){if(e.type)return _r.nativeSourceHandler.canPlayType(e.type);if(e.src){var n=zt(e.src);return _r.nativeSourceHandler.canPlayType("video/"+n)}return""},_r.nativeSourceHandler.handleSource=function(e,t,n){t.setSrc(e.src)},_r.nativeSourceHandler.dispose=function(){},_r.registerSourceHandler(_r.nativeSourceHandler),kn.registerTech("Html5",_r);var yr=["progress","abort","suspend","emptied","stalled","loadedmetadata","loadeddata","timeupdate","resize","volumechange","texttrackchange"],mr={canplay:"CanPlay",canplaythrough:"CanPlayThrough",playing:"Playing",seeked:"Seeked"},br=["tiny","xsmall","small","medium","large","xlarge","huge"],Tr={};br.forEach(function(e){var t="x"===e.charAt(0)?"x-"+e.substring(1):e;Tr[e]="vjs-layout-"+t});var kr={tiny:210,xsmall:320,small:425,medium:768,large:1440,xlarge:2560,huge:1/0},Cr=function(u){function c(e,t,n){var i;if(e.id=e.id||t.id||"vjs_video_"+De(),(t=b(c.getTagSettings(e),t)).initChildren=!1,t.createEl=!1,t.evented=!1,t.reportTouchActivity=!1,!t.language)if("function"==typeof e.closest){var r=e.closest("[lang]");r&&r.getAttribute&&(t.language=r.getAttribute("lang"))}else for(var s=e;s&&1===s.nodeType;){if(ce(s).hasOwnProperty("lang")){t.language=s.getAttribute("lang");break}s=s.parentNode}if((i=u.call(this,null,t,n)||this).boundDocumentFullscreenChange_=function(e){return i.documentFullscreenChange_(e)},i.boundFullWindowOnEscKey_=function(e){return i.fullWindowOnEscKey(e)},i.isFullscreen_=!1,i.log=g(i.id_),i.fsApi_=f,i.isPosterFromTech_=!1,i.queuedCallbacks_=[],i.isReady_=!1,i.hasStarted_=!1,i.userActive_=!1,i.debugEnabled_=!1,!i.options_||!i.options_.techOrder||!i.options_.techOrder.length)throw new Error("No techOrder specified. Did you overwrite videojs.options instead of just changing the properties you want to override?");if(i.tag=e,i.tagAttributes=e&&ce(e),i.language(i.options_.language),t.languages){var o={};Object.getOwnPropertyNames(t.languages).forEach(function(e){o[e.toLowerCase()]=t.languages[e]}),i.languages_=o}else i.languages_=c.prototype.options_.languages;i.resetCache_(),i.poster_=t.poster||"",i.controls_=!!t.controls,e.controls=!1,e.removeAttribute("controls"),i.changingSrc_=!1,i.playCallbacks_=[],i.playTerminatedQueue_=[],e.hasAttribute("autoplay")?i.autoplay(!0):i.autoplay(i.options_.autoplay),t.plugins&&Object.keys(t.plugins).forEach(function(e){if("function"!=typeof i[e])throw new Error('plugin "'+e+'" does not exist')}),i.scrubbing_=!1,i.el_=i.createEl(),ct(bt(i),{eventBusKey:"el_"}),i.fsApi_.requestFullscreen&&(We(p,i.fsApi_.fullscreenchange,i.boundDocumentFullscreenChange_),i.on(i.fsApi_.fullscreenchange,i.boundDocumentFullscreenChange_)),i.fluid_&&i.on(["playerreset","resize"],i.updateStyleEl_);var a=ft(i.options_);t.plugins&&Object.keys(t.plugins).forEach(function(e){i[e](t.plugins[e])}),t.debug&&i.debug(!0),i.options_.playerOptions=a,i.middleware_=[],i.initChildren(),i.isAudio("audio"===e.nodeName.toLowerCase()),i.controls()?i.addClass("vjs-controls-enabled"):i.addClass("vjs-controls-disabled"),i.el_.setAttribute("role","region"),i.isAudio()?i.el_.setAttribute("aria-label",i.localize("Audio Player")):i.el_.setAttribute("aria-label",i.localize("Video Player")),i.isAudio()&&i.addClass("vjs-audio"),i.flexNotSupported_()&&i.addClass("vjs-no-flex"),z&&i.addClass("vjs-touch-enabled"),q||i.addClass("vjs-workinghover"),c.players[i.id_]=bt(i);var l=h.split(".")[0];return i.addClass("vjs-v"+l),i.userActive(!0),i.reportUserActivity(),i.one("play",i.listenForUserActivity_),i.on("stageclick",i.handleStageClick_),i.on("keydown",i.handleKeyDown),i.on("languagechange",i.handleLanguagechange),i.breakpoints(i.options_.breakpoints),i.responsive(i.options_.responsive),i}Tt(c,u);var e=c.prototype;return e.dispose=function(){var i=this;this.trigger("dispose"),this.off("dispose"),Ue(p,this.fsApi_.fullscreenchange,this.boundDocumentFullscreenChange_),Ue(p,"keydown",this.boundFullWindowOnEscKey_),this.styleEl_&&this.styleEl_.parentNode&&(this.styleEl_.parentNode.removeChild(this.styleEl_),this.styleEl_=null),c.players[this.id_]=null,this.tag&&this.tag.player&&(this.tag.player=null),this.el_&&this.el_.player&&(this.el_.player=null),this.tech_&&(this.tech_.dispose(),this.isPosterFromTech_=!1,this.poster_=""),this.playerElIngest_&&(this.playerElIngest_=null),this.tag&&(this.tag=null),function(e){En[e.id()]=null}(this),bn.names.forEach(function(e){var t=bn[e],n=i[t.getterName]();n&&n.off&&n.off()}),u.prototype.dispose.call(this)},e.createEl=function(){var t,n=this.tag,e=this.playerElIngest_=n.parentNode&&n.parentNode.hasAttribute&&n.parentNode.hasAttribute("data-vjs-player"),i="video-js"===this.tag.tagName.toLowerCase();e?t=this.el_=n.parentNode:i||(t=this.el_=u.prototype.createEl.call(this,"div"));var r=ce(n);if(i){for(t=this.el_=n,n=this.tag=p.createElement("video");t.children.length;)n.appendChild(t.firstChild);re(t,"video-js")||se(t,"video-js"),t.appendChild(n),e=this.playerElIngest_=t,Object.keys(t).forEach(function(e){try{n[e]=t[e]}catch(e){}})}if(n.setAttribute("tabindex","-1"),r.tabindex="-1",(H||R&&K)&&(n.setAttribute("role","application"),r.role="application"),n.removeAttribute("width"),n.removeAttribute("height"),"width"in r&&delete r.width,"height"in r&&delete r.height,Object.getOwnPropertyNames(r).forEach(function(e){i&&"class"===e||t.setAttribute(e,r[e]),i&&n.setAttribute(e,r[e])}),n.playerId=n.id,n.id+="_html5_api",n.className="vjs-tech",n.player=t.player=this,this.addClass("vjs-paused"),!0!==d.VIDEOJS_NO_DYNAMIC_STYLE){this.styleEl_=Le("vjs-styles-dimensions");var s=Se(".vjs-styles-defaults"),o=Se("head");o.insertBefore(this.styleEl_,s?s.nextSibling:o.firstChild)}this.fill_=!1,this.fluid_=!1,this.width(this.options_.width),this.height(this.options_.height),this.fill(this.options_.fill),this.fluid(this.options_.fluid),this.aspectRatio(this.options_.aspectRatio),this.crossOrigin(this.options_.crossOrigin||this.options_.crossorigin);for(var a=n.getElementsByTagName("a"),l=0;l<a.length;l++){var c=a.item(l);se(c,"vjs-hidden"),c.setAttribute("hidden","hidden")}return n.initNetworkState_=n.networkState,n.parentNode&&!e&&n.parentNode.insertBefore(t,n),ie(n,t),this.children_.unshift(n),this.el_.setAttribute("lang",this.language_),this.el_=t},e.crossOrigin=function(e){if(!e)return this.techGet_("crossOrigin");"anonymous"===e||"use-credentials"===e?this.techCall_("setCrossOrigin",e):v.warn('crossOrigin must be "anonymous" or "use-credentials", given "'+e+'"')},e.width=function(e){return this.dimension("width",e)},e.height=function(e){return this.dimension("height",e)},e.dimension=function(e,t){var n=e+"_";if(void 0===t)return this[n]||0;if(""===t||"auto"===t)return this[n]=void 0,void this.updateStyleEl_();var i=parseFloat(t);isNaN(i)?v.error('Improper value "'+t+'" supplied for for '+e):(this[n]=i,this.updateStyleEl_())},e.fluid=function(e){var t=this;if(void 0===e)return!!this.fluid_;this.fluid_=!!e,at(this)&&this.off(["playerreset","resize"],this.updateStyleEl_),e?(this.addClass("vjs-fluid"),this.fill(!1),function(e,t){at(e)?t():(e.eventedCallbacks||(e.eventedCallbacks=[]),e.eventedCallbacks.push(t))}(this,function(){t.on(["playerreset","resize"],t.updateStyleEl_)})):this.removeClass("vjs-fluid"),this.updateStyleEl_()},e.fill=function(e){if(void 0===e)return!!this.fill_;this.fill_=!!e,e?(this.addClass("vjs-fill"),this.fluid(!1)):this.removeClass("vjs-fill")},e.aspectRatio=function(e){if(void 0===e)return this.aspectRatio_;if(!/^\d+\:\d+$/.test(e))throw new Error("Improper value supplied for aspect ratio. The format should be width:height, for example 16:9.");this.aspectRatio_=e,this.fluid(!0),this.updateStyleEl_()},e.updateStyleEl_=function(){if(!0!==d.VIDEOJS_NO_DYNAMIC_STYLE){var e,t,n,i=(void 0!==this.aspectRatio_&&"auto"!==this.aspectRatio_?this.aspectRatio_:0<this.videoWidth()?this.videoWidth()+":"+this.videoHeight():"16:9").split(":"),r=i[1]/i[0];e=void 0!==this.width_?this.width_:void 0!==this.height_?this.height_/r:this.videoWidth()||300,t=void 0!==this.height_?this.height_:e*r,n=/^[^a-zA-Z]/.test(this.id())?"dimensions-"+this.id():this.id()+"-dimensions",this.addClass(n),Ne(this.styleEl_,"\n      ."+n+" {\n        width: "+e+"px;\n        height: "+t+"px;\n      }\n\n      ."+n+".vjs-fluid {\n        padding-top: "+100*r+"%;\n      }\n    ")}else{var s="number"==typeof this.width_?this.width_:this.options_.width,o="number"==typeof this.height_?this.height_:this.options_.height,a=this.tech_&&this.tech_.el();a&&(0<=s&&(a.width=s),0<=o&&(a.height=o))}},e.loadTech_=function(e,t){var n=this;this.tech_&&this.unloadTech_();var i=pt(e),r=e.charAt(0).toLowerCase()+e.slice(1);"Html5"!==i&&this.tag&&(kn.getTech("Html5").disposeMediaElement(this.tag),this.tag.player=null,this.tag=null),this.techName_=i,this.isReady_=!1;var s={source:t,autoplay:"string"!=typeof this.autoplay()&&this.autoplay(),nativeControlsForTouch:this.options_.nativeControlsForTouch,playerId:this.id(),techId:this.id()+"_"+r+"_api",playsinline:this.options_.playsinline,preload:this.options_.preload,loop:this.options_.loop,disablePictureInPicture:this.options_.disablePictureInPicture,muted:this.options_.muted,poster:this.poster(),language:this.language(),playerElIngest:this.playerElIngest_||!1,"vtt.js":this.options_["vtt.js"],canOverridePoster:!!this.options_.techCanOverridePoster,enableSourceset:this.options_.enableSourceset,Promise:this.options_.Promise};bn.names.forEach(function(e){var t=bn[e];s[t.getterName]=n[t.privateName]}),b(s,this.options_[i]),b(s,this.options_[r]),b(s,this.options_[e.toLowerCase()]),this.tag&&(s.tag=this.tag),t&&t.src===this.cache_.src&&0<this.cache_.currentTime&&(s.startTime=this.cache_.currentTime);var o=kn.getTech(e);if(!o)throw new Error("No Tech named '"+i+"' exists! '"+i+"' should be registered using videojs.registerTech()'");this.tech_=new o(s),this.tech_.ready($e(this,this.handleTechReady_),!0),Nt(this.textTracksJson_||[],this.tech_),yr.forEach(function(e){n.on(n.tech_,e,n["handleTech"+pt(e)+"_"])}),Object.keys(mr).forEach(function(t){n.on(n.tech_,t,function(e){0===n.tech_.playbackRate()&&n.tech_.seeking()?n.queuedCallbacks_.push({callback:n["handleTech"+mr[t]+"_"].bind(n),event:e}):n["handleTech"+mr[t]+"_"](e)})}),this.on(this.tech_,"loadstart",this.handleTechLoadStart_),this.on(this.tech_,"sourceset",this.handleTechSourceset_),this.on(this.tech_,"waiting",this.handleTechWaiting_),this.on(this.tech_,"ended",this.handleTechEnded_),this.on(this.tech_,"seeking",this.handleTechSeeking_),this.on(this.tech_,"play",this.handleTechPlay_),this.on(this.tech_,"firstplay",this.handleTechFirstPlay_),this.on(this.tech_,"pause",this.handleTechPause_),this.on(this.tech_,"durationchange",this.handleTechDurationChange_),this.on(this.tech_,"fullscreenchange",this.handleTechFullscreenChange_),this.on(this.tech_,"fullscreenerror",this.handleTechFullscreenError_),this.on(this.tech_,"enterpictureinpicture",this.handleTechEnterPictureInPicture_),this.on(this.tech_,"leavepictureinpicture",this.handleTechLeavePictureInPicture_),this.on(this.tech_,"error",this.handleTechError_),this.on(this.tech_,"loadedmetadata",this.updateStyleEl_),this.on(this.tech_,"posterchange",this.handleTechPosterChange_),this.on(this.tech_,"textdata",this.handleTechTextData_),this.on(this.tech_,"ratechange",this.handleTechRateChange_),this.usingNativeControls(this.techGet_("controls")),this.controls()&&!this.usingNativeControls()&&this.addTechControlsListeners_(),this.tech_.el().parentNode===this.el()||"Html5"===i&&this.tag||ie(this.tech_.el(),this.el()),this.tag&&(this.tag.player=null,this.tag=null)},e.unloadTech_=function(){var n=this;bn.names.forEach(function(e){var t=bn[e];n[t.privateName]=n[t.getterName]()}),this.textTracksJson_=Lt(this.tech_),this.isReady_=!1,this.tech_.dispose(),this.tech_=!1,this.isPosterFromTech_&&(this.poster_="",this.trigger("posterchange")),this.isPosterFromTech_=!1},e.tech=function(e){return void 0===e&&v.warn("Using the tech directly can be dangerous. I hope you know what you're doing.\nSee https://github.com/videojs/video.js/issues/2617 for more info.\n"),this.tech_},e.addTechControlsListeners_=function(){this.removeTechControlsListeners_(),this.on(this.tech_,"mouseup",this.handleTechClick_),this.on(this.tech_,"dblclick",this.handleTechDoubleClick_),this.on(this.tech_,"touchstart",this.handleTechTouchStart_),this.on(this.tech_,"touchmove",this.handleTechTouchMove_),this.on(this.tech_,"touchend",this.handleTechTouchEnd_),this.on(this.tech_,"tap",this.handleTechTap_)},e.removeTechControlsListeners_=function(){this.off(this.tech_,"tap",this.handleTechTap_),this.off(this.tech_,"touchstart",this.handleTechTouchStart_),this.off(this.tech_,"touchmove",this.handleTechTouchMove_),this.off(this.tech_,"touchend",this.handleTechTouchEnd_),this.off(this.tech_,"mouseup",this.handleTechClick_),this.off(this.tech_,"dblclick",this.handleTechDoubleClick_)},e.handleTechReady_=function(){this.triggerReady(),this.cache_.volume&&this.techCall_("setVolume",this.cache_.volume),this.handleTechPosterChange_(),this.handleTechDurationChange_()},e.handleTechLoadStart_=function(){this.removeClass("vjs-ended"),this.removeClass("vjs-seeking"),this.error(null),this.handleTechDurationChange_(),this.paused()?(this.hasStarted(!1),this.trigger("loadstart")):(this.trigger("loadstart"),this.trigger("firstplay")),this.manualAutoplay_(this.autoplay())},e.manualAutoplay_=function(t){var i=this;if(this.tech_&&"string"==typeof t){var e,n=function(){var e=i.muted();i.muted(!0);function t(){i.muted(e)}i.playTerminatedQueue_.push(t);var n=i.play();if(jt(n))return n.catch(t)};if("any"===t&&!0!==this.muted()?jt(e=this.play())&&(e=e.catch(n)):e="muted"===t&&!0!==this.muted()?n():this.play(),jt(e))return e.then(function(){i.trigger({type:"autoplay-success",autoplay:t})}).catch(function(e){i.trigger({type:"autoplay-failure",autoplay:t})})}},e.updateSourceCaches_=function(e){void 0===e&&(e="");var t=e,n="";"string"!=typeof t&&(t=e.src,n=e.type),this.cache_.source=this.cache_.source||{},this.cache_.sources=this.cache_.sources||[],t&&!n&&(n=function(e,t){if(!t)return"";if(e.cache_.source.src===t&&e.cache_.source.type)return e.cache_.source.type;var n=e.cache_.sources.filter(function(e){return e.src===t});if(n.length)return n[0].type;for(var i=e.$$("source"),r=0;r<i.length;r++){var s=i[r];if(s.type&&s.src&&s.src===t)return s.type}return Nn(t)}(this,t)),this.cache_.source=ft({},e,{src:t,type:n});for(var i=this.cache_.sources.filter(function(e){return e.src&&e.src===t}),r=[],s=this.$$("source"),o=[],a=0;a<s.length;a++){var l=ce(s[a]);r.push(l),l.src&&l.src===t&&o.push(l.src)}o.length&&!i.length?this.cache_.sources=r:i.length||(this.cache_.sources=[this.cache_.source]),this.cache_.src=t},e.handleTechSourceset_=function(e){var n=this;if(!this.changingSrc_){var t=function(e){return n.updateSourceCaches_(e)},i=this.currentSource().src,r=e.src;i&&!/^blob:/.test(i)&&/^blob:/.test(r)&&(this.lastSource_&&(this.lastSource_.tech===r||this.lastSource_.player===i)||(t=function(){})),t(r),e.src||this.tech_.any(["sourceset","loadstart"],function(e){if("sourceset"!==e.type){var t=n.techGet("currentSrc");n.lastSource_.tech=t,n.updateSourceCaches_(t)}})}this.lastSource_={player:this.currentSource().src,tech:e.src},this.trigger({src:e.src,type:"sourceset"})},e.hasStarted=function(e){if(void 0===e)return this.hasStarted_;e!==this.hasStarted_&&(this.hasStarted_=e,this.hasStarted_?(this.addClass("vjs-has-started"),this.trigger("firstplay")):this.removeClass("vjs-has-started"))},e.handleTechPlay_=function(){this.removeClass("vjs-ended"),this.removeClass("vjs-paused"),this.addClass("vjs-playing"),this.hasStarted(!0),this.trigger("play")},e.handleTechRateChange_=function(){0<this.tech_.playbackRate()&&0===this.cache_.lastPlaybackRate&&(this.queuedCallbacks_.forEach(function(e){return e.callback(e.event)}),this.queuedCallbacks_=[]),this.cache_.lastPlaybackRate=this.tech_.playbackRate(),this.trigger("ratechange")},e.handleTechWaiting_=function(){var t=this;this.addClass("vjs-waiting"),this.trigger("waiting");var n=this.currentTime();this.on("timeupdate",function e(){n!==t.currentTime()&&(t.removeClass("vjs-waiting"),t.off("timeupdate",e))})},e.handleTechCanPlay_=function(){this.removeClass("vjs-waiting"),this.trigger("canplay")},e.handleTechCanPlayThrough_=function(){this.removeClass("vjs-waiting"),this.trigger("canplaythrough")},e.handleTechPlaying_=function(){this.removeClass("vjs-waiting"),this.trigger("playing")},e.handleTechSeeking_=function(){this.addClass("vjs-seeking"),this.trigger("seeking")},e.handleTechSeeked_=function(){this.removeClass("vjs-seeking"),this.removeClass("vjs-ended"),this.trigger("seeked")},e.handleTechFirstPlay_=function(){this.options_.starttime&&(v.warn("Passing the `starttime` option to the player will be deprecated in 6.0"),this.currentTime(this.options_.starttime)),this.addClass("vjs-has-started"),this.trigger("firstplay")},e.handleTechPause_=function(){this.removeClass("vjs-playing"),this.addClass("vjs-paused"),this.trigger("pause")},e.handleTechEnded_=function(){this.addClass("vjs-ended"),this.options_.loop?(this.currentTime(0),this.play()):this.paused()||this.pause(),this.trigger("ended")},e.handleTechDurationChange_=function(){this.duration(this.techGet_("duration"))},e.handleTechClick_=function(e){Ce(e)&&this.controls_&&(this.paused()?At(this.play()):this.pause())},e.handleTechDoubleClick_=function(t){this.controls_&&(Array.prototype.some.call(this.$$(".vjs-control-bar, .vjs-modal-dialog"),function(e){return e.contains(t.target)})||void 0!==this.options_&&void 0!==this.options_.userActions&&void 0!==this.options_.userActions.doubleClick&&!1===this.options_.userActions.doubleClick||(void 0!==this.options_&&void 0!==this.options_.userActions&&"function"==typeof this.options_.userActions.doubleClick?this.options_.userActions.doubleClick.call(this,t):this.isFullscreen()?this.exitFullscreen():this.requestFullscreen()))},e.handleTechTap_=function(){this.userActive(!this.userActive())},e.handleTechTouchStart_=function(){this.userWasActive=this.userActive()},e.handleTechTouchMove_=function(){this.userWasActive&&this.reportUserActivity()},e.handleTechTouchEnd_=function(e){e.cancelable&&e.preventDefault()},e.handleStageClick_=function(){this.reportUserActivity()},e.toggleFullscreenClass_=function(){this.isFullscreen()?this.addClass("vjs-fullscreen"):this.removeClass("vjs-fullscreen")},e.documentFullscreenChange_=function(e){var t=e.target.player;if(!t||t===this){var n=this.el(),i=p[this.fsApi_.fullscreenElement]===n;!i&&n.matches?i=n.matches(":"+this.fsApi_.fullscreen):!i&&n.msMatchesSelector&&(i=n.msMatchesSelector(":"+this.fsApi_.fullscreen)),this.isFullscreen(i)}},e.handleTechFullscreenChange_=function(e,t){t&&(t.nativeIOSFullscreen&&this.toggleClass("vjs-ios-native-fs"),this.isFullscreen(t.isFullscreen))},e.handleTechFullscreenError_=function(e,t){this.trigger("fullscreenerror",t)},e.togglePictureInPictureClass_=function(){this.isInPictureInPicture()?this.addClass("vjs-picture-in-picture"):this.removeClass("vjs-picture-in-picture")},e.handleTechEnterPictureInPicture_=function(e){this.isInPictureInPicture(!0)},e.handleTechLeavePictureInPicture_=function(e){this.isInPictureInPicture(!1)},e.handleTechError_=function(){var e=this.tech_.error();this.error(e)},e.handleTechTextData_=function(e,t){var n=null;1<arguments.length&&(n=t),this.trigger("textdata",n)},e.getCache=function(){return this.cache_},e.resetCache_=function(){this.cache_={currentTime:0,initTime:0,inactivityTimeout:this.options_.inactivityTimeout,duration:NaN,lastVolume:1,lastPlaybackRate:this.defaultPlaybackRate(),media:null,src:"",source:{},sources:[],volume:1}},e.techCall_=function(e,t){this.ready(function(){if(e in jn)return function(e,t,n,i){return t[n](e.reduce(In(n),i))}(this.middleware_,this.tech_,e,t);if(e in An)return xn(this.middleware_,this.tech_,e,t);try{this.tech_&&this.tech_[e](t)}catch(e){throw v(e),e}},!0)},e.techGet_=function(t){if(this.tech_&&this.tech_.isReady_){if(t in Pn)return function(e,t,n){return e.reduceRight(In(n),t[n]())}(this.middleware_,this.tech_,t);if(t in An)return xn(this.middleware_,this.tech_,t);try{return this.tech_[t]()}catch(e){if(void 0===this.tech_[t])throw v("Video.js: "+t+" method not defined for "+this.techName_+" playback technology.",e),e;if("TypeError"===e.name)throw v("Video.js: "+t+" unavailable on "+this.techName_+" playback technology element.",e),this.tech_.isReady_=!1,e;throw v(e),e}}},e.play=function(){var t=this,e=this.options_.Promise||d.Promise;return e?new e(function(e){t.play_(e)}):this.play_()},e.play_=function(e){var t=this;void 0===e&&(e=At),this.playCallbacks_.push(e);var n=Boolean(!this.changingSrc_&&(this.src()||this.currentSrc()));if(this.waitToPlay_&&(this.off(["ready","loadstart"],this.waitToPlay_),this.waitToPlay_=null),!this.isReady_||!n)return this.waitToPlay_=function(e){t.play_()},this.one(["ready","loadstart"],this.waitToPlay_),void(n||!X&&!q||this.load());var i=this.techGet_("play");null===i?this.runPlayTerminatedQueue_():this.runPlayCallbacks_(i)},e.runPlayTerminatedQueue_=function(){var e=this.playTerminatedQueue_.slice(0);this.playTerminatedQueue_=[],e.forEach(function(e){e()})},e.runPlayCallbacks_=function(t){var e=this.playCallbacks_.slice(0);this.playCallbacks_=[],this.playTerminatedQueue_=[],e.forEach(function(e){e(t)})},e.pause=function(){this.techCall_("pause")},e.paused=function(){return!1!==this.techGet_("paused")},e.played=function(){return this.techGet_("played")||Et(0,0)},e.scrubbing=function(e){if("undefined"==typeof e)return this.scrubbing_;this.scrubbing_=!!e,this.techCall_("setScrubbing",this.scrubbing_),e?this.addClass("vjs-scrubbing"):this.removeClass("vjs-scrubbing")},e.currentTime=function(e){return"undefined"!=typeof e?(e<0&&(e=0),this.isReady_&&!this.changingSrc_&&this.tech_&&this.tech_.isReady_?(this.techCall_("setCurrentTime",e),void(this.cache_.initTime=0)):(this.cache_.initTime=e,this.off("canplay",this.applyInitTime_),void this.one("canplay",this.applyInitTime_))):(this.cache_.currentTime=this.techGet_("currentTime")||0,this.cache_.currentTime)},e.applyInitTime_=function(){this.currentTime(this.cache_.initTime)},e.duration=function(e){if(void 0===e)return void 0!==this.cache_.duration?this.cache_.duration:NaN;(e=parseFloat(e))<0&&(e=1/0),e!==this.cache_.duration&&((this.cache_.duration=e)===1/0?this.addClass("vjs-live"):this.removeClass("vjs-live"),isNaN(e)||this.trigger("durationchange"))},e.remainingTime=function(){return this.duration()-this.currentTime()},e.remainingTimeDisplay=function(){return Math.floor(this.duration())-Math.floor(this.currentTime())},e.buffered=function(){var e=this.techGet_("buffered");return e&&e.length||(e=Et(0,0)),e},e.bufferedPercent=function(){return St(this.buffered(),this.duration())},e.bufferedEnd=function(){var e=this.buffered(),t=this.duration(),n=e.end(e.length-1);return t<n&&(n=t),n},e.volume=function(e){var t;return void 0!==e?(t=Math.max(0,Math.min(1,parseFloat(e))),this.cache_.volume=t,this.techCall_("setVolume",t),void(0<t&&this.lastVolume_(t))):(t=parseFloat(this.techGet_("volume")),isNaN(t)?1:t)},e.muted=function(e){if(void 0===e)return this.techGet_("muted")||!1;this.techCall_("setMuted",e)},e.defaultMuted=function(e){return void 0!==e?this.techCall_("setDefaultMuted",e):this.techGet_("defaultMuted")||!1},e.lastVolume_=function(e){if(void 0===e||0===e)return this.cache_.lastVolume;this.cache_.lastVolume=e},e.supportsFullScreen=function(){return this.techGet_("supportsFullScreen")||!1},e.isFullscreen=function(e){if(void 0===e)return this.isFullscreen_;var t=this.isFullscreen_;return this.isFullscreen_=Boolean(e),this.isFullscreen_!==t&&this.fsApi_.prefixed&&this.trigger("fullscreenchange"),void this.toggleFullscreenClass_()},e.requestFullscreen=function(o){var e=this.options_.Promise||d.Promise;if(e){var a=this;return new e(function(e,n){function i(){a.off("fullscreenerror",r),a.off("fullscreenchange",t)}function t(){i(),e()}function r(e,t){i(),n(t)}a.one("fullscreenchange",t),a.one("fullscreenerror",r);var s=a.requestFullscreenHelper_(o);if(s)return s.then(i,i),s})}return this.requestFullscreenHelper_()},e.requestFullscreenHelper_=function(e){var t,n=this;if(this.fsApi_.prefixed||(t=this.options_.fullscreen&&this.options_.fullscreen.options||{},void 0!==e&&(t=e)),this.fsApi_.requestFullscreen){var i=this.el_[this.fsApi_.requestFullscreen](t);return i&&i.then(function(){return n.isFullscreen(!0)},function(){return n.isFullscreen(!1)}),i}this.tech_.supportsFullScreen()?this.techCall_("enterFullScreen"):this.enterFullWindow()},e.exitFullscreen=function(){var e=this.options_.Promise||d.Promise;if(e){var o=this;return new e(function(e,n){function i(){o.off("fullscreenerror",r),o.off("fullscreenchange",t)}function t(){i(),e()}function r(e,t){i(),n(t)}o.one("fullscreenchange",t),o.one("fullscreenerror",r);var s=o.exitFullscreenHelper_();if(s)return s.then(i,i),s})}return this.exitFullscreenHelper_()},e.exitFullscreenHelper_=function(){var e=this;if(this.fsApi_.requestFullscreen){var t=p[this.fsApi_.exitFullscreen]();return t&&t.then(function(){return e.isFullscreen(!1)}),t}this.tech_.supportsFullScreen()?this.techCall_("exitFullScreen"):this.exitFullWindow()},e.enterFullWindow=function(){this.isFullscreen(!0),this.isFullWindow=!0,this.docOrigOverflow=p.documentElement.style.overflow,We(p,"keydown",this.boundFullWindowOnEscKey_),p.documentElement.style.overflow="hidden",se(p.body,"vjs-full-window"),this.trigger("enterFullWindow")},e.fullWindowOnEscKey=function(e){Ot.isEventKey(e,"Esc")&&(!0===this.isFullscreen()?this.exitFullscreen():this.exitFullWindow())},e.exitFullWindow=function(){this.isFullscreen(!1),this.isFullWindow=!1,Ue(p,"keydown",this.boundFullWindowOnEscKey_),p.documentElement.style.overflow=this.docOrigOverflow,oe(p.body,"vjs-full-window"),this.trigger("exitFullWindow")},e.disablePictureInPicture=function(e){if(void 0===e)return this.techGet_("disablePictureInPicture");this.techCall_("setDisablePictureInPicture",e),this.options_.disablePictureInPicture=e,this.trigger("disablepictureinpicturechanged")},e.isInPictureInPicture=function(e){return void 0!==e?(this.isInPictureInPicture_=!!e,void this.togglePictureInPictureClass_()):!!this.isInPictureInPicture_},e.requestPictureInPicture=function(){if("pictureInPictureEnabled"in p&&!1===this.disablePictureInPicture())return this.techGet_("requestPictureInPicture")},e.exitPictureInPicture=function(){if("pictureInPictureEnabled"in p)return p.exitPictureInPicture()},e.handleKeyDown=function(e){var t=this.options_.userActions;if(t&&t.hotkeys){!function(e){var t=e.tagName.toLowerCase();if(e.isContentEditable)return!0;if("input"===t)return-1===["button","checkbox","hidden","radio","reset","submit"].indexOf(e.type);return-1!==["textarea"].indexOf(t)}(this.el_.ownerDocument.activeElement)&&("function"==typeof t.hotkeys?t.hotkeys.call(this,e):this.handleHotkeys(e))}},e.handleHotkeys=function(e){var t=this.options_.userActions?this.options_.userActions.hotkeys:{},n=t.fullscreenKey,i=void 0===n?function(e){return Ot.isEventKey(e,"f")}:n,r=t.muteKey,s=void 0===r?function(e){return Ot.isEventKey(e,"m")}:r,o=t.playPauseKey,a=void 0===o?function(e){return Ot.isEventKey(e,"k")||Ot.isEventKey(e,"Space")}:o;if(i.call(this,e)){e.preventDefault(),e.stopPropagation();var l=mt.getComponent("FullscreenToggle");!1!==p[this.fsApi_.fullscreenEnabled]&&l.prototype.handleClick.call(this,e)}else if(s.call(this,e)){e.preventDefault(),e.stopPropagation(),mt.getComponent("MuteToggle").prototype.handleClick.call(this,e)}else if(a.call(this,e)){e.preventDefault(),e.stopPropagation(),mt.getComponent("PlayToggle").prototype.handleClick.call(this,e)}},e.canPlayType=function(e){for(var t,n=0,i=this.options_.techOrder;n<i.length;n++){var r=i[n],s=kn.getTech(r);if(s=s||mt.getComponent(r)){if(s.isSupported()&&(t=s.canPlayType(e)))return t}else v.error('The "'+r+'" tech is undefined. Skipped browser support check for that tech.')}return""},e.selectSource=function(e){function t(e,n,i){var r;return e.some(function(t){return n.some(function(e){if(r=i(t,e))return!0})}),r}function n(e,t){var n=e[0];if(e[1].canPlaySource(t,r.options_[n.toLowerCase()]))return{source:t,tech:n}}var i,r=this,s=this.options_.techOrder.map(function(e){return[e,kn.getTech(e)]}).filter(function(e){var t=e[0],n=e[1];return n?n.isSupported():(v.error('The "'+t+'" tech is undefined. Skipped browser support check for that tech.'),!1)});return(this.options_.sourceOrder?t(e,s,(i=n,function(e,t){return i(t,e)})):t(s,e,n))||!1},e.src=function(e){var n=this;if("undefined"==typeof e)return this.cache_.src||"";var i=function t(e){if(Array.isArray(e)){var n=[];e.forEach(function(e){e=t(e),Array.isArray(e)?n=n.concat(e):T(e)&&n.push(e)}),e=n}else e="string"==typeof e&&e.trim()?[Mn({src:e})]:T(e)&&"string"==typeof e.src&&e.src&&e.src.trim()?[Mn(e)]:[];return e}(e);i.length?(this.changingSrc_=!0,this.cache_.sources=i,this.updateSourceCaches_(i[0]),wn(this,i[0],function(e,t){if(n.middleware_=t,n.cache_.sources=i,n.updateSourceCaches_(e),n.src_(e))return 1<i.length?n.src(i.slice(1)):(n.changingSrc_=!1,n.setTimeout(function(){this.error({code:4,message:this.localize(this.options_.notSupportedMessage)})},0),void n.triggerReady());!function(e,t){e.forEach(function(e){return e.setTech&&e.setTech(t)})}(t,n.tech_)})):this.setTimeout(function(){this.error({code:4,message:this.localize(this.options_.notSupportedMessage)})},0)},e.src_=function(e){var t=this,n=this.selectSource([e]);return!n||(function(e,t){return pt(e)===pt(t)}(n.tech,this.techName_)?this.ready(function(){this.tech_.constructor.prototype.hasOwnProperty("setSource")?this.techCall_("setSource",e):this.techCall_("src",e.src),this.changingSrc_=!1},!0):(this.changingSrc_=!0,this.loadTech_(n.tech,n.source),this.tech_.ready(function(){t.changingSrc_=!1})),!1)},e.load=function(){this.techCall_("load")},e.reset=function(){var e=this,t=this.options_.Promise||d.Promise;this.paused()||!t?this.doReset_():At(this.play().then(function(){return e.doReset_()}))},e.doReset_=function(){this.tech_&&this.tech_.clearTracks("text"),this.resetCache_(),this.poster(""),this.loadTech_(this.options_.techOrder[0],null),this.techCall_("reset"),this.resetControlBarUI_(),at(this)&&this.trigger("playerreset")},e.resetControlBarUI_=function(){this.resetProgressBar_(),this.resetPlaybackRate_(),this.resetVolumeBar_()},e.resetProgressBar_=function(){this.currentTime(0);var e=this.controlBar,t=e.durationDisplay,n=e.remainingTimeDisplay;t&&t.updateContent(),n&&n.updateContent()},e.resetPlaybackRate_=function(){this.playbackRate(this.defaultPlaybackRate()),this.handleTechRateChange_()},e.resetVolumeBar_=function(){this.volume(1),this.trigger("volumechange")},e.currentSources=function(){var e=this.currentSource(),t=[];return 0!==Object.keys(e).length&&t.push(e),this.cache_.sources||t},e.currentSource=function(){return this.cache_.source||{}},e.currentSrc=function(){return this.currentSource()&&this.currentSource().src||""},e.currentType=function(){return this.currentSource()&&this.currentSource().type||""},e.preload=function(e){return void 0!==e?(this.techCall_("setPreload",e),void(this.options_.preload=e)):this.techGet_("preload")},e.autoplay=function(e){if(void 0===e)return this.options_.autoplay||!1;var t;"string"==typeof e&&/(any|play|muted)/.test(e)?(this.options_.autoplay=e,this.manualAutoplay_(e),t=!1):this.options_.autoplay=!!e,t="undefined"==typeof t?this.options_.autoplay:t,this.tech_&&this.techCall_("setAutoplay",t)},e.playsinline=function(e){return void 0!==e?(this.techCall_("setPlaysinline",e),this.options_.playsinline=e,this):this.techGet_("playsinline")},e.loop=function(e){return void 0!==e?(this.techCall_("setLoop",e),void(this.options_.loop=e)):this.techGet_("loop")},e.poster=function(e){if(void 0===e)return this.poster_;(e=e||"")!==this.poster_&&(this.poster_=e,this.techCall_("setPoster",e),this.isPosterFromTech_=!1,this.trigger("posterchange"))},e.handleTechPosterChange_=function(){if((!this.poster_||this.options_.techCanOverridePoster)&&this.tech_&&this.tech_.poster){var e=this.tech_.poster()||"";e!==this.poster_&&(this.poster_=e,this.isPosterFromTech_=!0,this.trigger("posterchange"))}},e.controls=function(e){if(void 0===e)return!!this.controls_;e=!!e,this.controls_!==e&&(this.controls_=e,this.usingNativeControls()&&this.techCall_("setControls",e),this.controls_?(this.removeClass("vjs-controls-disabled"),this.addClass("vjs-controls-enabled"),this.trigger("controlsenabled"),this.usingNativeControls()||this.addTechControlsListeners_()):(this.removeClass("vjs-controls-enabled"),this.addClass("vjs-controls-disabled"),this.trigger("controlsdisabled"),this.usingNativeControls()||this.removeTechControlsListeners_()))},e.usingNativeControls=function(e){if(void 0===e)return!!this.usingNativeControls_;e=!!e,this.usingNativeControls_!==e&&(this.usingNativeControls_=e,this.usingNativeControls_?(this.addClass("vjs-using-native-controls"),this.trigger("usingnativecontrols")):(this.removeClass("vjs-using-native-controls"),this.trigger("usingcustomcontrols")))},e.error=function(e){if(void 0===e)return this.error_||null;if(this.options_.suppressNotSupportedError&&e&&4===e.code){var t=function(){this.error(e)};return this.options_.suppressNotSupportedError=!1,this.any(["click","touchstart"],t),void this.one("loadstart",function(){this.off(["click","touchstart"],t)})}if(null===e)return this.error_=e,this.removeClass("vjs-error"),void(this.errorDisplay&&this.errorDisplay.close());this.error_=new wt(e),this.addClass("vjs-error"),v.error("(CODE:"+this.error_.code+" "+wt.errorTypes[this.error_.code]+")",this.error_.message,this.error_),this.trigger("error")},e.reportUserActivity=function(e){this.userActivity_=!0},e.userActive=function(e){if(void 0===e)return this.userActive_;if((e=!!e)!==this.userActive_){if(this.userActive_=e,this.userActive_)return this.userActivity_=!0,this.removeClass("vjs-user-inactive"),this.addClass("vjs-user-active"),void this.trigger("useractive");this.tech_&&this.tech_.one("mousemove",function(e){e.stopPropagation(),e.preventDefault()}),this.userActivity_=!1,this.removeClass("vjs-user-active"),this.addClass("vjs-user-inactive"),this.trigger("userinactive")}},e.listenForUserActivity_=function(){function e(e){r(),this.clearInterval(t)}var t,n,i,r=$e(this,this.reportUserActivity);this.on("mousedown",function(){r(),this.clearInterval(t),t=this.setInterval(r,250)}),this.on("mousemove",function(e){e.screenX===n&&e.screenY===i||(n=e.screenX,i=e.screenY,r())}),this.on("mouseup",e),this.on("mouseleave",e);var s,o=this.getChild("controlBar");!o||q||N||(o.on("mouseenter",function(e){this.player().cache_.inactivityTimeout=this.player().options_.inactivityTimeout,this.player().options_.inactivityTimeout=0}),o.on("mouseleave",function(e){this.player().options_.inactivityTimeout=this.player().cache_.inactivityTimeout})),this.on("keydown",r),this.on("keyup",r),this.setInterval(function(){if(this.userActivity_){this.userActivity_=!1,this.userActive(!0),this.clearTimeout(s);var e=this.options_.inactivityTimeout;e<=0||(s=this.setTimeout(function(){this.userActivity_||this.userActive(!1)},e))}},250)},e.playbackRate=function(e){if(void 0===e)return this.tech_&&this.tech_.featuresPlaybackRate?this.cache_.lastPlaybackRate||this.techGet_("playbackRate"):1;this.techCall_("setPlaybackRate",e)},e.defaultPlaybackRate=function(e){return void 0!==e?this.techCall_("setDefaultPlaybackRate",e):this.tech_&&this.tech_.featuresPlaybackRate?this.techGet_("defaultPlaybackRate"):1},e.isAudio=function(e){if(void 0===e)return!!this.isAudio_;this.isAudio_=!!e},e.addTextTrack=function(e,t,n){if(this.tech_)return this.tech_.addTextTrack(e,t,n)},e.addRemoteTextTrack=function(e,t){if(this.tech_)return this.tech_.addRemoteTextTrack(e,t)},e.removeRemoteTextTrack=function(e){void 0===e&&(e={});var t=e.track;if(t=t||e,this.tech_)return this.tech_.removeRemoteTextTrack(t)},e.getVideoPlaybackQuality=function(){return this.techGet_("getVideoPlaybackQuality")},e.videoWidth=function(){return this.tech_&&this.tech_.videoWidth&&this.tech_.videoWidth()||0},e.videoHeight=function(){return this.tech_&&this.tech_.videoHeight&&this.tech_.videoHeight()||0},e.language=function(e){if(void 0===e)return this.language_;this.language_!==String(e).toLowerCase()&&(this.language_=String(e).toLowerCase(),at(this)&&this.trigger("languagechange"))},e.languages=function(){return ft(c.prototype.options_.languages,this.languages_)},e.toJSON=function(){var e=ft(this.options_),t=e.tracks;e.tracks=[];for(var n=0;n<t.length;n++){var i=t[n];(i=ft(i)).player=void 0,e.tracks[n]=i}return e},e.createModal=function(e,t){var n=this;(t=t||{}).content=e||"";var i=new Dt(this,t);return this.addChild(i),i.on("dispose",function(){n.removeChild(i)}),i.open(),i},e.updateCurrentBreakpoint_=function(){if(this.responsive())for(var e=this.currentBreakpoint(),t=this.currentWidth(),n=0;n<br.length;n++){var i=br[n];if(t<=this.breakpoints_[i]){if(e===i)return;e&&this.removeClass(Tr[e]),this.addClass(Tr[i]),this.breakpoint_=i;break}}},e.removeCurrentBreakpoint_=function(){var e=this.currentBreakpointClass();this.breakpoint_="",e&&this.removeClass(e)},e.breakpoints=function(e){return void 0===e||(this.breakpoint_="",this.breakpoints_=b({},kr,e),this.updateCurrentBreakpoint_()),b(this.breakpoints_)},e.responsive=function(e){return void 0===e?this.responsive_:(e=Boolean(e))!==this.responsive_?((this.responsive_=e)?(this.on("playerresize",this.updateCurrentBreakpoint_),this.updateCurrentBreakpoint_()):(this.off("playerresize",this.updateCurrentBreakpoint_),this.removeCurrentBreakpoint_()),e):void 0},e.currentBreakpoint=function(){return this.breakpoint_},e.currentBreakpointClass=function(){return Tr[this.breakpoint_]||""},e.loadMedia=function(e,t){var n=this;if(e&&"object"==typeof e){this.reset(),this.cache_.media=ft(e);var i=this.cache_.media,r=i.artwork,s=i.poster,o=i.src,a=i.textTracks;!r&&s&&(this.cache_.media.artwork=[{src:s,type:Nn(s)}]),o&&this.src(o),s&&this.poster(s),Array.isArray(a)&&a.forEach(function(e){return n.addRemoteTextTrack(e,!1)}),this.ready(t)}},e.getMedia=function(){if(this.cache_.media)return ft(this.cache_.media);var e=this.poster(),t={src:this.currentSources(),textTracks:Array.prototype.map.call(this.remoteTextTracks(),function(e){return{kind:e.kind,label:e.label,language:e.language,src:e.src}})};return e&&(t.poster=e,t.artwork=[{src:t.poster,type:Nn(t.poster)}]),t},c.getTagSettings=function(e){var t={sources:[],tracks:[]},n=ce(e),i=n["data-setup"];if(re(e,"vjs-fill")&&(n.fill=!0),re(e,"vjs-fluid")&&(n.fluid=!0),null!==i){var r=Pt(i||"{}"),s=r[0],o=r[1];s&&v.error(s),b(n,o)}if(b(t,n),e.hasChildNodes())for(var a=e.childNodes,l=0,c=a.length;l<c;l++){var u=a[l],h=u.nodeName.toLowerCase();"source"===h?t.sources.push(ce(u)):"track"===h&&t.tracks.push(ce(u))}return t},e.flexNotSupported_=function(){var e=p.createElement("i");return!("flexBasis"in e.style||"webkitFlexBasis"in e.style||"mozFlexBasis"in e.style||"msFlexBasis"in e.style||"msFlexOrder"in e.style)},e.debug=function(e){if(void 0===e)return this.debugEnabled_;e?(this.trigger("debugon"),this.previousLogLevel_=this.log.level,this.log.level("debug"),this.debugEnabled_=!0):(this.trigger("debugoff"),this.log.level(this.previousLogLevel_),this.previousLogLevel_=void 0,this.debugEnabled_=!1)},c}(mt);bn.names.forEach(function(e){var t=bn[e];Cr.prototype[t.getterName]=function(){return this.tech_?this.tech_[t.getterName]():(this[t.privateName]=this[t.privateName]||new t.ListClass,this[t.privateName])}}),Cr.prototype.crossorigin=Cr.prototype.crossOrigin,Cr.players={};var Er=d.navigator;Cr.prototype.options_={techOrder:kn.defaultTechOrder_,html5:{},inactivityTimeout:2e3,playbackRates:[],liveui:!1,children:["mediaLoader","posterImage","textTrackDisplay","loadingSpinner","bigPlayButton","liveTracker","controlBar","errorDisplay","textTrackSettings","resizeManager"],language:Er&&(Er.languages&&Er.languages[0]||Er.userLanguage||Er.language)||"en",languages:{},notSupportedMessage:"No compatible source was found for this media.",fullscreen:{options:{navigationUI:"hide"}},breakpoints:{},responsive:!1},["ended","seeking","seekable","networkState","readyState"].forEach(function(e){Cr.prototype[e]=function(){return this.techGet_(e)}}),yr.forEach(function(e){Cr.prototype["handleTech"+pt(e)+"_"]=function(){return this.trigger(e)}}),mt.registerComponent("Player",Cr);var Sr=t(function(n){function i(e,t){return n.exports=i=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},i(e,t)}n.exports=i});function wr(e){return Mr.hasOwnProperty(e)}function xr(e){return wr(e)?Mr[e]:void 0}function Pr(e,t){e[Or]=e[Or]||{},e[Or][t]=!0}function jr(e,t,n){var i=(n?"before":"")+"pluginsetup";e.trigger(i,t),e.trigger(i+":"+t.name,t)}function Ar(r,s){return s.prototype.name=r,function(){jr(this,{name:r,plugin:s,instance:null},!0);for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var i=Lr(s,[this].concat(t));return this[r]=function(){return i},jr(this,i.getEventHash()),i}}var Ir=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(e){return!1}},Lr=t(function(i){function r(e,t,n){return Ir()?i.exports=r=Reflect.construct:i.exports=r=function(e,t,n){var i=[null];i.push.apply(i,t);var r=new(Function.bind.apply(e,i));return n&&Sr(r,n.prototype),r},r.apply(null,arguments)}i.exports=r}),Nr="plugin",Or="activePlugins_",Mr={},Dr=function(){function n(e){if(this.constructor===n)throw new Error("Plugin must be sub-classed; not directly instantiated.");this.player=e,this.log||(this.log=this.player.log.createLogger(this.name)),ct(this),delete this.trigger,ht(this,this.constructor.defaultState),Pr(e,this.name),this.dispose=$e(this,this.dispose),e.on("dispose",this.dispose)}var e=n.prototype;return e.version=function(){return this.constructor.VERSION},e.getEventHash=function(e){return void 0===e&&(e={}),e.name=this.name,e.plugin=this.constructor,e.instance=this,e},e.trigger=function(e,t){return void 0===t&&(t={}),qe(this.eventBusEl_,e,this.getEventHash(t))},e.handleStateChanged=function(e){},e.dispose=function(){var e=this.name,t=this.player;this.trigger("dispose"),this.off(),t.off("dispose",this.dispose),t[Or][e]=!1,this.player=this.state=null,t[e]=Ar(e,Mr[e])},n.isBasic=function(e){var t="string"==typeof e?xr(e):e;return"function"==typeof t&&!n.prototype.isPrototypeOf(t.prototype)},n.registerPlugin=function(e,t){if("string"!=typeof e)throw new Error('Illegal plugin name, "'+e+'", must be a string, was '+typeof e+".");if(wr(e))v.warn('A plugin named "'+e+'" already exists. You may want to avoid re-registering plugins!');else if(Cr.prototype.hasOwnProperty(e))throw new Error('Illegal plugin name, "'+e+'", cannot share a name with an existing player method!');if("function"!=typeof t)throw new Error('Illegal plugin for "'+e+'", must be a function, was '+typeof t+".");return Mr[e]=t,e!==Nr&&(n.isBasic(t)?Cr.prototype[e]=function(t,n){function i(){jr(this,{name:t,plugin:n,instance:null},!0);var e=n.apply(this,arguments);return Pr(this,t),jr(this,{name:t,plugin:n,instance:e}),e}return Object.keys(n).forEach(function(e){i[e]=n[e]}),i}(e,t):Cr.prototype[e]=Ar(e,t)),t},n.deregisterPlugin=function(e){if(e===Nr)throw new Error("Cannot de-register base plugin.");wr(e)&&(delete Mr[e],delete Cr.prototype[e])},n.getPlugins=function(e){var n;return void 0===e&&(e=Object.keys(Mr)),e.forEach(function(e){var t=xr(e);t&&((n=n||{})[e]=t)}),n},n.getPluginVersion=function(e){var t=xr(e);return t&&t.VERSION||""},n}();Dr.getPlugin=xr,Dr.BASE_PLUGIN_NAME=Nr,Dr.registerPlugin(Nr,Dr),Cr.prototype.usingPlugin=function(e){return!!this[Or]&&!0===this[Or][e]},Cr.prototype.hasPlugin=function(e){return!!wr(e)};var Fr=function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&Sr(e,t)},Rr=function(e){return 0===e.indexOf("#")?e.slice(1):e};function Br(e,n,t){var i=Br.getPlayer(e);if(i)return n&&v.warn('Player "'+e+'" is already initialised. Options will not be applied.'),t&&i.ready(t),i;var r="string"==typeof e?Se("#"+Rr(e)):e;if(!J(r))throw new TypeError("The element or ID supplied is not valid. (videojs)");r.ownerDocument.defaultView&&r.ownerDocument.body.contains(r)||v.warn("The element supplied is not included in the DOM"),n=n||{},Br.hooks("beforesetup").forEach(function(e){var t=e(r,ft(n));T(t)&&!Array.isArray(t)?n=ft(n,t):v.error("please return an object in beforesetup hooks")});var s=mt.getComponent("Player");return i=new s(r,n,t),Br.hooks("setup").forEach(function(e){return e(i)}),i}if(Br.hooks_={},Br.hooks=function(e,t){return Br.hooks_[e]=Br.hooks_[e]||[],t&&(Br.hooks_[e]=Br.hooks_[e].concat(t)),Br.hooks_[e]},Br.hook=function(e,t){Br.hooks(e,t)},Br.hookOnce=function(n,e){Br.hooks(n,[].concat(e).map(function(t){return function e(){return Br.removeHook(n,e),t.apply(void 0,arguments)}}))},Br.removeHook=function(e,t){var n=Br.hooks(e).indexOf(t);return!(n<=-1)&&(Br.hooks_[e]=Br.hooks_[e].slice(),Br.hooks_[e].splice(n,1),!0)},!0!==d.VIDEOJS_NO_DYNAMIC_STYLE&&Q()){var Hr=Se(".vjs-styles-defaults");if(!Hr){Hr=Le("vjs-styles-defaults");var Vr=Se("head");Vr&&Vr.insertBefore(Hr,Vr.firstChild),Ne(Hr,"\n      .video-js {\n        width: 300px;\n        height: 150px;\n      }\n\n      .vjs-fluid {\n        padding-top: 56.25%\n      }\n    ")}}return Ae(1,Br),Br.VERSION=h,Br.options=Cr.prototype.options_,Br.getPlayers=function(){return Cr.players},Br.getPlayer=function(e){var t,n=Cr.players;if("string"==typeof e){var i=Rr(e),r=n[i];if(r)return r;t=Se("#"+i)}else t=e;if(J(t)){var s=t,o=s.player,a=s.playerId;if(o||n[a])return o||n[a]}},Br.getAllPlayers=function(){return Object.keys(Cr.players).map(function(e){return Cr.players[e]}).filter(Boolean)},Br.players=Cr.players,Br.getComponent=mt.getComponent,Br.registerComponent=function(e,t){kn.isTech(t)&&v.warn("The "+e+" tech was registered as a component. It should instead be registered using videojs.registerTech(name, tech)"),mt.registerComponent.call(mt,e,t)},Br.getTech=kn.getTech,Br.registerTech=kn.registerTech,Br.use=function(e,t){Cn[e]=Cn[e]||[],Cn[e].push(t)},Object.defineProperty(Br,"middleware",{value:{},writeable:!1,enumerable:!0}),Object.defineProperty(Br.middleware,"TERMINATOR",{value:Sn,writeable:!1,enumerable:!0}),Br.browser=G,Br.TOUCH_ENABLED=z,Br.extend=function(e,t){void 0===t&&(t={});var n=function(){e.apply(this,arguments)},i={};for(var r in"object"==typeof t?(t.constructor!==Object.prototype.constructor&&(n=t.constructor),i=t):"function"==typeof t&&(n=t),Fr(n,e),e&&(n.super_=e),i)i.hasOwnProperty(r)&&(n.prototype[r]=i[r]);return n},Br.mergeOptions=ft,Br.bind=$e,Br.registerPlugin=Dr.registerPlugin,Br.deregisterPlugin=Dr.deregisterPlugin,Br.plugin=function(e,t){return v.warn("videojs.plugin() is deprecated; use videojs.registerPlugin() instead"),Dr.registerPlugin(e,t)},Br.getPlugins=Dr.getPlugins,Br.getPlugin=Dr.getPlugin,Br.getPluginVersion=Dr.getPluginVersion,Br.addLanguage=function(e,t){var n;return e=(""+e).toLowerCase(),Br.options.languages=ft(Br.options.languages,((n={})[e]=t,n)),Br.options.languages[e]},Br.log=v,Br.createLogger=g,Br.createTimeRange=Br.createTimeRanges=Et,Br.formatTime=Qn,Br.setFormatTime=function(e){Yn=e},Br.resetFormatTime=function(){Yn=$n},Br.parseUrl=Vt,Br.isCrossOrigin=Wt,Br.EventTarget=Qe,Br.on=We,Br.one=Xe,Br.off=Ue,Br.trigger=qe,Br.xhr=an,Br.TextTrack=fn,Br.AudioTrack=vn,Br.VideoTrack=gn,["isEl","isTextNode","createEl","hasClass","addClass","removeClass","toggleClass","setAttributes","getAttributes","emptyEl","appendContent","insertContent"].forEach(function(e){Br[e]=function(){return v.warn("videojs."+e+"() is deprecated; use videojs.dom."+e+"() instead"),xe[e].apply(null,arguments)}}),Br.computedStyle=C,Br.dom=xe,Br.url=tn,Br.defineLazyProperty=pr,Br});