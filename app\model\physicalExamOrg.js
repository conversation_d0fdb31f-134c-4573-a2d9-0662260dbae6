/**
 * 机构端-注册体检机构表
 * update by wx on 2022-08-02
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const upload_http_path = app.config.upload_http_path || '';


  const PhysicalExamOrgSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    name: { // 机构名称
      type: String,
      trim: true, // 去掉两边空格
      require: true,
      set(val) {
        return (val || '').replace(/（/g, '(').replace(/）/g, ')');
      },
    },
    shortName: { // 机构简称
      type: String,
      trim: true,
      unique: true,
      set(val) {
        return (val || '').replace(/（/g, '(').replace(/）/g, ')');
      },
    },
    formerNames: [ String ], // 机构曾用名
    organization: { // 社会统一信用代码
      type: String,
      trim: true,
      require: true,
    },
    regAddr: { // 注册的省市区
      default: [],
      type: Array,
    },
    address: { // 注册详细地址
      type: String,
      trim: true,
    },
    corp: String, // 法人代表
    managers: [{ type: String, ref: 'PhysicalExamUser' }], // 管理人员id集合
    contract: String, // 联系人
    phoneNum: String, // 联系方式
    lineOfBusiness: { // 业务范围
      type: String,
      trim: true,
      default: '',
    },
    projectNums: { // 业务范围
      type: String,
      trim: true,
      default: '1',
    }, // 1 服务过的企业
    filingType: String, // 备案类型
    physicalAddress: String, // 体检地址
    inspectionItem: String, // 体检项目
    harmFactors: [ Array ], // 危害因素
    regType: { // 组成形式
      type: String,
      default: '',
    },
    egress: Boolean, // 能否开展外出职业健康检查
    introduction: String, // 机构介绍
    ctime: { // 创建/更改时间
      type: Date,
      default: Date.now,
    },
    img: { // 营业执照
      type: String,
      require: true,
      trim: true,
      set(val) {
        return (val && val.indexOf(upload_http_path) !== -1) ? val.split(upload_http_path)[1] : val;

      },
    },
    qualifies: { // 资质证书id集合
      type: Array,
      ref: 'PhysicalExamDetectionMechanism',
      default: [],
    },
    message: String, // 审核结果
    status: { // 状态
      type: Number,
      default: 0, // 0未注册；1已上传营业执照；2已上传资质证书；3审核通过；4审核不通过；5注销
      enum: [ 0, 1, 2, 3, 4, 5 ],
    },
    landline: { // 座机
      type: String,
    },
    administrator: { // 超级管理员，也是绑定了社会统一信用代码的人
      type: String,
      ref: 'PhysicalExamUser',
    },
    sourse: { // 哪个端创建的
      type: String,
      default: 'zyjk', // 比如 'operate' / 'tj'
    },
    defaultAppointPeopleCount: { // 默认预约人数
      type: Number,
      default: 100,
    },
    unitCode: { // 单位编号 【导入进来的】
      type: String,
      default: '',
    },
  });
  PhysicalExamOrgSchema.index({ organization: 1 });
  PhysicalExamOrgSchema.plugin(app.dataMaskPlugin, {
    fields: {
      phoneNum: 'phoneNum',
    },
  });

  return mongoose.model('PhysicalExamOrg', PhysicalExamOrgSchema, 'physicalExamOrgs');

};

