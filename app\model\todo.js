/**
 * @file Todo模型
 * @description 待办事项数据模型
 * <AUTHOR> Assistant
 * @createDate 2024-03-20
 */
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  const TodoSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    title: {
      type: String,
      required: [ true, '标题不能为空' ],
      trim: true,
      maxlength: [ 100, '标题最大长度为100个字符' ],
    },
    content: {
      type: String,
      trim: true,
      maxlength: [ 500, '内容最大长度为500个字符' ],
    },
    completed: {
      type: Boolean,
      default: false,
    },
    dueDate: {
      type: Date,
      get: v => (v ? app.moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    state: {
      type: String,
      default: '1', // 1正常，0删除
      enum: [ '0', '1' ],
    },
  }, {
    timestamps: true,
    versionKey: false,
    collection: 'todos',
    minimize: false,
    strict: true,
  });

  // 创建索引
  TodoSchema.index({ title: 1 });
  TodoSchema.index({ completed: 1 });
  TodoSchema.index({ dueDate: 1 });
  TodoSchema.index({ createdAt: -1 });

  return mongoose.model('Todo', TodoSchema);
};
