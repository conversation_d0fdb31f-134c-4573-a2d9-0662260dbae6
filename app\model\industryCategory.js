/**
 * Created by Administrator on 2015/4/15.
 * 行业分类
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const IndustryCategorySchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    value: String, // 编号
    label: String, // 名字
    sort: Number, // 排序
    isJialei: { type: Number, default: 0 }, // 是否甲类
    riskType: { type: Number, default: 0 }, // 职业病危害风险分类,分为：2:严重 ;1:较重 ; 0:一般
    children: [{ // 下级
      _id: {
        type: String,
        default: shortid.generate,
      },
      value: String, // 编号
      label: String, // 名字
      isJialei: { type: Number, default: 0 }, // 是否甲类
      riskType: { type: Number, default: 0 }, // 职业病危害风险分类,分为：2:严重 ;1:较重 ; 0:一般
      children: [{ // 下级
        _id: {
          type: String,
          default: shortid.generate,
        },
        value: String, // 编号
        label: String, // 名字
        isJialei: { type: Number, default: 0 }, // 是否甲类
        riskType: { type: Number, default: 0 }, // 职业病危害风险分类,分为：2:严重 ;1:较重 ; 0:一般
        children: [{ // 下级
          _id: {
            type: String,
            default: shortid.generate,
          },
          value: String, // 编号
          label: String, // 名字
          isJialei: { type: Number, default: 0 }, // 是否甲类
          riskType: { type: Number, default: 0 }, // 职业病危害风险分类,分为：2:严重 ;1:较重 ; 0:一般
        }],
      }],
    }],
  });

  return mongoose.model('IndustryCategory', IndustryCategorySchema, 'industryCategory');
};
