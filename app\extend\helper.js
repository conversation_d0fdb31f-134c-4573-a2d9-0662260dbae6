require('module-alias/register');
const ImageModule = require('docxtemplater-image-module-free-norepeat');
const Axios = require('axios');
const _ = require('lodash');
// 文件操作对象
const CryptoJS = require('crypto-js');
const fs = require('fs');
const awaitWriteStream = require('await-stream-ready').write;
const streamWormhole = require('stream-wormhole');
const Minio = require('minio');
const crypto = require('crypto');
// const stat = fs.stat;
const path = require('path');
const PizZip = require('pizzip');
const Docxtemplater = require('docxtemplater');
const { mkdirp } = require('mkdirp');
const expressions = require('angular-expressions');
const assign = require('lodash/assign');
const last = require('lodash/last');
const archiver = require('archiver');
const url = require('url');
const { integratesBy } = require('@utils');
const { Readable } = require('stream'); // 导入 Readable 类

expressions.filters.lower = function(input) {
  if (!input) return input;
  return input.toLowerCase();
};
function angularParser(tag) {
  if (tag === '.') {
    return {
      get(s) {
        return s;
      },
    };
  }
  const expr = expressions.compile(
    tag.replace(/(’|‘)/g, "'").replace(/(“|”)/g, '"')
  );
  return {
    get(scope, context) {
      let obj = {};
      const index = last(context.scopePathItem);
      const scopeList = context.scopeList;
      const num = context.num;
      for (let i = 0, len = num + 1; i < len; i++) {
        obj = assign(obj, scopeList[i]);
      }
      obj = assign(obj, { $index: index });
      const res = expr(scope, obj);
      if (res && res !== null) {
        return res;
      }
      return '';
    },
  };
}

// 阿里云初始化
const RPCClient = require('@alicloud/pop-core').RPCClient;
function initVodClient(accessKeyId, secretAccessKey) {
  const regionId = 'cn-shanghai'; // 点播服务接入区域
  const client = new RPCClient({
    accessKeyId,
    secretAccessKey,
    endpoint: 'http://vod.' + regionId + '.aliyuncs.com',
    apiVersion: '2017-03-21',
  });
  return client;
}

module.exports = {
  // 获取钉钉token凭据
  async dingTalkApiGettoken() {
    const { ctx } = this;
    const oapiHost = 'https://oapi.dingtalk.com';
    // 多谱应用凭证
    // CorpId:'ding2fa4df5d78220543',
    // AgentId: 869258467 ,
    // AppKey: dingfrf5xbl2uhcecjpn,
    // AppSecret: bKNHDi4E6YUoPVCubcDsWbVuPwtFkHwfCZQguYxEUZG1t-wt_XKEb_V8EfUf-MhY
    return new Promise(async (resolve, rejected) => {
      try {
        const getOptions = {
          // 要发送的数据，将自动进行字符串化
          data: {
            appkey: this.app.config.dingTalk.AppKey,
            appsecret: this.app.config.dingTalk.AppSecret,
          },
          timeout: 8000, // 请求超时
          dataType: 'json', // 字符串-相应的类型，可能是text或json
          method: 'GET',
        };
        // egg中请求外部接口：this.ctx.curl(url,optionsopt)
        const tokenBack = await ctx.curl(oapiHost + '/gettoken', getOptions);
        const access_token = tokenBack.data.access_token;
        resolve({ access_token });
      } catch (error) {
        console.log('钉钉取凭证接口出错！', error);
        const err = {
          errcode: 1,
          errmsg: error,
        };
        rejected(err);
      }
    });
  },
  // 通过人员列表，发送消息给钉钉用户
  async dingTalkConversation(list) {
    // console.log('list=====================', list)
    // console.log('到钉钉接口了吗');
    const { ctx } = this;
    try {
      // return
      const access_token = (await this.dingTalkApiGettoken()).access_token;
      // console.log('userid====',JSON.stringify(list.userid_list))
      const getUser = {
        data: {
          agent_id: this.config.dingTalk.AgentId,
          userid_list: list.userid_list, // 人员列表
          msg: JSON.stringify(list.msg),
        },
        dataType: 'json',
        method: 'POST',
      };
      await ctx.curl(
        'https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2?access_token=' +
          access_token,
        getUser
      );
    } catch (error) {
      console.log(error);
    }
    // console.log('到了');
  },

  // 通过电话号码获取钉钉用户信息
  async dingTalkGteUserInfo(phoneNumbers) {
    const { ctx } = this;
    const oapiHost = 'https://oapi.dingtalk.com';
    try {
      // return
      const access_token = (await this.dingTalkApiGettoken()).access_token;
      let dingTalkUserId = '';
      let getUserInfoOption = {};
      let dingTalkUserInfoBack = {};
      let getUserIdOption = {};
      let dingTalkUserIdBack = {};
      const usersInfo = [];
      for (let i = 0; i < phoneNumbers.length; i++) {
        getUserIdOption = {
          data: {
            mobile: phoneNumbers[i],
          },
          dataType: 'json',
          method: 'POST',
        };
        dingTalkUserIdBack = await ctx.curl(
          'https://oapi.dingtalk.com/topapi/v2/user/getbymobile?access_token=' +
            access_token,
          getUserIdOption
        );
        // console.log('用户id=======',i,dingTalkUserIdBack)
        dingTalkUserId =
          dingTalkUserIdBack.data.result &&
          dingTalkUserIdBack.data.result.userid;

        getUserInfoOption = {
          data: {
            userid: dingTalkUserId,
          },
          dataType: 'json',
          method: 'get',
        };
        dingTalkUserInfoBack = await ctx.curl(
          oapiHost + '/user/get?access_token=' + access_token,
          getUserInfoOption
        );
        usersInfo.push(dingTalkUserInfoBack.data);
      }
      return usersInfo;
    } catch (error) {
      console.log(error);
    }
  },

  async riskSort(ctx, company, allIndustryCategory, table2) {
    console.log('分类中...');
    table2;
    try {
      const year = new Date().toISOString();
      const nearTwoYears = [
        year.slice(0, 4),
        JSON.stringify(year.slice(0, 4) - 1),
      ];
      // 载入该企业所有在案累计的职业病例
      const cumulativeTotalCount = await ctx.service.db.find(
        'Odisease',
        {
          EnterpriseID: company._id,
        },
        {},
        { count: true }
      );

      let assessmentSort = 1; // 分类 ： 1 : 丙类  ， 2 : 乙类  3 : 甲类
      // console.log("当前企业分类信息",companyTestDataBack.industryCategory);
      if (company.industryCategory && company.industryCategory.length > 0) {
        const curCat =
          company.industryCategory[company.industryCategory.length - 1];
        const curCatObj = _.find(allIndustryCategory, { value: curCat }); // 当前分类
        if ((curCatObj && curCatObj.isJialei) || cumulativeTotalCount > 1) {
          assessmentSort = 3;
          return assessmentSort;
        }
      }

      let checkResult = await ctx.model.CheckAssessment.find({
        EnterpriseID: company._id,
        year: new Date(nearTwoYears[0]),
      }).sort({ year: -1 });
      if (checkResult && checkResult.length > 0) {
        checkResult = checkResult[0];
      } else {
        return assessmentSort;
      }

      let checkResultFormData = [];

      if (
        checkResult.biologicalFactors &&
        checkResult.biologicalFactors.formData.length > 0
      ) {
        checkResult.biologicalFactors.formData.forEach(item => {
          item.sort = '生物因素';
          item.sortValue = 'biologicalFactors';
        });
        checkResultFormData.push(checkResult.biologicalFactors.formData);
      }
      if (
        checkResult.chemistryFactors &&
        checkResult.chemistryFactors.formData.length > 0
      ) {
        checkResult.chemistryFactors.formData.forEach(item => {
          item.sort = '化学';
          item.sortValue = 'chemistryFactors';
        });
        checkResultFormData.push(checkResult.chemistryFactors.formData);
      }
      if (
        checkResult.dustFactors &&
        checkResult.dustFactors.formData.length > 0
      ) {
        checkResult.dustFactors.formData.forEach(item => {
          item.sort = '粉尘';
          item.sortValue = 'dustFactors';
        });
        checkResultFormData.push(checkResult.dustFactors.formData);
      }
      if (
        checkResult.handBorneVibrationFactors &&
        checkResult.handBorneVibrationFactors.formData.length > 0
      ) {
        checkResult.handBorneVibrationFactors.formData.forEach(item => {
          item.sort = '物理';
          item.sortName = '手传震动';
          item.sortValue = 'handBorneVibrationFactors';
        });
        checkResultFormData.push(
          checkResult.handBorneVibrationFactors.formData
        );
      }
      if (
        checkResult.heatFactors &&
        checkResult.heatFactors.formData.length > 0
      ) {
        checkResult.heatFactors.formData.forEach(item => {
          item.sort = '物理';
          item.sortName = '高温';
          item.sortValue = 'heatFactors';
        });
        checkResultFormData.push(checkResult.heatFactors.formData);
      }
      if (
        checkResult.highFrequencyEleFactors &&
        checkResult.highFrequencyEleFactors.formData.length > 0
      ) {
        checkResult.highFrequencyEleFactors.formData.forEach(item => {
          item.sort = '物理';
          item.sortName = '高频电磁场';
          item.sortValue = 'highFrequencyEleFactors';
        });
        checkResultFormData.push(checkResult.highFrequencyEleFactors.formData);
      }
      if (
        checkResult.laserFactors &&
        checkResult.laserFactors.formData.length > 0
      ) {
        checkResult.laserFactors.formData.forEach(item => {
          item.sort = '物理';
          item.sortName = '高温';
          item.sortValue = 'laserFactors';
        });
        checkResultFormData.push(checkResult.laserFactors.formData);
      }
      if (
        checkResult.microwaveFactors &&
        checkResult.microwaveFactors.formData.length > 0
      ) {
        checkResult.microwaveFactors.formData.forEach(item => {
          item.sort = '物理';
          item.sortName = '微波辐射';
          item.sortValue = 'microwaveFactors';
        });
        checkResultFormData.push(checkResult.microwaveFactors.formData);
      }
      if (
        checkResult.noiseFactors &&
        checkResult.noiseFactors.formData.length > 0
      ) {
        checkResult.noiseFactors.formData.forEach(item => {
          item.sort = '物理';
          item.sortName = '噪声';
          item.sortValue = 'noiseFactors';
        });
        checkResultFormData.push(checkResult.noiseFactors.formData);
      }
      if (
        checkResult.powerFrequencyElectric &&
        checkResult.powerFrequencyElectric.formData.length > 0
      ) {
        checkResult.powerFrequencyElectric.formData.forEach(item => {
          item.sort = '物理';
          item.sortName = '工频电场';
          item.sortValue = 'powerFrequencyElectric';
        });
        checkResultFormData.push(checkResult.powerFrequencyElectric.formData);
      }
      if (
        checkResult.SiO2Factors &&
        checkResult.SiO2Factors.formData.length > 0
      ) {
        checkResult.SiO2Factors.formData.forEach(item => {
          item.sort = '物理';
          item.sortName = '游离二氧化硅';
          item.sortValue = 'SiO2Factors';
        });
        checkResultFormData.push(checkResult.SiO2Factors.formData);
      }
      if (
        checkResult.ultraHighRadiationFactors &&
        checkResult.ultraHighRadiationFactors.formData.length > 0
      ) {
        checkResult.ultraHighRadiationFactors.formData.forEach(item => {
          item.sort = '物理';
          item.sortName = '超高频辐射';
          item.sortValue = 'heatFactors';
        });
        checkResultFormData.push(
          checkResult.ultraHighRadiationFactors.formData
        );
      }
      if (
        checkResult.ultravioletFactors &&
        checkResult.ultravioletFactors.formData.length > 0
      ) {
        checkResult.ultravioletFactors.formData.forEach(item => {
          item.sort = '物理';
          item.sortName = '紫外辐射';
          item.sortValue = 'heatFactors';
        });
        checkResultFormData.push(checkResult.ultravioletFactors.formData);
      }
      checkResultFormData = _.flatten(checkResultFormData);

      // 是否存在浓度大于50%的
      const over50 = _.filter(checkResultFormData, item => {
        return item.percent > 0.5;
      });
      if (over50.length > 0) {
        assessmentSort = 3;
        return assessmentSort;
      }

      // 苯、1,2-二氯乙烷、三氯乙烯
      const over10jia = _.filter(checkResultFormData, item => {
        if (
          item.checkProject === '苯' ||
          item.checkProject === '1,2-二氯乙烷' ||
          item.checkProject === '三氯乙烯'
        ) {
          return item.percent > 0.1;
        }
      });
      if (over10jia.length > 0) {
        assessmentSort = 3;
        return assessmentSort;
      }

      // 作业场所空气中化学物质或游离二氧化硅≥10%的生产性粉尘浓度超过国家职业接触限值的；

      if (
        checkResult.SiO2Factors &&
        checkResult.SiO2Factors.formData.length > 0
      ) {
        console.info('存在游离二氧化硅粉尘');
        const so2checkList = checkResult.SiO2Factors.formData;
        const so2over = _.filter(so2checkList, item => {
          return item.checkResult > 0.1;
        });
        if (so2over.length > 0) {
          assessmentSort = 3;
          return assessmentSort;
        }
      }

      const cumulativeTwoYearsCount = await ctx.service.db.find(
        'Odisease',
        {
          EnterpriseID: company._id,
          year: { $in: nearTwoYears }, // 去掉年份限制，就统计全部累积人数
        },
        {},
        { count: true }
      );
      if (cumulativeTwoYearsCount && cumulativeTwoYearsCount > 0) {
        assessmentSort = 3;
        return assessmentSort;
      }

      // 是否存在浓度在10%~50%的
      const less50 = _.filter(checkResultFormData, item => {
        return item.percent <= 0.5 && item.percent >= 0.1;
      });
      if (less50.length > 0 && assessmentSort !== 3) {
        assessmentSort = 2;
        return assessmentSort;
      }

      // 苯、1,2-二氯乙烷、三氯乙烯
      const less10jia = _.filter(checkResultFormData, item => {
        if (
          item.checkProject === '苯' ||
          item.checkProject === '1,2-二氯乙烷' ||
          item.checkProject === '三氯乙烯'
        ) {
          return item.percent <= 0.1 && item.percent >= 0;
        }
      });
      // console.log('存在指定危害因素！',over10jia.length)
      if (less10jia.length > 0 && assessmentSort !== 3) {
        assessmentSort = 2;
        return assessmentSort;
      }

      return assessmentSort;
    } catch (error) {
      console.log('企业分类定时任务报错了：', error);
      return 1;
    }
  },

  async riskSortQuzhou(ctx, company, allIndustryCategory, table2) {
    console.log('分类中...');
    try {
      const year = new Date().toISOString();
      const nearTwoYears = [
        year.slice(0, 4),
        JSON.stringify(year.slice(0, 4) - 1),
      ];
      const cumulativeTotalCount = await ctx.service.db.find(
        'Odisease',
        {
          EnterpriseID: company._id,
        },
        {},
        { count: true }
      );

      let assessmentSort = 1; // 分类 ： 1 : 丙类  ， 2 : 乙类  3 : 甲类
      console.log('当前企业分类信息', company.industryCategory);
      if (company.industryCategory && company.industryCategory.length > 0) {
        const curCat =
          company.industryCategory[company.industryCategory.length - 1];
        const curCatObj = _.find(allIndustryCategory, { value: curCat }); // 当前分类
        if ((curCatObj && curCatObj.isJialei) || cumulativeTotalCount > 1) {
          console.log('企业分类中属于甲类');
          assessmentSort = 3;
          return assessmentSort;
        }
      }

      const checkResultFormData = company.adminorgDetail.result;

      const harmNames = _.map(checkResultFormData, 'label');

      const isInTable2 = _.includes(table2, harmNames);

      if (isInTable2) {
        const exceedFactors = _.filter(checkResultFormData, item => {
          return item.exceed > 0;
        });
        exceedFactors.length > 0 ? (assessmentSort = 3) : (assessmentSort = 2);
        console.log('包含表2中有害物质');
        return assessmentSort;
      }

      // 苯、1,2-二氯乙烷、三氯乙烯
      const over10jia = _.filter(checkResultFormData, item => {
        if (
          item.label === '苯' ||
          item.label === '1,2-二氯乙烷' ||
          item.label === '三氯乙烯'
        ) {
          return item.exceed > 0;
        }
      });
      if (over10jia.length > 0) {
        console.log('苯、1,2-二氯乙烷、三氯乙烯');
        assessmentSort = 3;
        return assessmentSort;
      }
      // 苯、1,2-二氯乙烷、三氯乙烯
      const less10yi = _.filter(checkResultFormData, item => {
        if (
          item.label === '苯' ||
          item.label === '1,2-二氯乙烷' ||
          item.label === '三氯乙烯'
        ) {
          return item.exceed === 0 && item.point > 0;
        }
      });
      if (less10yi.length > 0) {
        console.log('苯、1,2-二氯乙烷、三氯乙烯');
        assessmentSort = 2;
        return assessmentSort;
      }

      // 作业场所空气中化学物质或游离二氧化硅≥10%的生产性粉尘浓度超过国家职业接触限值的；

      const SiO2Factors = _.filter(checkResultFormData, item => {
        if (item.label === '矽尘（游离SiO₂含量≥10%）') {
          return item.exceed > 0;
        }
      });
      if (SiO2Factors.length > 0) {
        console.log('矽尘（游离SiO₂含量≥10%）超标');
        assessmentSort = 3;
        return assessmentSort;
      }

      const SiO2FactorsLess = _.filter(checkResultFormData, item => {
        if (item.label === '矽尘（游离SiO₂含量≥10%）') {
          return item.exceed === 0 && item.point > 0;
        }
      });
      if (SiO2FactorsLess.length > 0) {
        console.log('矽尘（游离SiO₂含量≥10%）存在');
        assessmentSort = 2;
        return assessmentSort;
      }

      const cumulativeTwoYearsCount = await ctx.service.db.find(
        'Odisease',
        {
          EnterpriseID: company._id,
          year: { $in: nearTwoYears }, // 去掉年份限制，就统计全部累积人数
        },
        {},
        { count: true }
      );
      if (cumulativeTwoYearsCount && cumulativeTwoYearsCount > 0) {
        console.log('近两年职业病人累计人数');
        assessmentSort = 3;
        return assessmentSort;
      }

      // 电离辐射

      const radioFactors = _.filter(checkResultFormData, item => {
        if (item.label === '电离辐射') {
          return item.exceed > 0;
        }
      });
      if (radioFactors.length > 0) {
        console.log('电离辐射超标');
        assessmentSort = 3;
        return assessmentSort;
      }

      const radioFactorsLess = _.filter(checkResultFormData, item => {
        if (item.label === '电离辐射') {
          return item.exceed === 0 && item.point > 0;
        }
      });
      if (radioFactorsLess.length > 0) {
        console.log('电离辐射包含');
        assessmentSort = 2;
        return assessmentSort;
      }

      return assessmentSort;
    } catch (error) {
      console.log('企业分类定时任务报错了：', error);
      return 1;
    }
  },

  async baoluLevel(ctx, company, allHarmFactors) {
    ctx;
    company;
    allHarmFactors;
    // 严重职业病危害因素包括以下内容：
    // 1. 《高毒物品目录》所列职业病危害因素；
    // 2. 石棉纤维粉尘、游离二氧化硅含量10%以上粉尘；
    // 3. 确认人类致癌物、致敏物；
    // 4. 电离辐射（除外Ⅲ类射线装置、Ⅳ类和Ⅴ类密封源、丙级非密封源工作场所及予以豁免的实践或源）；
    // 5. 卫生健康主管部门规定的其他应列入严重职业病危害因素范围的。

    // 先判定一般危害因素还是严重危害因素
    // 一般职业病危害因素和严重职业病危害因素同时存在时，算严重危害因素
    // 然后判定接触水平，超标和不超标
    // 再根据接触人数，判定暴露风险等级
    //                  <=9 ； 10-49 ； >=50;
    // 一般  不超标 ： 低风险  低风险    低风险
    // 一般  超  标 ： 低风险  中风险    高风险
    // 严重  不超标 ： 中风险  中风险    高风险
    // 严重  超  标 ： 中风险  高风险    高风险

    return 0;
  },

  async baoluLevelQuzhou(
    ctx,
    company,
    allHarmFactors,
    highToxicFactors,
    cancerAllergy
  ) {
    // 先判定一般危害因素还是严重危害因素
    // 一般职业病危害因素和严重职业病危害因素同时存在时，算严重危害因素
    // 然后判定接触水平，超标和不超标
    // 再根据接触人数，判定暴露风险等级
    //                  <=9 ； 10-49 ； >=50;
    // 一般  不超标 ： 低风险  低风险    低风险
    // 一般  超  标 ： 低风险  中风险    高风险
    // 严重  不超标 ： 中风险  中风险    高风险
    // 严重  超  标 ： 中风险  高风险    高风险

    console.log('衢州企业暴露风险判定中...');

    const checkResultFormData = company.adminorgDetail.result;
    const harmStatisticsTotal = company.adminorgDetail.harmStatisticsTotal;
    const harmNames = _.map(checkResultFormData, 'label');
    let exposeResult = 0;
    console.log(
      '当前企业包含的危害因素 %s ，接害总人数 %s 。',
      harmNames,
      harmStatisticsTotal
    );
    let isSerious = false;
    let isOver = false;
    const seriousHarmFactors = _.filter(checkResultFormData, item => {
      let curFactor = _.find(allHarmFactors, { label: item.label });
      curFactor = curFactor && curFactor.label ? curFactor : item;
      isSerious = ctx.helper.isSeriousFactor(
        allHarmFactors,
        highToxicFactors,
        cancerAllergy,
        curFactor.label
      );
      return isSerious;
    });
    if (seriousHarmFactors.length > 0) {
      isSerious = true;
    }
    const overExceedFactors = _.filter(checkResultFormData, item => {
      const curFactor = _.find(allHarmFactors, { label: item.label });
      if (curFactor && curFactor.exceed) return;
    });
    if (overExceedFactors.length > 0) {
      isOver = true;
    }

    console.log('严重职业病危害因素：%s ，是否有超标： %s', isSerious, isOver);

    if (harmStatisticsTotal <= 9) {
      if (!isSerious && !isOver) exposeResult = 0;
      if (!isSerious && isOver) exposeResult = 0;
      if (isSerious && !isOver) exposeResult = 1;
      if (isSerious && isOver) exposeResult = 1;
    } else if (harmStatisticsTotal > 9 && harmStatisticsTotal < 49) {
      if (!isSerious && !isOver) exposeResult = 0;
      if (!isSerious && isOver) exposeResult = 1;
      if (isSerious && !isOver) exposeResult = 1;
      if (isSerious && isOver) exposeResult = 2;
    } else if (harmStatisticsTotal >= 50) {
      if (!isSerious && !isOver) exposeResult = 0;
      if (!isSerious && isOver) exposeResult = 2;
      if (isSerious && !isOver) exposeResult = 2;
      if (isSerious && isOver) exposeResult = 2;
    }

    return exposeResult;
  },

  isSeriousFactor(allHarmfactors, highToxicFactors, cancerAllergy, harmfactor) {
    allHarmfactors;
    // 严重职业病危害因素包括以下内容：
    // 1. 《高毒物品目录》所列职业病危害因素；
    // 2. 石棉纤维粉尘、游离二氧化硅含量10%以上粉尘；
    // 3. 确认人类致癌物、致敏物；
    // 4. 电离辐射（除外Ⅲ类射线装置、Ⅳ类和Ⅴ类密封源、丙级非密封源工作场所及予以豁免的实践或源）；
    // 5. 卫生健康主管部门规定的其他应列入严重职业病危害因素范围的。

    const highToxicFactorsNames = highToxicFactors.map(item => {
      return item.name;
    });
    const cancerAllergyNames = cancerAllergy.map(item => {
      return item.chineseName;
    });

    const isHighToxic = _.includes(highToxicFactorsNames, harmfactor);
    const isCancerAllergy = _.includes(cancerAllergyNames, harmfactor);

    if (
      isHighToxic ||
      isCancerAllergy ||
      harmfactor === '石棉纤维粉尘' ||
      harmfactor === '石棉粉尘' ||
      harmfactor === '矽尘（游离SiO2含量≥10%）' ||
      harmfactor === '矽尘' ||
      harmfactor === '电离辐射'
    ) {
      // console.log( '判定当前危害因素 < %s > 是否严重 : 是 ', harmfactor );
      return true;
    }
    // console.log( '判定当前危害因素 < %s > 是否严重 : 否 ', harmfactor );
    return false;
  },

  async reqJsonData(url, params = {}, method = 'get') {
    let responseData;

    let targetUrl = '';
    if (url.indexOf('manage/') === 0) {
      targetUrl = this.app.config.server_path + '/' + url;
    } else if (url.indexOf('http') === 0) {
      targetUrl = url;
    } else {
      targetUrl = this.app.config.server_api + '/' + url;
    }

    if (method === 'get') {
      responseData = await Axios.get(targetUrl, {
        params,
      });
    } else if (method === 'post') {
      responseData = await Axios.post(targetUrl, params);
    }
    if (
      responseData &&
      responseData.status === 200 &&
      !_.isEmpty(responseData.data) &&
      responseData.data.status === 200
    ) {
      return responseData.data.data;
    }

    // throw new Error(responseData.data.message);
    // 便于单元测试，改为如下，结果是一样的
    return { status: 500, message: responseData.data.message, data: {} };
  },
  clearCache(str, cacheKey) {
    console.log('cacheStr', str);
    const currentKey = this.app.config.session_secret + cacheKey + str;
    this.setCache(currentKey, '', 2000);
  },
  setCache(key, value, time) {
    // 多进程消息同步
    console.log('=========存储验证码');
    this.app.messenger.sendToApp('refreshCache', {
      key,
      value,
      time,
    });
    console.log(this.app.messenger);
  },
  getCache(key) {
    console.log('getchache 函数里');
    // console.log(this.app);
    console.log('-=------=-=-=-=-=-=-=-=-=-=-=');
    // console.log(this.app.cache);
    return this.app.cache.get(key);
  },
  async getScopeData(type, userid = '') {
    const scopeData = [
      'userid',
      'enterprise_ids',
      'dingtree_ids',
      'powerIds',
      'superAdmin',
      'dingtree_ids_all',
      'millConstruction_ids',
    ];
    if (scopeData.indexOf(type) === -1) {
      throw new Error(`暂无${type}类型的数据范围`);
    }
    try {
      const userId = userid || this.ctx.session.adminUserInfo._id;
      const res = await this.getRedis(`permission:${userId}:${type}`);
      return res;
    } catch (error) {
      return null;
    }
  },
  async getPoliceCache(key) {
    const res = await this.app.cache.get(key);
    return res;
  },
  async setPolicyCache(key, value, ttl = 3600) {
    if (!key || !value) {
      throw new Error('key 或 value 不能为空');
    }
    await this.setRedis(key, value, ttl);
  },
  async clearPolicyCache(key) {
    return await this.app.redis.del(key);
  },
  renderSuccess(ctx, { data = {}, message = '' } = {}) {
    ctx.body = {
      status: 200,
      data,
      message,
    };
    ctx.status = 200;
  },
  renderCustom(ctx, { status = 200, data = {}, message = '' } = {}) {
    ctx.body = {
      data,
      message,
      status,
    };
    ctx.status = status;
  },
  renderFail(ctx, { message = '哎呀！网页开小差了！', data = {} } = {}) {
    if (message instanceof Object) {
      console.log(message);
      message = message.message;
    }
    ctx.body = {
      status: 500,
      message,
      data,
    };
    ctx.status = 200;
  },
  renderAutoResponse(ctx, status = 200, { data = {}, message = '' } = {}) {
    ctx.body = {
      status,
      data,
      message,
    };
    ctx.status = 200;
  },

  async getAdminPower(ctx, isAll = false) {
    const adminUserId = ctx.session.adminUserInfo._id;
    if (!adminUserId) return [];
    let adminUser;
    try {
      adminUser = await ctx.model.AdminUser.aggregate([
        {
          $match: {
            _id: adminUserId,
          },
        },
        {
          $lookup: {
            from: 'employees',
            localField: 'userId',
            foreignField: 'userId',
            as: 'employee',
          },
        },
        {
          $lookup: {
            from: 'adminorgs',
            let: { adminUserId: '$_id' }, // 将 _id 传递到 lookup 的 pipeline
            pipeline: [
              {
                $match: {
                  $expr: {
                    // $in: ['$$adminUserId', '$adminArray'], // 匹配 adminUserId 是否在 adminArray 数组中
                    $or: [
                      // 匹配 _id 在 adminArray 数组中
                      {
                        $in: [
                          '$$adminUserId',
                          { $ifNull: [ '$adminArray', []] },
                        ],
                      },
                      // 或者直接等于 adminUserId 字段
                      { $eq: [ '$$adminUserId', '$adminUserId' ] },
                    ],
                  },
                },
              },
            ],
            as: 'adminorg',
          },
        },
        {
          $project: {
            _id: 0,
            'employee._id': 1,
            'adminorg._id': 1,
          },
        },
        {
          $unwind: {
            path: '$employee',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: '$adminorg',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $project: {
            employeeId: '$employee._id',
            adminorgId: '$adminorg._id',
          },
        },
      ]).allowDiskUse(true);
    } catch (err) {
      ctx.auditLog(
        `系统错误（${adminUserId}鉴权）`,
        JSON.stringify(err),
        'error'
      );
    }

    let isAdminPower = true,
      adminPower = [];
    if (adminUser && adminUser.length > 0 && !adminUser[0].adminorgId) {
      const rolePower = await ctx.model.Roles.aggregate([
        { $unwind: '$formData' },
        {
          $match: {
            'formData.userId': {
              $elemMatch: { $elemMatch: { $eq: adminUser[0].employeeId } },
            },
          },
        },
        { $unwind: '$formData.power' },
        { $group: { _id: null, power: { $addToSet: '$formData.power' } } },
        { $project: { _id: 0 } },
      ]);
      if (rolePower && rolePower.length > 0) {
        isAdminPower = false;
        adminPower = rolePower[0].power || [];
      }
    }

    if (isAdminPower || isAll) {
      const options = { authCheck: false };
      const adminUserInfo = await ctx.service.adminUser.item(
        ctx,
        {
          query: {
            _id: adminUserId,
          },
          populate: [
            {
              path: 'group',
              select: 'power _id enable name',
            },
          ],
          files: 'group',
        },
        options
      );
      adminPower = adminUserInfo.group.power || [];
    }
    return adminPower;
  },

  async getPolicyAdminPower(ctx) {
    const adminUserId = ctx.session.adminUserInfo._id;
    const EnterpriseID = ctx.session.adminUserInfo.EnterpriseID;
    const topEnterpriseID = ctx.session.adminUserInfo.topEnterpriseID;
    if (!adminUserId) return [];

    const adminPower = await ctx.model.Policy.aggregate([
      {
        $match: {
          topEnterpriseID,
          user_ids: {
            $elemMatch: {
              $eq: adminUserId,
            },
          },
        },
      },
      {
        $unwind: '$group_ids',
      },
      {
        $lookup: {
          from: 'qygroups', // 角色表
          localField: 'group_ids',
          foreignField: '_id',
          as: 'groupInfo',
        },
      },
      {
        $unwind: '$groupInfo',
      },
      {
        $addFields: {
          power: { $ifNull: [ '$groupInfo.power', []] }, // 如果 power 字段为 null，则设置为一个空数组
        },
      },
      {
        $addFields: {
          power: {
            $cond: {
              if: { $isArray: '$power' },
              then: '$power',
              else: [],
            },
          },
        },
      },
      {
        $group: {
          _id: null,
          powerIds: { $addToSet: '$power' },
          enterpriseScopeType: { $push: '$enterpriseScopeType' },
          dingtreeScopeType: { $push: '$dingtreeScopeType' },
          millScopeType: { $push: '$millScopeType' },
          enterprise_ids: {
            $addToSet: '$enterprise_ids',
          },
          dingtree_ids: {
            $addToSet: '$dingtree_ids',
          },
          millConstruction_ids: {
            $addToSet: '$millConstruction_ids',
          },
        },
      },
      {
        $project: {
          _id: 0,
          enterpriseScopeType: 1,
          dingtreeScopeType: 1,
          millScopeType: 1,
          powerIds: {
            $reduce: {
              input: '$powerIds',
              initialValue: [],
              in: { $setUnion: [ '$$value', '$$this' ] },
            },
          },
          enterprise_ids: {
            $reduce: {
              input: '$enterprise_ids',
              initialValue: [],
              in: { $setUnion: [ '$$value', '$$this' ] },
            },
          },
          dingtree_ids: {
            $reduce: {
              input: '$dingtree_ids',
              initialValue: [],
              in: { $setUnion: [ '$$value', '$$this' ] },
            },
          },
          millConstruction_ids: {
            $reduce: {
              input: '$millConstruction_ids',
              initialValue: [],
              in: { $setUnion: [ '$$value', '$$this' ] },
            },
          },
        },
      },
    ]);
    const res = adminPower[0];
    if (!res) return [];
    const {
      enterpriseScopeType,
      enterprise_ids,
      dingtreeScopeType,
      dingtree_ids,
      millConstruction_ids = [],
      millScopeType,
    } = res;
    if (
      enterpriseScopeType &&
      enterpriseScopeType.length > 0 &&
      enterpriseScopeType.some(element => element !== null)
    ) {
      if (enterpriseScopeType.includes(1)) {
        const nextEnterpriseIds =
          await ctx.service.policyManage.getEnterpriseId(1);
        res.enterprise_ids = nextEnterpriseIds;
      }
      if (!enterpriseScopeType.includes(1) && enterpriseScopeType.includes(2)) {
        const nextEnterpriseIds =
          await ctx.service.policyManage.getEnterpriseId(2);
        res.enterprise_ids = [
          ...new Set([ ...enterprise_ids, ...nextEnterpriseIds ]),
        ];
      }
    } else if (enterprise_ids && enterprise_ids.length > 0) {
      res.enterprise_ids = [ ...new Set([ ...enterprise_ids, EnterpriseID ]) ];
    } else {
      res.enterprise_ids = [ EnterpriseID ];
    }
    if (
      dingtreeScopeType &&
      dingtreeScopeType.length > 0 &&
      dingtreeScopeType.some(element => element !== null)
    ) {
      if (dingtreeScopeType.includes(1)) {
        // const enterprise_ids_arr =
        //   await ctx.service.policyManage.getEnterpriseId(1);
        // const dingtree_ids_arr = await this.getDingtreeScopeIds(
        //   enterprise_ids_arr
        // );
        const dingtree_ids_arr = await this.getTopEnterpriseChildIds();
        res.dingtree_ids = dingtree_ids_arr;
      }
      if (!dingtreeScopeType.includes(1) && dingtreeScopeType.includes(2)) {
        const nextDingtreeIds = await this.getDingtreeScopeTwo();
        const all_dingtree_ids = [
          ...new Set([ ...dingtree_ids, ...nextDingtreeIds ]),
        ];
        const fill_dingtrees_ids = await ctx.service.policyManage.getDingtreeId(
          all_dingtree_ids
        );
        res.dingtree_ids = fill_dingtrees_ids;
      }
    } else if (dingtree_ids && res.dingtree_ids.length > 0) {
      const dingtree_id = await this.getDingtreeSelf();
      const all_dingtree_ids = [ ...new Set([ ...dingtree_ids, ...dingtree_id ]) ];
      const fill_dingtrees_ids = await ctx.service.policyManage.getDingtreeId(
        all_dingtree_ids
      );
      res.dingtree_ids = fill_dingtrees_ids;
    } else {
      const dingtree_id = await this.getDingtreeSelf();
      res.dingtree_ids = dingtree_id;
    }
    if (
      millScopeType &&
      millScopeType.length > 0 &&
      millScopeType.some(element => element !== null)
    ) {
      if (millScopeType.includes(1)) {
        const millConstruction_id = await this.getAllMillConstruction();
        res.millConstruction_ids = millConstruction_id;
      }
      if (!millScopeType.includes(1) && millScopeType.includes(2)) {
        const millConstruction_id =
          await ctx.service.policyManage.getMillConstructionId([ EnterpriseID ]);
        res.millConstruction_ids = millConstruction_id;
      }
    } else if (millConstruction_ids && millConstruction_ids.length > 0) {
      res.millConstruction_ids = millConstruction_ids;
    } else {
      const millConstruction_id = await this.getMillConstructionSelf();
      res.millConstruction_ids = millConstruction_id;
    }
    return res;
  },

  /**
   * @description 根据部门权限获取可操作人员id
   * @param {*} cacheDingtreeIDs
   * @return {Array}
   */
  async getDingtreeIDs(cacheDingtreeIDs) {
    const { ctx } = this;
    if (cacheDingtreeIDs && cacheDingtreeIDs.length > 0) {
      const employeeIdArr = await ctx.model.Dingtree.aggregate([
        {
          $match: {
            _id: { $in: cacheDingtreeIDs },
          },
        },
        {
          $graphLookup: {
            from: 'dingtrees',
            startWith: '$_id',
            connectFromField: '_id',
            connectToField: 'parentid',
            as: 'descendants',
            depthField: 'depth',
          },
        },
        {
          $project: {
            allStaff: {
              $concatArrays: [
                '$staff',
                {
                  $reduce: {
                    input: '$descendants',
                    initialValue: [],
                    in: {
                      $concatArrays: [ '$$value', '$$this.staff' ],
                    },
                  },
                },
              ],
            },
          },
        },
        {
          $unwind: '$allStaff',
        },
        {
          $lookup: {
            from: 'employees',
            localField: 'allStaff',
            foreignField: '_id',
            as: 'employeeDetails',
          },
        },
        {
          $unwind: '$employeeDetails',
        },
        {
          $match: {
            'employeeDetails.status': 1,
          },
        },
        {
          $group: {
            _id: null,
            allStaff: {
              $addToSet: '$employeeDetails._id',
            },
          },
        },
        {
          $project: {
            _id: 0,
            allStaff: 1,
          },
        },
      ]);
      return employeeIdArr[0].allStaff;
    }
    return [];
  },

  /**
   * @description 获取当前用户部门权限id数组下所有可操作部门id
   * @param {*} cacheEnterpriseIDs
   * @return {Array}
   */
  async getDingtreeIdsArr(cacheDingtreeIDs) {
    const { ctx } = this;
    const res = await ctx.model.Dingtree.aggregate([
      {
        $match: {
          _id: {
            $in: cacheDingtreeIDs,
          },
        },
      },
      {
        $graphLookup: {
          from: 'dingtrees',
          startWith: '$_id',
          connectFromField: '_id',
          connectToField: 'parentid',
          as: 'descendants',
          depthField: 'depth',
        },
      },
      {
        $addFields: {
          allIds: {
            $concatArrays: [[{ _id: '$_id' }], '$descendants' ],
          },
        },
      },
      {
        $project: {
          allIds: {
            $map: {
              input: '$allIds',
              as: 'doc',
              in: '$$doc._id',
            },
          },
          _id: 0,
        },
      },
      {
        $unwind: '$allIds',
      },
      {
        $group: {
          _id: null,
          allIds: { $addToSet: '$allIds' },
        },
      },
      {
        $project: {
          _id: 0,
          allIds: 1,
        },
      },
    ]).option({ authCheck: false });
    return res[0].allIds;
  },

  /**
   * @description 获取当前用户是否是超级管理员
   * @param {userid} params
   * @return {Boolean}
   */
  async getIsSuperAdmin(userid) {
    if (!userid) return false;
    const { ctx } = this;
    const adminUsers = await ctx.model.Policy.find({
      user_ids: {
        $elemMatch: {
          $eq: userid,
        },
      },
    });

    const isSuperAdmin = adminUsers.some(user => user.isSuper);

    if (isSuperAdmin) {
      return '1';
    }
    return '0';
  },

  /**
   * @description 获取所有企业id
   * @return  {Array}
   */
  async getSuperAdminEnterpriseIds() {
    const { ctx } = this;
    try {
      const companies = await ctx.model.Adminorg.find();
      const companyIds = companies.map(company => company._id);
      return companyIds;
    } catch (error) {
      ctx.auditLog('获取子公司失败', `${error}`, 'error');
      return []; // 在出错时返回空数组
    }
  },

  /**
   * @description 获取最顶层企业id
   * @return  {String}
   */
  async getTopEnterpriseId(enterpriseId = '') {
    const { ctx } = this;
    const EnterpriseID = enterpriseId || ctx.session.adminUserInfo.EnterpriseID;
    try {
      const topCompany = await ctx.model.Adminorg.aggregate([
        {
          $match: {
            _id: EnterpriseID,
          },
        },
        {
          $graphLookup: {
            from: 'adminorgs',
            startWith: '$parentId',
            connectFromField: 'parentId',
            connectToField: '_id',
            as: 'parentHierarchy',
            depthField: 'depth',
          },
        },
        {
          $unwind: '$parentHierarchy',
        },
        {
          $sort: {
            'parentHierarchy.depth': -1,
          },
        },
        {
          $group: {
            _id: '$_id',
            maxDepthParent: {
              $first: '$parentHierarchy',
            },
          },
        },
        {
          $addFields: {
            'maxDepthParent.label': {
              $cond: {
                if: {
                  $gt: [
                    {
                      $ifNull: [ '$maxDepthParent.shortName', null ],
                    },
                    null,
                  ],
                },
                then: '$maxDepthParent.shortName',
                else: '$maxDepthParent.cname',
              },
            },
            'maxDepthParent.children': {
              $cond: {
                if: {
                  $gt: [
                    {
                      $size: '$maxDepthParent.childrenId',
                    },
                    0,
                  ],
                },
                then: null,
                else: '$$REMOVE',
              },
            },
          },
        },
        {
          $project: {
            _id: 0,
            maxDepthParent: {
              id: '$maxDepthParent._id',
              label: '$maxDepthParent.label',
              children: '$maxDepthParent.children',
            },
          },
        },
        {
          $replaceRoot: {
            newRoot: '$maxDepthParent',
          },
        },
      ]);
      if (topCompany.length === 0) {
        const companyInfo = await ctx.model.Adminorg.findOne(
          { _id: EnterpriseID },
          { _id: 1 }
        ).lean();

        if (companyInfo) {
          return companyInfo._id;
        }
        return '';
      }
      return topCompany[0].id;
    } catch (error) {
      ctx.auditLog('获取公司失败', `${error}`, 'error');
      return []; // 在出错时返回空数组
    }
  },

  // 获取作为顶级部门的下一级部门id
  async getTopEnterpriseChildIds() {
    const { ctx } = this;
    const EnterpriseID = await this.getTopEnterpriseId();
    if (!EnterpriseID) {
      return [];
    }
    const topDingtreeInfo = await ctx.service.policyManage.getScopeDingtreeList(
      EnterpriseID
    );
    const topDingtreeId = topDingtreeInfo[0].id;
    const topDingtreeChildIds = await ctx.model.Dingtree.distinct('_id', {
      parentid: topDingtreeId,
    });
    if (topDingtreeChildIds.length === 0) {
      return [ topDingtreeId ];
    }
    const allDingtreeIds = topDingtreeChildIds.concat(topDingtreeId);
    return allDingtreeIds;
  },

  // 获取当前用户部门权限为1的所有可操作的部门id
  async getDingtreeScopeIds(enterprise_ids) {
    const res = await this.ctx.model.Dingtree.distinct('_id', {
      EnterpriseID: { $in: enterprise_ids },
    });
    return res;
  },

  // 获取当前用户部门权限为2的所有可操作的部门id
  async getDingtreeScopeTwo() {
    const { ctx } = this;
    const userid = ctx.session.adminUserInfo._id;
    const res = await ctx.model.AdminUser.aggregate([
      {
        $match: {
          _id: userid,
        },
      },
      {
        $unwind: '$employees',
      },
      {
        $lookup: {
          from: 'employees',
          localField: 'employees',
          foreignField: '_id',
          as: 'employeeDetails',
        },
      },
      {
        $project: {
          employeeDetails: 1,
        },
      },
      {
        $unwind: '$employeeDetails',
      },
      {
        $replaceRoot: {
          newRoot: '$employeeDetails',
        },
      },
      {
        $unwind: '$departs',
      },
      {
        $lookup: {
          from: 'dingtrees',
          localField: 'departs',
          foreignField: '_id',
          as: 'dingtreInfo',
        },
      },
      {
        $unwind: '$dingtreInfo',
      },
      {
        $replaceRoot: {
          newRoot: '$dingtreInfo',
        },
      },
      {
        $graphLookup: {
          from: 'dingtrees',
          startWith: '$_id',
          connectFromField: '_id',
          connectToField: 'parentid',
          as: 'dingtreesIds',
          depthField: 'depth',
        },
      },
      {
        $addFields: {
          allDingtreeIds: {
            $concatArrays: [[{ _id: '$_id' }], '$dingtreesIds' ],
          },
        },
      },
      {
        $addFields: {
          allDingtreeIds: {
            $map: {
              input: '$allDingtreeIds',
              as: 'dingtree',
              in: '$$dingtree._id',
            },
          },
        },
      },
      {
        $project: {
          allDingtreeIds: 1,
          _id: 0,
        },
      },
    ]);
    const dingtreeIds = res[0].allDingtreeIds;
    return dingtreeIds;
  },

  // 获取当前用户自己的部门id
  async getDingtreeSelf() {
    const { ctx } = this;
    const userid = ctx.session.adminUserInfo._id;
    const pipeline = [
      {
        $match: {
          _id: userid,
        },
      },
      {
        $unwind: '$employees',
      },
      {
        $lookup: {
          from: 'employees',
          localField: 'employees',
          foreignField: '_id',
          as: 'employeeDetails',
        },
      },
      {
        $project: {
          employeeDetails: 1,
        },
      },
      {
        $unwind: '$employeeDetails',
      },
      {
        $replaceRoot: {
          newRoot: '$employeeDetails',
        },
      },
      {
        $project: {
          departs: 1,
          _id: 0,
        },
      },
    ];
    const res = await ctx.service.db.aggregate('AdminUser', pipeline, {
      authCheck: false,
    });
    return res[0].departs;
  },

  // 获取当前用户自己的工作场所id
  async getMillConstructionSelf() {
    const { ctx } = this;
    const userid = ctx.session.adminUserInfo._id;
    const adminUser = await ctx.service.db.findOne(
      'AdminUser',
      { _id: userid },
      {},
      { authCheck: false }
    );
    if (!adminUser || !adminUser.employees.length) return [];
    const employeeId = adminUser.employees[0];
    // 查询所在工作场所一级id
    const result = await ctx.service.db.findOne(
      'MillConstruction',
      {
        $or: [
          { 'children.employees': employeeId }, // 检查第二层的 employees
          { 'children.children.employees': employeeId }, // 检查第三层的 employees
        ],
      },
      { _id: 1 }, // 只返回 _id
      { authCheck: false }
    );
    if (!result) return [];
    return [ result._id ];
  },

  // 获取所有工作场所id
  async getAllMillConstruction() {
    const { ctx } = this;
    const res = await ctx.model.MillConstruction.distinct('_id');
    return res;
  },

  // 获取当前用户可操作资源
  async getUserResourceList(user_id = '') {
    const { ctx } = this;
    const userid = user_id || ctx.session.adminUserInfo._id;
    await this.setPolicyCache(`permission:${userid}:userid`, userid);
    // await this.getTopEnterpriseChildIds();
    const isSuper = await this.getIsSuperAdmin(userid);
    await this.setPolicyCache(`permission:${userid}:superAdmin`, isSuper);
    if (isSuper === '1') {
      const adminPower = await this.getAdminPower(ctx);
      const enterprise_ids = await ctx.service.policyManage.getEnterpriseId(1);
      // const dingtree_ids = await this.getDingtreeScopeIds(enterprise_ids);
      const dingtree_ids = await this.getTopEnterpriseChildIds();
      const dingtree_ids_all = await this.getDingtreeIdsArr(dingtree_ids);
      const millConstruction_ids = await this.getAllMillConstruction();

      await this.setPolicyCache(
        `permission:${userid}:dingtree_ids_all`,
        dingtree_ids_all
      );
      // const employee_ids_arr = await this.getDingtreeIDs(dingtree_ids);
      // this.setPolicyCache(`employee_ids_arr:${userid}`, employee_ids_arr);
      await this.setPolicyCache(
        `permission:${userid}:enterprise_ids`,
        enterprise_ids
      );
      await this.setPolicyCache(
        `permission:${userid}:dingtree_ids`,
        dingtree_ids
      );
      await this.setPolicyCache(
        `permission:${userid}:millConstruction_ids`,
        millConstruction_ids
      );
      await this.setPolicyCache(`permission:${userid}:powerIds`, adminPower);
      return adminPower;
    }
    const adminPower = await this.getPolicyAdminPower(ctx);
    const { powerIds, enterprise_ids, dingtree_ids, millConstruction_ids } =
      adminPower;
    if (!powerIds || powerIds.length === 0) return [];
    if (dingtree_ids && dingtree_ids.length > 0) {
      // const employee_ids_arr = await this.getDingtreeIDs(dingtree_ids);
      // this.setPolicyCache(`employee_ids_arr:${userid}`, employee_ids_arr);
      const dingtree_ids_all = await this.getDingtreeIdsArr(dingtree_ids);
      await this.setPolicyCache(
        `permission:${userid}:dingtree_ids_all`,
        dingtree_ids_all
      );
    }
    await this.setPolicyCache(`permission:${userid}:powerIds`, powerIds);
    await this.setPolicyCache(
      `permission:${userid}:enterprise_ids`,
      enterprise_ids
    );
    await this.setPolicyCache(
      `permission:${userid}:dingtree_ids`,
      dingtree_ids
    );
    await this.setPolicyCache(
      `permission:${userid}:millConstruction_ids`,
      millConstruction_ids
    );
    return powerIds;
  },

  // 清楚用户权限缓存
  async clearUserPolicyCache(user_id = '') {
    const { ctx } = this;
    const userid = user_id || ctx.session.adminUserInfo._id;
    if (!userid) return;
    await this.clearPolicyCache(`permission:${userid}:userid`);
    await this.clearPolicyCache(`permission:${userid}:superAdmin`);
    await this.clearPolicyCache(`permission:${userid}:enterprise_ids`);
    await this.clearPolicyCache(`permission:${userid}:dingtree_ids`);
    await this.clearPolicyCache(`permission:${userid}:millConstruction_ids`);
    await this.clearPolicyCache(`permission:${userid}:powerIds`);
    await this.clearPolicyCache(`permission:${userid}:dingtree_ids_all`);
  },

  deleteFolder(path) {
    // console.log("---del path--" + path);
    return new Promise(resolve => {
      let files = [];
      if (fs.existsSync(path)) {
        // console.log("---begin to del--");
        if (fs.statSync(path).isDirectory()) {
          const walk = function(path) {
            files = fs.readdirSync(path);
            files.forEach(function(file) {
              const curPath = path + '/' + file;
              if (fs.statSync(curPath).isDirectory()) {
                // recurse
                walk(curPath);
              } else {
                // delete file
                fs.unlinkSync(curPath);
              }
            });
            fs.rmdirSync(path);
          };
          walk(path);
          // console.log("---del folder success----");
          resolve();
        } else {
          fs.unlink(path, function(err) {
            if (err) {
              console.log(err);
            } else {
              console.log('del file success');
              resolve();
            }
          });
        }
      } else {
        resolve();
      }
    });
  },

  hashSha256(data, salt) {
    return CryptoJS.SHA256(data + salt).toString();
  },

  encrypt(data, key) {
    // 密码加密
    return CryptoJS.AES.encrypt(data, key).toString();
  },

  decrypt(data, key) {
    // 密码解密
    try {
      const bytes = CryptoJS.AES.decrypt(data, key);
      return bytes.toString(CryptoJS.enc.Utf8);
    } catch (error) {
      console.log('密码解密失败：' + error);
      return '';
    }
  },
  // APP加密
  encryptApp(key, iv, data) {
    console.log(key);
    const cipher = crypto.createCipheriv('aes-128-cbc', key, iv);
    let cryped = cipher.update(data, 'utf8', 'binary');
    cryped += cipher.final('binary');
    cryped = new Buffer(cryped, 'binary').toString('base64');
    return cryped;
  },

  decryptApp(key, iv, crypted) {
    try {
      crypted = new Buffer(crypted, 'base64').toString('binary');
      const decipher = crypto.createDecipheriv('aes-128-cbc', key, iv);
      let decoded = decipher.update(crypted, 'binary', 'utf8');
      decoded += decipher.final('utf8');
      return decoded;
    } catch (error) {
      console.log('check token failed!');
      return '';
    }
  },

  // 填充word模板文件通用方法
  async fillWord(ctx, templateFileName, wordData, fileNameSuffix = '') {
    // fileNameSuffix文件名的后缀
    const temPath = ctx.app.config.report_template_path;
    await mkdirp(temPath);
    const content = fs.readFileSync(
      path.resolve(temPath, templateFileName + '.docx'),
      'binary'
    );
    const zip = new PizZip(content);
    const doc = new Docxtemplater();
    const opts = {};
    const EnterpriseID = ctx.session.adminUserInfo
      ? ctx.session.adminUserInfo.EnterpriseID
      : '';
    const configFilePath = path.resolve(
      path.join(ctx.app.config.enterprise_path, `/${EnterpriseID}`)
    );
    opts.centered = false;
    opts.getImage = tagValue => {
      return fs.readFileSync(path.resolve(tagValue));
    };
    opts.getSize = function() {
      return [ 140, 70 ];
    };
    doc.attachModule(new ImageModule(opts));
    doc.setOptions({ parser: angularParser, linebreaks: true });
    doc.loadZip(zip);
    doc.setData(wordData);
    let res = {};
    try {
      if (fileNameSuffix !== '') {
        // 判断文档是否存在
        const checkDir = fs.existsSync(
          path.resolve(
            configFilePath,
            templateFileName + fileNameSuffix + '.docx'
          )
        );
        if (checkDir) {
          res = {
            path:
              '/static' +
              ctx.app.config.enterprise_http_path +
              '/' +
              EnterpriseID +
              '/' +
              templateFileName +
              fileNameSuffix +
              '.docx',
          };
          return res; // 返回路径
        }
      }
      await doc.render();
      const fileName =
        templateFileName + (fileNameSuffix || new Date().getTime()); // doc文件名
      const buf = doc
        .getZip()
        .generate({ type: 'nodebuffer', compression: 'DEFLATE' });

      let urlPath =
        '/static' +
        ctx.app.config.enterprise_http_path +
        '/' +
        EnterpriseID +
        '/' +
        fileName +
        '.docx';
      if (ctx.app.config.storageType === 'oss') {
        // 上传到oss
        await ctx.helper.pipe({
          readableStream: Readable.from(buf),
          target: path.resolve(configFilePath, fileName + '.docx'),
        });
        // console.log('fileUploadRes', res);
        urlPath = await ctx.helper.concatenatePath({
          path:
            ctx.app.config.enterprise_http_path +
            '/' +
            EnterpriseID +
            '/' +
            fileName +
            '.docx',
        });
      } else {
        await mkdirp(configFilePath);
        await fs.writeFileSync(
          path.resolve(configFilePath, fileName + '.docx'),
          buf
        );
      }
      res = {
        originName: fileName,
        staticName: fileName + '.docx',
        path: urlPath,
        createEnterpriseID: EnterpriseID,
      };
    } catch (error) {
      const e = {
        message: error.message,
        name: error.name,
        stack: error.stack,
        properties: error.properties,
      };
      console.log(JSON.stringify(e));

      res = {
        code: 500,
        e,
        message: '模板错误或关闭正在操作的word文件',
      };
    }
    return res;
  },

  // 阿里云视频SDK封装
  async request_alivod(action, param) {
    const aliclient = initVodClient(
      this.app.config.aliVideo.accessKeyId,
      this.app.config.aliVideo.secretAccessKey
    );
    return await aliclient
      .request(action, param, {})
      .then(response => {
        return response;
      })
      .catch(response => {
        return response;
      });
  },

  /**
   * @description: 初始化client
   * @return {*}
   */
  async initCilent() {
    const clientOption = this.app.config.oss; // 获取oss配置

    // 实例化client

    const endPoint = clientOption.endPoint;
    const parsedUrl = url.parse(endPoint);

    const protocol = parsedUrl.protocol;
    const hostname = parsedUrl.hostname;
    const port = parsedUrl.port;

    if (!protocol || !hostname) {
      throw Error(`请检查url是否正确：${endPoint}`);
    }
    // minio
    const client = new Minio.Client({
      endPoint: hostname,
      region: clientOption.region,
      accessKey: clientOption.accessKeyId,
      secretKey: clientOption.accessKeySecret,
      port: port ? parseInt(port) : '',
      useSSL: protocol.includes('https'),
    });
    return client;
  },

  /**
   * @description: stream写入
   * @param {*} readableStream 可读流 必填
   * @param {*} target 写入路径 必填 例
   * @param {*} bucketObj bucket配置对象 非必填 app.oss.buckets.xxx ，默认app.oss.buckets.default
   * @return {*}
   */
  async pipe({ readableStream, target, bucketObj }) {
    // console.log('target', target);
    const { app } = this;
    // 获取文件存储类型
    const storageType = app.config.storageType;
    const option = app.config.oss;

    // 1.存储时 target 正则提取一下企业ID 换成最顶层的   async getTopEnterpriseId()
    let id = null;
    const regex = /([\\/](?:enterprise|tjReport)[\\/])([^\\\/]+)/;
    const match = regex.exec(target);
    if (match) {
      id = match[2];
    }
    if (!id) {
      return {
        status: 500,
        message: 'error',
        type: storageType,
        url: '',
      };
    }
    try {
      const EnterpriseID = await this.getTopEnterpriseId(id) || id;
      // TODO 替换 id
      target = target.replace(regex, `$1${EnterpriseID}`);
    } catch (error) {
      return {
        status: 500,
        message: 'error',
        type: storageType,
        url: '',
      };
    }

    // 2.获取资源时   async concatenatePath 也替换一下获取顶层
    // 旧文件的处理 脚本 处理一下都进行迁移到顶级企业id层级下 要支持两种存储 oss和nfs
    // oapi的文件存储也要改一下

    if (storageType.includes('oss')) {
      try {
        const targetSplit = target.replace(/\\/gi, '/').split('/public/');
        let filePath = targetSplit[targetSplit.length - 1];
        if (filePath.startsWith('/')) {
          filePath = filePath.substring(1);
        }

        let bucketName = '';
        let accessPolicy = '';
        if (bucketObj) {
          bucketName = bucketObj.name;
          accessPolicy = bucketObj.accessPolicy;
        } else {
          bucketName = option.buckets.default.name;
          accessPolicy = option.buckets.default.accessPolicy;
        }
        const client = await this.initCilent();

        const parsedUrl = url.parse(option.endPoint);

        const protocol = parsedUrl.protocol;
        const hostname = parsedUrl.hostname;
        const port = parsedUrl.port;

        const res = await client.putObject(
          bucketName,
          filePath,
          readableStream
        );
        // console.log('ossPipeRes', res);

        let fileUrl = '';
        if (accessPolicy === 'private') {
          fileUrl = await client.presignedUrl(
            'GET',
            bucketName,
            filePath,
            24 * 60 * 60
          );
        } else {
          fileUrl = `${protocol}//${hostname}${
            port ? ':' + port : ''
          }/${bucketName}/${filePath}`;
        }
        // console.log('ossPipeFileUrl', fileUrl);

        if (res && res.etag) {
          return {
            status: 200,
            type: storageType,
            url: fileUrl,
          };
        }
      } catch (error) {
        console.log('pipeError', error);
        return {
          status: 500,
          message: 'error',
          type: storageType,
          url: '',
        };
      }
    } else {
      // const fileName = path.basename(target)
      // 解析路径，创建缺少的目录结构
      const dirname = path.dirname(target);
      await mkdirp(dirname);
      // 默认本地存储
      const writeStream = fs.createWriteStream(target);
      try {
        await awaitWriteStream(readableStream.pipe(writeStream));
        return {
          status: 200,
          type: storageType ? storageType : 'local',
        };
      } catch (error) {
        console.log(error);
        await streamWormhole(writeStream);
        writeStream.destroy();
        return {
          status: 500,
          message: 'error',
          type: storageType,
        };
      }
    }
  },
  /**
   * @description: 文件是否存在
   * @param {*} target 必填 文件路径
   * @param {*} bucketObj 可选
   * @return {*}
   */
  async statObject(target, bucketObj) {
    const { app } = this;
    // 获取文件存储类型
    const storageType = app.config.storageType;

    let id = null;
    const regex = /([\\/](?:enterprise|tjReport)[\\/])([^\\\/]+)/;
    const match = regex.exec(target);
    if (match) {
      id = match[2];
    }
    if (!id) {
      return '';
    }
    try {
      const EnterpriseID = await this.getTopEnterpriseId(id) || id;
      // TODO 替换 id
      target = target.replace(regex, `$1${EnterpriseID}`);
      // console.log('target', target);
    } catch (error) {
      return '';
    }

    if (storageType === 'oss') {
      try {
        const option = app.config.oss;
        let bucketName = '';
        if (bucketObj) {
          bucketName = bucketObj.name;
        } else {
          bucketName = option.buckets.default.name;
        }
        const client = await this.initCilent();
        const targetSplit = target.replace(/\\/gi, '/').split('app/public/');
        const filePath = targetSplit[targetSplit.length - 1];
        await client.statObject(bucketName, filePath);
        // console.log('statObjectRes', ossRes);
        return true;
      } catch (error) {
        // console.log('statObjectError', error);
        if (error.code === 'NoSuchKey' || error.code === 'NotFound') {
          console.log(`${target}文件不存在`);
          return false;
        }
        console.error('Error:', error);
        throw error; // 其他错误重新抛出
      }
    }
  },
  /**
   * @description: 删除文件
   * @param {*} target 必填 文件路径
   * @param {*} bucketObj 可选
   * @return {*}
   */
  async deleteObject(target, bucketObj) {
    const { app } = this;
    // 获取文件存储类型
    const storageType = app.config.storageType;
    const result = await this.processEnterprisePath(target);
    target = result.newTarget;
    if (storageType === 'oss') {
      try {
        const option = app.config.oss;
        let bucketName = '';
        if (bucketObj) {
          bucketName = bucketObj.name;
        } else {
          bucketName = option.buckets.default.name;
        }
        const client = await this.initCilent();
        const targetSplit = target.replace(/\\/gi, '/').split('app/public/');
        const filePath = targetSplit[targetSplit.length - 1];
        // console.log('deleteParmas', bucketName, filePath);
        const ossRes = await client.removeObject(bucketName, filePath);
        if (!ossRes) {
          return {
            status: '200',
            message: 'success',
            type: storageType,
          };
        }
      } catch (error) {
        // console.log('deleteObjectError', error);
        return {
          status: 500,
          message: 'error',
          type: storageType,
        };
      }
    } else {
      // 默认本地存储
      try {
        if (fs.existsSync(target)) {
          fs.unlinkSync(target);
        }
        return {
          status: 200,
          message: 'sucess',
          type: storageType,
        };
      } catch (error) {
        return {
          status: 500,
          message: 'error',
          type: storageType,
        };
      }
    }
  },
  /**
   * @description 批量删除文件
   * @param {*} targetArr 必填 文件路径数组
   * @param {*} bucketObj 可选
   * @return {*}
   */
  async deleteObjects({ targetArr, bucketObj }) {
    const { app } = this;
    // 获取文件存储类型
    const storageType = app.config.storageType;
    if (!targetArr || targetArr.length === 0) return;
    // console.log('进来批量删除了哦', targetArr, targetArr.length);
    const newTargetArr = [];
    for (const target of targetArr) {
      const result = await this.processEnterprisePath(target);
      if (result.success) {
        newTargetArr.push(result.newTarget);
      }
    }

    if (storageType === 'oss') {
      try {
        const option = app.config.oss;
        let bucketName = '';
        if (bucketObj) {
          bucketName = bucketObj.name;
        } else {
          bucketName = option.buckets.default.name;
        }
        const client = await this.initCilent();
        const deleteObjects = newTargetArr.map(target => {
          const targetSplit = target.replace(/\\/gi, '/').split('/public/');
          const filePath = targetSplit[targetSplit.length - 1];
          return filePath;
        });
        // console.log('deleteObjects', bucketName, deleteObjects);
        try {
          if (this.app.config.oss.endPoint.includes('aliyun')) {
            throw '阿里云不支持批量删除';
          }
          await client.removeObjects(bucketName, deleteObjects, err => {
            // console.log('删除结果', res);
            if (err) {
              // console.log('删除失败', err);
              throw '删除失败';
            }
          });
        } catch (error) {
          // console.log('批量删除失败转单个删除');
          await Promise.all(
            newTargetArr.map(async target => {
              await this.deleteObject(target, bucketObj);
            })
          );
        }
        return {
          status: '200',
          message: 'success',
          type: storageType,
        };
      } catch (error) {
        console.log('deleteObjectsError', error);
        return {
          status: 500,
          message: 'error',
          type: storageType,
        };
      }
    } else {
      // 默认本地存储
      try {
        newTargetArr.forEach(target => {
          if (fs.existsSync(target)) {
            fs.unlinkSync(target);
          }
        });
        return {
          status: 200,
          message: 'success',
          type: storageType,
        };
      } catch (error) {
        return {
          status: 500,
          message: 'error',
          type: storageType,
        };
      }
    }
  },

  /**
   * @description: 下载对象并将其保存为本地文件系统中的文件
   * @param {*} objectPath 必填 存在oss上的文件路径
   * @param {*} filePath 必填 存在本机上的文件路径
   * @param {*} bucketObj 可选
   * @return {*}
   */
  async fGetObject({ objectPath, filePath, bucketObj }) {
    const { app } = this;
    const option = app.config.oss;
    let bucketName = '';
    if (bucketObj) {
      bucketName = bucketObj.name;
    } else {
      bucketName = option.buckets.default.name;
    }
    const client = await this.initCilent();
    try {
      // console.log('fGetObject参数', bucketName, objectPath, filePath);
      const res = await client.fGetObject(bucketName, objectPath, filePath);
      // console.log('fGetObjectRes', res);
      if (!res) {
        return {
          status: '200',
          message: 'success',
          type: app.config.storageType,
        };
      }
    } catch (error) {
      console.error('Error:', error);
    }
  },
  /**
   * @description: 获取对象的content
   * @param {*} objectPath 必填 存在oss上的文件路径
   */
  async getObjectContent({ objectPath, bucketObj }) {
    const { app } = this;
    const option = app.config.oss;
    let bucketName = '';
    const result = await this.processEnterprisePath(objectPath);
    objectPath = result.newTarget;
    if (bucketObj) {
      bucketName = bucketObj.name;
    } else {
      bucketName = option.buckets.default.name;
    }
    const client = await this.initCilent();
    try {
      const targetSplit = objectPath.replace(/\\/gi, '/').split('/public/');
      let filePath = targetSplit[targetSplit.length - 1];
      if (filePath.startsWith('/')) {
        filePath = filePath.substring(1);
      }
      // console.log('getObjectContent喽', filePath);

      return await client.getObject(bucketName, filePath);
    } catch (error) {
      // console.log('getObjectContent失败');
      console.error('Error:', error);
    }
  },

  // 将url转化为代理url
  async transformProxyUrl(url) {
    const { app } = this;
    const clientOpt = app.config.oss;
    return url.replace(`${clientOpt.endPoint}`, '/oss');
  },
  async concatenatePath({ path, bucketObj }) {
    const { app } = this;
    // 获取文件存储类型
    const storageType = app.config.storageType;
    // console.log('storageType：', storageType);
    // console.log('storageType === oss', storageType === 'oss');
    let id = null;
    const regex = /([\\/](?:enterprise|tjReport)[\\/])([^\\\/]+)/;
    const match = regex.exec(path);
    if (match) {
      id = match[2];
    }
    if (!id) {
      return '';
    }
    try {
      const EnterpriseID = await this.getTopEnterpriseId(id) || id;
      path = path.replace(regex, `$1${EnterpriseID}`);
      // console.log('path', path);
    } catch (error) {
      return '';
    }
    try {
      if (storageType.includes('oss')) {
        // console.log('oss拼接路径了');
        const clientOpt = app.config.oss;
        let bucketName = '';
        let accessPolicy = '';
        if (bucketObj) {
          bucketName = bucketObj.name;
          accessPolicy = bucketObj.accessPolicy;
        } else {
          bucketName = clientOpt.buckets.default.name;
          accessPolicy = clientOpt.buckets.default.accessPolicy;
        }
        // 判断bucket是否是私有的
        // 如果是私有，要对url签名然后返回给前端
        if (accessPolicy === 'private') {
          const client = await this.initCilent(bucketName);

          // // 如果路径首个带/，就把它去掉
          const pathUrl = path.replace(/^\//, '');
          // 获取临时的URL
          const reqParams = {
            'response-content-type': this.getMIME(pathUrl),
          };
          const res = await client.presignedUrl(
            'GET',
            bucketName,
            pathUrl,
            24 * 60 * 60,
            reqParams
          );
          // console.log('获取的临时的URL', res);
          if (res) {
            return res.replace(`${clientOpt.endPoint}`, '/oss');
            // return res;
          }
          return '';
        }
        // 如果是公开的oss，就直接返回这个
        // console.log(`${clientOpt.endPoint}/${bucketName}${path}`);
        return `${clientOpt.endPoint}/${bucketName}${path}`;
        // return `/oss/${bucketName}${path}`;
      }
      // 默认本地存储
      return app.config.static.prefix + path;
    } catch (error) {
      console.log('concatenatePathError', error);
      return '';
    }
  },
  async formatUrl(url) {
    try {
      // const ossConfig = this.app.config.oss;
      return url;
    } catch (error) {
      return '';
    }
  },
  /**
   * @description: 压缩文件并上传到 OSS
   * @param ctx
   * @param {Array} filePaths 文件路径数组
   * @param {string} zipFileName 压缩文件名
   * @return {Promise<Object>} 上传结果对象
   */
  async compressAndUploadToOSS(ctx, filePaths) {
    // 随机文件名
    const uploadFileName = this.generateFileName();
    // 创建可读流数组
    const readStreams = filePaths.map(filePath =>
      fs.createReadStream(filePath)
    );

    // 创建压缩流
    const archive = archiver('zip', { zlib: { level: 9 } });

    // 添加文件到压缩流
    readStreams.forEach(readStream => {
      const fileName = readStream.path.split('/').pop();
      archive.append(readStream, { name: fileName });
    });

    // 创建上传流
    const uploadStream = archive.pipe();
    const target = path.join(ctx.app.config.uploadPath, `${uploadFileName}`);
    const uploadResult = await this.ctx.helper.pipe({
      readableStream: uploadStream,
      target,
    });

    return uploadResult;
  },

  /**
   * @description: 随机生成文件名
   * @return {string} 文件名
   */
  generateFileName() {
    const date = new Date();
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();

    const random = Math.random().toString(36).substr(2, 8);

    return `${year}${month}${day}-${random}`;
  },
  /**
   * @description 获取mime文件类型
   * @param {string} pathUrl 文件路径
   * @return {string} 文件类型
   */
  getMIME(pathUrl) {
    const pathUrlSplit = pathUrl.split('.');
    const extension = pathUrlSplit[pathUrlSplit.length - 1].toLowerCase();

    const mimeTypes = {
      pdf: 'application/pdf',
      xls: 'application/vnd.ms-excel',
      xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      doc: 'application/msword',
      docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      jpg: 'image/jpeg',
      bmp: 'image/bmp',
      jpeg: 'image/jpeg',
      png: 'image/png',
      gif: 'image/gif',
      csv: 'text/csv',
      txt: 'text/plain',
      rtf: 'application/rtf',
      html: 'text/html',
      htm: 'text/html',
      zip: 'application/zip',
      tar: 'application/x-tar',
      rar: 'application/vnd.rar',
      // Add more as needed...
    };

    return mimeTypes[extension] || 'application/octet-stream';
  },

  // /**
  //  * @description: 北元用 生成通知代办接口sign
  //  * @param {*} params.apiName 接口名称
  //  * @param {*} params.query 请求参数
  //  */
  signAkSk({ apiName = '', params = {} }) {
    const { app } = this;
    if (app.config.branch !== 'by') return;
    const APIS = {
      sendNotification: {
        uri: '/open-api/p/notification/v2/messages',
        method: 'POST',
        params: {
          businessCode: {
            type: 'string',
            required: true,
            description: '消息发送方业务编号',
          },
          businessName: {
            type: 'string',
            required: true,
            description: '消息发送方业务名称',
          },
          receivers: {
            type: 'Array',
            required: true,
            description: '消息接收人',
          },
          contents: {
            type: 'Array',
            required: true,
            description: '消息内容',
          },
        },
      },
    };
    if (!app.config.byNotice) return;
    const { api_host: API_BASE_HOST, ak, sk } = app.config.byNotice;
    const headerJson = {
      'Content-Type': 'application/json;charset=utf-8',
    };
    const wholeUrl = API_BASE_HOST + APIS[apiName].uri;
    integratesBy.signHeaderWithAkSk(
      APIS[apiName].uri,
      APIS[apiName].method,
      null,
      headerJson,
      ak,
      sk
    );
    Axios({
      method: APIS[apiName].method,
      url: wholeUrl,
      headers: headerJson,
      data: params,
    })
      .then(response => {
        console.log('response:', response.data);
      })
      .catch(error => {
        console.error(error);
      });
  },

  // 获取redis缓存信息
  async getRedis(key) {
    if (!key || typeof key !== 'string') return null;
    const { ctx, app } = this;

    try {
      // 检测键类型
      const keyType = await app.redis.type(key);

      let result;

      // 根据键类型选择获取方式
      if (keyType === 'string') {
        result = await app.redis.get(key);
        return result ? JSON.parse(result) : null;
      } else if (keyType === 'hash') {
        result = await app.redis.hgetall(key);

        // 将哈希表的值放入数组
        return Object.values(result).map(value => {
          try {
            return JSON.parse(value); // 尝试将值解析为对象或数组
          } catch (e) {
            return value; // 若解析失败，返回原始值
          }
        });
      }

      // 如果键类型不是字符串或哈希表，则返回 null
      return null;
    } catch (err) {
      ctx.auditLog('获取缓存信息失败', JSON.stringify(err.message), 'error');
      return null;
    }
  },
  // 设置redis缓存信息
  // key：string 缓存key
  // value: any, // 缓存值, 无需序列化，iService2会自动处理
  // ttl: number 过期时间，单位秒,不设置的话默认为永久有效
  async setRedis(key, value, ttl) {
    const { app, config, ctx } = this;

    if (!key || value === undefined) {
      throw new Error('key 或 value 不能为空');
    }

    try {
      // 判断 value 的类型并进行相应处理
      if (Array.isArray(value) && value.length > 0) {
        const pipeline = app.redis.pipeline();

        // 使用 hmset 将整个数组作为哈希存储
        const hashData = {};
        value.forEach((val, index) => {
          hashData[index] = JSON.stringify(val); // 确保以字符串形式存储
        });

        pipeline.hmset(key, hashData);
        await pipeline.expire(key, ttl || config.redis_ttl); // 设置过期时间
        await pipeline.exec(); // 执行管道命令
      } else {
        await app.redis.set(
          key,
          JSON.stringify(value),
          'EX',
          ttl || config.redis_ttl
        ); // 确保以字符串形式存储
      }
    } catch (error) {
      ctx.auditLog('设置缓存信息失败', JSON.stringify(error.message), 'error');
    }
  },

  // redis哈希存储
  async setRedisHash(key, field, value, ttl) {
    const { app, config, ctx } = this;

    if (!key || !field || value === undefined) {
      throw new Error('key, field 或 value 不能为空');
    }
    try {
      await app.redis.hset(key, field, value);
      await app.redis.expire(key, ttl || config.redis_ttl);
    } catch (error) {
      ctx.auditLog('设置缓存信息失败', JSON.stringify(error.message), 'error');
    }
  },

  /**
   * 删除Redis缓存数据
   * @param {string} key - 缓存键名
   * @return {Promise<boolean>} 删除成功返回true，失败返回false
   */
  async delRedis(key) {
    try {
      await this.app.redis.del(key);
      return true;
    } catch (error) {
      this.ctx.logger.error('删除Redis缓存失败', error);
      return false;
    }
  },

  /**
   * 批量删除Redis缓存数据
   * @param {string} pattern - 缓存键名模式
   * @return {Promise<boolean>} 删除成功返回true，失败返回false
   */
  async delRedisPattern(pattern) {
    try {
      // 获取所有匹配模式的键
      const keys = await this.app.redis.keys(pattern);

      // 如果没有匹配的键，直接返回成功
      if (!keys || keys.length === 0) {
        return true;
      }

      // 使用 pipeline 批量删除以提高效率
      const pipeline = this.app.redis.pipeline();
      keys.forEach(key => pipeline.del(key));
      await pipeline.exec();

      this.ctx.logger.info(`成功删除 ${keys.length} 个匹配 ${pattern} 的缓存`);
      return true;
    } catch (error) {
      this.ctx.logger.error(`批量删除 Redis 缓存失败: ${pattern}`, error);
      return false;
    }
  },

  /**
   * 处理路径中的企业ID替换
   * @param {string} target - 目标路径
   * @return {Promise<{success: boolean, newTarget: string }>}
   */
  async processEnterprisePath(target) {
    const regex = /([\\/](?:enterprise|tjReport)[\\/])([^\\\/]+)/;
    const match = regex.exec(target);

    if (!match) {
      return { success: false, newTarget: target };
    }

    const id = match[2];

    try {
      const EnterpriseID = await this.getTopEnterpriseId(id) || id;
      const newTarget = target.replace(regex, `$1${EnterpriseID}`);
      return { success: true, newTarget };
    } catch (error) {
      return { success: false, newTarget: target };
    }
  },
};
