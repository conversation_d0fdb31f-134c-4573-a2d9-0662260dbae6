---
description: vue2的接口请求规则
globs: 
alwaysApply: false
---
# API 请求规范

## 基本结构与组织
只允许使用get和post方式
### 1. 目录结构
API 文件定义 (src/api/模块名.js)
```
backstage/模块名/
├── src/
│   ├── api/                  # API 接口定义目录
│   │   ├── index.js          # 主要 API 接口
│   │   └── 功能模块.js        # 按功能模块拆分的 API 接口
```

### 2. 导入规范
```javascript
// 统一从公共方法中导入 request
import request from '@root/publicMethods/request';
```

## API 定义规范
### 命名方式：单独导出函数
```javascript
// GET 请求示例
export function getList(params) {
  return request({
    url: '/manage/模块名/getList',
    method: 'get',
    params,
  });
}

// POST 请求示例
export function saveData(data) {
  return request({
    url: '/manage/模块名/saveData',
    method: 'post',
    data,
  });
}
```

### 3. 命名规范
- GET 请求函数名：使用 `get` + 实体名 + 操作，如 `getDoublePreventionList`
- POST 请求函数名：使用动词 + 实体名，如 `saveDefendProductList`
- 函数名采用 camelCase 命名法
- URL 路径：`/manage/模块名/操作`（管理后台）或 `/api/模块名/操作`（前台 API）

### 4. 注释规范
```javascript
/**
 * 获取双重预防体系列表
 * @param {Object} params - 查询参数
 * @param {Number} params.page - 页码
 * @param {Number} params.limit - 每页条数
 * @returns {Promise} - 返回请求的 Promise 对象
 */
export function getDoublePreventionList(params) {
  // 配置加载状态 如果不需要加载动画的话isLoadingMaskDisable: true 默认没有
  params.loadingConfig = { isLoadingMaskDisable: true };
  return request({
    url: '/manage/doublePrevention/getSanitaryList',
    method: 'get',
    params,
  });
}
```

## 请求配置规范

### 1. 请求参数
- GET 请求：使用 `params` 参数
- POST 请求：使用 `data` 参数
- 加载状态配置：通过 `loadingConfig` 参数控制

```javascript
// GET 请求示例
export function getList(params) {
  // 禁用加载遮罩
  params.loadingConfig = { isLoadingMaskDisable: true };
  return request({
    url: '/manage/模块名/list',
    method: 'get',
    params,
  });
}

// POST 请求示例
export function saveData(data) {
  // 自定义加载文本
  data.loadingConfig = { str: '保存中...' };
  return request({
    url: '/manage/模块名/save',
    method: 'post',
    data,
  });
}
```

### 2. 超时设置
```javascript
// 在 request.js 中统一设置了超时时间
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API,
  timeout: 1000 * 60 * 2, // 2分钟超时
});
```

## 拦截器规范

### 1. 请求拦截器
```javascript
// 请求拦截器
service.interceptors.request.use(
  config => {
    // 处理加载状态
    let loadingConfig;
    if (config.method === 'get') {
      // 处理 GET 请求参数序列化
      config.paramsSerializer = function(params) {
        return qs.stringify(params, { arrayFormat: 'repeat' });
      };
      // 提取加载配置
      loadingConfig = !_.isEmpty(config.params) && !_.isEmpty(config.params.loadingConfig)
        ? config.params.loadingConfig
        : {};
    } else if (config.method === 'post') {
      // 提取加载配置
      loadingConfig = !_.isEmpty(config.data) && !_.isEmpty(config.data.loadingConfig)
        ? config.data.loadingConfig
        : {};
    }
    
    // 显示加载状态
    if (!config.url.startsWith('/manage/courses')) {
      showFullScreenLoading(loadingConfig);
    }
    
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);
```

### 2. 响应拦截器
```javascript
// 响应拦截器
service.interceptors.response.use(
  response => {
    // 隐藏加载状态
    tryHideFullScreenLoading();
    
    const res = response.data;
    
    // 处理非 200 状态码
    if (res.status !== 200 && res.code !== 200) {
      // 显示错误消息
      if (!_.isEmpty(res.message)) {
        Message({
          message: res.message,
          type: 'error',
          duration: 5 * 1000,
        });
      }
      
      // 处理特定错误码
      if (res.status === 50008 || res.status === 50012 || res.status === 50014) {
        // 处理登录过期
        MessageBox.confirm('登录已过期，请重新登录', '确认登出', {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          location.reload();
        });
      }
      
      return Promise.reject(new Error(res.message || 'Error'));
    }
    
    return res;
  },
  error => {
    // 隐藏加载状态
    tryHideFullScreenLoading();
    
    // 处理 401 未授权
    if (error.response && error.response.status === 401) {
      window.location = '/admin/login';
      return;
    }
    
    // 提取错误信息
    let errorMsg = error.message;
    if (error.response && error.response.data && error.response.data.message) {
      errorMsg = error.response.data.message;
    }
    
    // 处理网络错误
    if (errorMsg === 'Network Error' || errorMsg === 'Request failed with status code 502') {
      return {
        status: 500,
        message: 'Network Error',
      };
    }
    
    // 显示错误消息
    if (error.response && error.response.status === 510) {
      Message({
        message: errorMsg,
        type: 'info',
        duration: 5 * 1000,
      });
    } else {
      Message({
        message: errorMsg,
        type: 'warning',
        duration: 5 * 1000,
      });
    }
    
    return Promise.reject(error);
  }
);
```

## 加载状态管理

### 1. 显示加载状态
```javascript
export function showFullScreenLoading(loadingConfig = {}) {
  // 保存配置
  if (!_.isEmpty(loadingConfig)) {
    configs = loadingConfig;
  } else {
    configs = {};
  }
  
  // 如果未禁用加载遮罩，则显示
  if (!configs.isLoadingMaskDisable) {
    if (needLoadingRequestCount === 0) {
      startLoading();
    }
    needLoadingRequestCount++;
  }
}

function startLoading() {
  loading = Loading.service({
    lock: true,
    text: configs.str ? configs.str : '数据加载中...',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.7)',
  });
}
```

### 2. 隐藏加载状态
```javascript
export function tryHideFullScreenLoading() {
  if (needLoadingRequestCount <= 0) return;
  needLoadingRequestCount--;
  if (needLoadingRequestCount === 0) {
    _.debounce(tryCloseLoading, 300)();
  }
}

const tryCloseLoading = () => {
  if (needLoadingRequestCount === 0 && !configs.alwaysShow) {
    endLoading();
  }
};

function endLoading() {
  loading.close();
}
```

## 错误处理规范

### 1. 统一错误处理
```javascript
// 在响应拦截器中统一处理错误
if (res.status !== 200 && res.code !== 200) {
  // 显示错误消息
  Message({
    message: res.message || '请求失败',
    type: 'error',
    duration: 5 * 1000,
  });
  
  return Promise.reject(new Error(res.message || 'Error'));
}
```

### 2. 特定错误处理
```javascript
// 在 API 调用处处理特定错误
try {
  const result = await getDoublePreventionList(params);
  // 处理成功响应
} catch (error) {
  // 处理特定错误
  console.error('获取列表失败:', error);
}
```

## 最佳实践

### 1. 参数处理
- GET 请求使用 `params` 传递查询参数
- POST 请求使用 `data` 传递请求体
- 数组参数使用 `arrayFormat: 'repeat'` 格式化

### 2. 加载状态控制
- 通过 `loadingConfig.isLoadingMaskDisable` 控制是否显示加载遮罩
- 通过 `loadingConfig.str` 自定义加载文本
- 通过 `loadingConfig.alwaysShow` 控制是否始终显示加载状态

### 3. 错误处理
- 使用 try/catch 捕获 API 调用错误
- 在响应拦截器中统一处理通用错误
- 在 API 调用处处理特定业务错误

### 4. 代码组织
- 按功能模块拆分 API 文件
- 相关 API 放在同一文件中
- 使用有意义的函数名和注释

## 禁止事项

1. 禁止在组件中直接使用 axios，必须通过封装的 request 方法调用
2. 禁止在 API 定义中硬编码完整 URL，应使用相对路径
3. 禁止省略错误处理
4. 禁止在 API 调用中包含业务逻辑
5. 禁止在多个地方重复定义相同的 API
