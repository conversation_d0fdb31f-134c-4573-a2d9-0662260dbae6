exports.jk_Wzq = {
  alias: 'wzq', // 插件目录，必须为英文
  pkgName: 'egg-jk-wzq', // 插件包名
  enName: 'jk_Wzq', // 插件名
  name: '用户', // 插件名称
  description: '用户', // 插件描述
  adminApi: [
    // preventionfunds
    {
      url: 'preventionfunds/create',
      method: 'post',
      controllerName: 'index',
      details: '添加表单数据',
    },
    {
      url: 'preventionfunds/find',
      method: 'get',
      controllerName: 'getAllData',
      details: '查找所有数据',
    },
    {
      url: 'preventionfunds/find',
      method: 'post',
      controllerName: 'getTableData',
      details: '按年度查找表单数据',
    },
    {
      url: 'preventionfunds/delete',
      method: 'post',
      controllerName: 'delete',
      details: '删除数据',
    },
    {
      url: 'preventionfunds/resettemplate',
      method: 'get',
      controllerName: 'resetTemplate',
      details: '重置模板',
    },
    {
      url: 'preventionfunds/uploadfile',
      method: 'post',
      controllerName: 'uploadfile',
      details: '上传文件',
    },
    {
      url: 'preventionfunds/deleteFile',
      method: 'post',
      controllerName: 'deleteFile',
      details: '删除文件',
    },
    {
      url: 'preventionfunds/deletePlanFile',
      method: 'post',
      controllerName: 'deletePlanFile',
      details: '删除计划文件',
    },
    {
      url: 'preventionfunds/copyData',
      method: 'post',
      controllerName: 'copyData',
      details: '复制数据',
    },
    {
      url: 'preventionfunds/changeTemp',
      method: 'post',
      controllerName: 'changeTemp',
      details: '切换模板',
    },
    {
      url: 'preventionfunds/uploadTotmp',
      method: 'post',
      controllerName: 'uploadTotmp',
      details: '上传文件',
    },
    {
      url: 'warnnotice/uploadCard',
      method: 'post',
      controllerName: 'uploadCard',
      details: '上传告知卡',
    },
    // {
    //   url: 'preventionfunds/downloadtemplate',
    //   method: 'get',
    //   controllerName: 'downloadtemplate',
    //   details: '下载模板文件',
    // },
    // propagate

    {
      url: 'repropagate/getPropagateList',
      method: 'get',
      controllerName: 'getPropagateList',
      details: '获取培训记录数据',
    },

    {
      url: 'repropagate/add',
      method: 'post',
      controllerName: 'create',
      details: '获取培训记录数据',
    },

    {
      url: 'repropagate/update',
      method: 'post',
      controllerName: 'update',
      details: '获取培训记录数据',
    },
    {
      url: 'repropagate/getPropagatePersonList',
      method: 'get',
      controllerName: 'getPropagatePersonList',
      details: '获取培训个人数据',
    },
    {
      url: 'repropagate/delete',
      method: 'post',
      controllerName: 'delete',
      details: '获取培训个人数据',
    },
    {
      url: 'propagate/search',
      method: 'post',
      controllerName: 'findByYear',
      details: '获取表格数据',
    },
    {
      url: 'propagate/add',
      method: 'post',
      controllerName: 'create',
      details: '新增表格数据',
    },
    {
      url: 'propagate/update',
      method: 'post',
      controllerName: 'update',
      details: '修改表格数据',
    },
    {
      url: 'propagate/delete',
      method: 'post',
      controllerName: 'deleteById',
      details: '删除表格数据',
    },
    {
      url: 'propagate/getCascader',
      method: 'get',
      controllerName: 'getCascader',
      details: '获取厂房车间岗位',
    },
    {
      url: 'propagate/getCascaderPerson',
      method: 'post',
      controllerName: 'getCascaderPerson',
      details: '获取岗位下的人员信息',
    },
    // preventionFunding
    {
      url: 'preventionFunding/findFundByYear',
      method: 'get',
      controllerName: 'findFundByYear',
      details: '查询年度防治经费',
    },
    {
      url: 'preventionFunding/getLeadOptions',
      method: 'get',
      controllerName: 'getLeadOptions',
      details: '获取领导人选项',
    },
    {
      url: 'preventionFunding/createFund',
      method: 'post',
      controllerName: 'createFund',
      details: '新增防治经费记录',
    },
    {
      url: 'preventionFunding/editFund',
      method: 'post',
      controllerName: 'editFund',
      details: '编辑防治经费记录',
    },
    {
      url: 'preventionFunding/deleteFund',
      method: 'post',
      controllerName: 'deleteFund',
      details: '删除防治经费记录',
    },
    {
      url: 'preventionFunding/exportExcel',
      method: 'post',
      controllerName: 'exportExcel',
      details: '导出防治经费管理表',
    },
    // preventionFundingStatistics
    {
      url: 'preventionFunding/getStatisticsData',
      method: 'get',
      controllerName: 'getStatisticsData',
      details: '统计集团及其分子公司每类的防治经费',
    },
    {
      url: 'preventionFunding/getStatisticsByPeriod',
      method: 'get',
      controllerName: 'getStatisticsByPeriod',
      details: '统计集团及其分子公司每年/每月的防治经费',
    },
    {
      url: 'preventionFunding/getStatisticsByCompany',
      method: 'get',
      controllerName: 'getStatisticsByCompany',
      details: '统计各个公司的年度防治经费',
    },
    // preventionPlan
    {
      url: 'preventionPlan/findPlanByYear',
      method: 'get',
      controllerName: 'findPlanByYear',
      details: '查询年度防治计划',
    },
    {
      url: 'preventionPlan/createPlan',
      method: 'post',
      controllerName: 'createPlan',
      details: '新增防治计划记录',
    },
    {
      url: 'preventionPlan/editPlan',
      method: 'post',
      controllerName: 'editPlan',
      details: '编辑防治计划记录',
    },
    {
      url: 'preventionPlan/deletePlan',
      method: 'post',
      controllerName: 'deletePlan',
      details: '删除防治计划记录',
    },
    {
      url: 'preventionPlan/exportExcel',
      method: 'post',
      controllerName: 'exportExcel',
      details: '导出防治计划管理表',
    },
    {
      url: 'preventionPlan/findMillConstruction',
      method: 'get',
      controllerName: 'findMillConstruction',
      details: '获取车间/岗位/人员结构',
    },
    {
      url: 'preventionPlan/getImplementData',
      method: 'get',
      controllerName: 'getImplementData',
      details: '查询年度防治计划实施情况',
    },
    {
      url: 'preventionPlan/updateData',
      method: 'post',
      controllerName: 'updateData',
      details: '更新防治计划实施情况',
    },
    {
      url: 'preventionPlan/getPlanDetail',
      method: 'get',
      controllerName: 'getPlanDetail',
      details: '获取防治计划详情',
    },
    {
      url: 'preventionPlan/getStatisticsData',
      method: 'get',
      controllerName: 'getStatisticsData',
      details: '统计集团及其分子公司每年的防治计划情况',
    },
    // diseasesOverhaul
    {
      url: 'diseasesoverhaul/find',
      method: 'get',
      controllerName: 'index',
      details: '获取表格数据',
    },
    {
      url: 'diseasesoverhaul/add',
      method: 'post',
      controllerName: 'create',
      details: '新增表格数据',
    },
    {
      url: 'diseasesoverhaul/update',
      method: 'post',
      controllerName: 'update',
      details: '修改表格数据',
    },
    {
      url: 'diseasesoverhaul/delete',
      method: 'post',
      controllerName: 'deleteById',
      details: '删除表格数据',
    },
    {
      url: 'diseasesoverhaul/searchPerson',
      method: 'post',
      controllerName: 'searchPerson',
      details: '远程搜索厂房',
    },
    // 警示标识
    {
      url: 'warnnotice/find',
      method: 'get',
      controllerName: 'index',
      details: '查询数据',
    },
    {
      url: 'warnnotice/create',
      method: 'post',
      controllerName: 'create',
      details: '添加数据',
    },
    {
      url: 'warnnotice/update',
      method: 'post',
      controllerName: 'update',
      details: '更新数据',
    },
    {
      url: 'warnnotice/delete',
      method: 'post',
      controllerName: 'delete',
      details: '删除数据',
    },
    {
      url: 'warnnotice/getWarnList',
      method: 'get',
      controllerName: 'getWarnList',
      details: '获取警示列表',
    },
    // ledger
    {
      url: 'ledger/uploadData',
      method: 'post',
      controllerName: 'uploadData',
      details: '获取数据',
    },
    {
      url: 'ledger/find',
      method: 'get',
      controllerName: 'index',
      details: '获取数据',
    },
    {
      url: 'ledger/getYearList',
      method: 'get',
      controllerName: 'getYearList',
      details: '获取所有年度',
    },
    {
      url: 'ledger/getSubComLedger',
      method: 'get',
      controllerName: 'getSubComLedger',
      details: '获取子公司原料设备数据',
    },
    {
      url: 'ledger/findByYear',
      method: 'post',
      controllerName: 'findByYear',
      details: '获取某年数据',
    },
    {
      url: 'ledger/findSubByYear',
      method: 'post',
      controllerName: 'findSubByYear',
      details: '获取子公司原料设备数据详细信息',
    },
    {
      url: 'ledger/getLedgerData',
      method: 'post',
      controllerName: 'getLedgerData',
      details: '获取分页数据',
    },
    {
      url: 'ledger/delete',
      method: 'post',
      controllerName: 'deleteById',
      details: '删除某年数据',
    },
    {
      url: 'ledger/update',
      method: 'post',
      controllerName: 'update',
      details: '更新数据',
    },
    {
      url: 'ledger/uploadTotmp',
      method: 'post',
      controllerName: 'uploadTotmp',
      details: '上传文件',
    },
    {
      url: 'ledger/deleteAnnex',
      method: 'post',
      controllerName: 'deleteAnnex',
      details: '删除文件',
    },
    {
      url: 'ledger/copyData',
      method: 'post',
      controllerName: 'copyData',
      details: '复制文件',
    },
    {
      url: 'ledger/handleImage',
      method: 'post',
      controllerName: 'handleImage',
      details: '处理文件',
    },
    // {
    //   url: 'ledger/getHarmFactors',
    //   method: 'post',
    //   controllerName: 'getHarmFactors',
    //   details: '获取危害因素',
    // },
    // testAppointment
    {
      url: 'testappointment/find',
      method: 'get',
      controllerName: 'index',
      details: '获取数据',
    },
    {
      url: 'testappointment/create',
      method: 'post',
      controllerName: 'create',
      details: '提交数据',
    },
    // 体检套餐管理
    {
      url: 'checkItems/getCheckItems',
      method: 'get',
      controllerName: 'getCheckItems',
      details: '查询体检项目',
    },
    {
      url: 'checkItems/createCheckItems',
      method: 'post',
      controllerName: 'createCheckItems',
      details: '新增体检项目',
    },
    {
      url: 'checkItems/updateCheckItems',
      method: 'post',
      controllerName: 'updateCheckItems',
      details: '更新体检项目',
    },
    {
      url: 'checkItems/deleteCheckItems',
      method: 'post',
      controllerName: 'deleteCheckItems',
      details: '删除体检项目',
    },
    {
      url: 'checkItems/exportExcel',
      method: 'post',
      controllerName: 'exportExcel',
      details: '导出体检项目表',
    },
    // 体检套餐预览
    {
      url: 'checkItems/getCheckupPackage',
      method: 'get',
      controllerName: 'getCheckupPackage',
      details: '获取体检套餐',
    },
    // 体检项目审核
    {
      url: 'checkItems/getAuditCheckItems',
      method: 'get',
      controllerName: 'getAuditCheckItems',
      details: '获取数据',
    },
    {
      url: 'checkItems/submitAduit',
      method: 'post',
      controllerName: 'submitAduit',
      details: '审核数据',
    },
    // 体检项目管理
    {
      url: 'checkItems/getIndicator',
      method: 'get',
      controllerName: 'getIndicator',
      details: '获取数据',
    },
    {
      url: 'checkItems/updateIndicator',
      method: 'post',
      controllerName: 'updateIndicator',
      details: '更新数据',
    },
    {
      url: 'checkItems/createIndicator',
      method: 'post',
      controllerName: 'createIndicator',
      details: '创建数据',
    },
    {
      url: 'checkItems/findChildren',
      method: 'get',
      controllerName: 'findChildren',
      details: '创建数据',
    },
    // 问卷
    {
      url: 'wj/getProjectsByDepts',
      method: 'post',
      controllerName: 'getProjectsByDepts',
      details: '根据多个部门ID查询问卷列表',
    },
    {
      url: 'wj/updateProjectUsers',
      method: 'post',
      controllerName: 'updateProjectUsers',
      details: '更新项目关联的用户',
    },


  ],
  fontApi: [],
  initData: '', // 初始化数据脚本
  pluginsConfig: ` 
    exports.jk_Wzq = {\n
        enable: true,\n        package: 'egg-jk-wzq',
    };\n
    `, // 插入到 plugins.js 中的配置
  defaultConfig: `
    wzqRouter:{\n
        match: [ctx => ctx.path.startsWith('/manage/propagate')],\n
    },\n
    `, // 插入到 config.default.js 中的配置

  // 列表查询每页显示的数量，默认为10
  // pageSize: 10,
  multipart: {
    fileSize: '50mb',
    fields: '100',
    mode: 'stream',
    whitelist: [ '.xlsx' ], // 文件类型白名单;报400错;一般就是你没写这句话;允许接收解析该类型文件;
    fileExtensions: [ 'docx', 'doc', 'xlsx', 'mp4', 'pdf', 'avi', 'rar' ],
  },
  // 图片上传路径
  propagateUploadPath: './app/public/upload/propagate',
  preventionFundsUploadPath: './app/public/upload/preventionFunds',
  ledgerUploadPath: './app/public/upload/ledger',
  security: {
    csrf: {
      ignoreJSON: true,
    },
    // 白名单
    // domainWhiteList: ['*'],
  },
};

