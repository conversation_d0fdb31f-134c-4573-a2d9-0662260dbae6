/*
 * @Author: WZQ
 * @Date: 2021-06-10 09:03:25
 * @LastEditTime: 2021-06-10 15:17:54
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \jcqlc\lib\plugin\egg-jk-spotRecord\app\model\spotProject.js
 */
module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  // 单个项目的 现场采样单汇总情况表
  const SpotProjectSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    companyName: String, // 受检单位名称
    EnterpriseID: String, // 受检单位ID
    serviceOrgId: String, // 机构ID
    jobHealthId: {
      type: String,
      require,
    },

    projectNumber: String, // 项目编号
    projectName: String, // 项目名称
    batch: String, // 批次
    checkType: String, // 检测类型 (定期 评价 ...)

    signManage: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      signRole: String, // 陪同人 采样人 测量人
      name: String, // 姓名
      signPath: String, // 签名文件储存位置
      signTime: Date, // 签名时间
    }],

    // 天气情况
    weatherScene: {
      weather: String, // 天气
      pressure: String, // 气压
      temperature: String, // 温度
      humidity: String, // 湿度
    },

    expectTimeStart: Date, // 预计 开始时间从方案中来，用于排序
    expectTimeEnd: Date, // 预计 结束时间从方案中来，用于排序

    samplingTime: Date, // 现场记录单 实际 开始时间
    complete: {
      type: Number,
      default: 0,
    }, // 是否完成  0 未完成 1 进行中 2 已完成
    samplingConfirmDate: Date, // 现场记录单 实际 完成确认时间
    createAt: { // 创建日期
      type: Date,
    },
    updateAt: { // 更新时间
      type: Date,
    },
  }, { timestamps: true });


  return mongoose.model('SpotProject', SpotProjectSchema, 'spotProject');
};
