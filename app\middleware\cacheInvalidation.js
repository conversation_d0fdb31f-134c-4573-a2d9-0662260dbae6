/**
 * 缓存失效中间件
 * 当特定数据更新时自动清除相关缓存
 */

module.exports = () => {
  return async function cacheInvalidation(ctx, next) {
    await next();

    // 只在成功的POST/PUT/DELETE请求后处理缓存失效
    if (ctx.status >= 200 && ctx.status < 300 && [ 'POST', 'PUT', 'DELETE' ].includes(ctx.method)) {
      const enterpriseId = ctx.session && ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';

      if (enterpriseId) {
        // 检查是否是影响接害人统计的操作
        const shouldInvalidateCache = checkShouldInvalidateCache(ctx);

        if (shouldInvalidateCache) {
          try {
            // 异步清除缓存，不阻塞响应
            setImmediate(async () => {
              try {
                await ctx.service.redisData.invalidateHarmStatisticsCache(ctx, enterpriseId);
              } catch (error) {
                ctx.logger.error(`[缓存失效中间件] 清除缓存失败: ${error.message}`);
              }
            });
          } catch (error) {
            // 缓存失效失败不应该影响正常业务
            ctx.logger.error(`[缓存失效中间件] 处理失败: ${error.message}`);
          }
        }
      }
    }
  };
};

/**
 * 检查是否应该清除接害人统计缓存
 * @param {Object} ctx - 上下文对象
 * @returns {boolean} 是否应该清除缓存
 */
function checkShouldInvalidateCache(ctx) {
  const url = ctx.url;
  const path = ctx.path;

  // 影响接害人统计的API路径
  const invalidationPatterns = [
    // MillConstruction相关
    '/api/mill',
    '/manage/mill',
    '/api/workspace',
    '/manage/workspace',
    '/api/station',
    '/manage/station',

    // CheckResult相关
    '/api/checkResult',
    '/manage/checkResult',
    '/api/detection',
    '/manage/detection',

    // Employee相关
    '/api/employee',
    '/manage/employee',
    '/api/employees',
    '/manage/employees',

    // 危害因素相关
    '/api/harmFactor',
    '/manage/harmFactor',
    '/api/hazard',
    '/manage/hazard',
  ];

  // 检查URL是否匹配需要清除缓存的模式
  return invalidationPatterns.some(pattern =>
    path.includes(pattern) || url.includes(pattern)
  );
}
