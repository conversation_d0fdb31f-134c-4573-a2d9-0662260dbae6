const Controller = require('egg').Controller;
const shortid = require('shortid');


const {
  adminGroupRule,
} = require('@validate');

// const _ = require('lodash');

class AdminGroupController extends Controller {
  // 此处只是单纯的角色管理，用于临时用，可将文件删除
  async list() {
    const {
      ctx,
      service,
    } = this;
    try {

      const payload = ctx.query;
      const adminGroupList = await service.adminGroup.find(payload);

      ctx.helper.renderSuccess(ctx, {
        data: adminGroupList,
      });

    } catch (err) {

      ctx.helper.renderFail(ctx, {
        message: err,
      });

    }
  }

  async create() {

    const {
      ctx,
      service,
    } = this;
    try {
      const fields = ctx.request.body || {};

      const formObj = {
        _id: fields._id || shortid.generate(), // 测试用
        name: fields.name,
        comments: fields.comments,
      };


      ctx.validate(adminGroupRule.form(ctx), formObj);


      await service.adminGroup.create(formObj);

      ctx.helper.renderSuccess(ctx);

    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }


  async getOne() {
    const {
      ctx,
      service,
    } = this;
    try {
      const _id = ctx.query.id;

      const targetUser = await service.adminGroup.item(ctx, {
        query: {
          _id,
        },
      });

      ctx.helper.renderSuccess(ctx, {
        data: targetUser,
      });

    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }

  }


  async update() {

    const {
      ctx,
      service,
    } = this;
    try {

      const fields = ctx.request.body || {};
      const formObj = {
        name: fields.name,
        comments: fields.comments,
        power: fields.power,
      };

      ctx.validate(adminGroupRule.form(ctx), formObj);

      await service.adminGroup.update(ctx, fields._id, formObj);

      ctx.helper.renderSuccess(ctx);

    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }


  async removes() {
    const {
      ctx,
      service,
    } = this;
    try {
      const targetIds = ctx.query.ids;
      await service.adminGroup.removes(ctx, targetIds);
      ctx.helper.renderSuccess(ctx);

    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }
}

module.exports = AdminGroupController;
