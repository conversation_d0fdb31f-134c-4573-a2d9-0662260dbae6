/**
 * @file 万华商城订单控制器
 * @description 万华商城订单相关接口
 * @createDate 2025-03-14
 */

const Controller = require('egg').Controller;

/**
 * @controller 万华商城订单管理
 */
class WhOrderController extends Controller {
  /**
   * @summary 获取订单列表
   * @description 获取万华商城订单列表，支持分页和条件筛选
   * @return {Promise<void>} 无返回值
   */
  async getOrderList() {
    const { ctx, service } = this;
    try {
      // 获取查询参数
      const params = ctx.query;

      // 获取订单列表
      const result = await service.manage.whOrderService.getOrderList(params);

      // 记录操作日志
      ctx.auditLog('查询万华商城订单列表', `查询条件: ${JSON.stringify(params)}`, 'info');

      // 返回结果
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      // 记录错误日志
      ctx.logger.error('获取订单列表失败:', err);

      // 返回错误信息
      ctx.helper.renderFail(ctx, {
        message: err.message || '获取订单列表失败',
      });
    }
  }

  /**
   * @summary 获取订单详情
   * @description 根据订单ID获取订单详情及订单项
   * @return {Promise<void>} 无返回值
   */
  async getOrderDetail() {
    const { ctx, service } = this;
    try {
      // 获取订单ID
      const { id } = ctx.params;
      // 获取订单详情
      const result = await service.manage.whOrderService.getOrderDetail(id);

      // 记录操作日志
      ctx.auditLog('查询万华商城订单详情', `订单ID: ${id}`, 'info');

      // 返回结果
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      // 记录错误日志
      ctx.logger.error('获取订单详情失败:', err);

      // 返回错误信息
      ctx.helper.renderFail(ctx, {
        message: err.message || '获取订单详情失败',
      });
    }
  }

  /**
   * @summary 创建订单
   * @description 创建新的万华商城订单
   * @return {Promise<void>} 无返回值
   */
  async createOrder() {
    const { ctx, service } = this;
    try {
      // 获取请求体参数
      const { orderData, orderDetails } = ctx.request.body;

      // 参数校验
      ctx.validate({
        orderData: {
          type: 'object',
          rule: {
            orderName: { type: 'string', required: true },
            personnelCode: { type: 'string', required: true },
            personnelName: { type: 'string', required: true },
            orderPrice: { type: 'number', required: true },
            orgCode: { type: 'string', required: false },
            orgName: { type: 'string', required: false },
          },
        },
        orderDetails: {
          type: 'array',
          itemType: 'object',
          rule: {
            sku: { type: 'string', required: true },
            goodsName: { type: 'string', required: true },
            buyNumber: { type: 'number', required: true },
            goodsPrice: { type: 'number', required: true },
          },
          min: 1,
        },
      }, ctx.request.body);

      // 权限校验
      const adminPower = await ctx.helper.getAdminPower(ctx);
      if (!adminPower.includes('MANAGE_ORDER')) {
        ctx.helper.renderFail(ctx, {
          message: '无操作权限',
        });
        return;
      }

      // 设置创建人
      orderData.createBy = ctx.state.user.userName;

      // 创建订单
      const result = await service.manage.whOrderService.createOrder(orderData, orderDetails);

      // 记录操作日志
      ctx.auditLog('创建万华商城订单', `订单编号: ${result.orderNo}`, 'info');

      // 返回结果
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '创建订单成功',
      });
    } catch (err) {
      // 记录错误日志
      ctx.logger.error('创建订单失败:', err);

      // 返回错误信息
      ctx.helper.renderFail(ctx, {
        message: err.message || '创建订单失败',
      });
    }
  }

  /**
   * @summary 更新订单状态
   * @description 更新万华商城订单状态
   * @return {Promise<void>} 无返回值
   */
  async updateOrderStatus() {
    const { ctx, service } = this;
    try {
      // 获取订单ID和状态
      const { id, status } = ctx.request.body;

      // 参数校验
      ctx.validate({
        id: { type: 'string', required: true },
        status: { type: 'string', required: true },
      }, ctx.request.body);

      // 权限校验
      const adminPower = await ctx.helper.getAdminPower(ctx);
      if (!adminPower.includes('MANAGE_ORDER')) {
        ctx.helper.renderFail(ctx, {
          message: '无操作权限',
        });
        return;
      }

      // 更新订单状态
      const result = await service.manage.whOrderService.updateOrderStatus(id, status);

      // 记录操作日志
      ctx.auditLog('更新万华商城订单状态', `订单ID: ${id}, 状态: ${status}`, 'info');

      // 返回结果
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '更新订单状态成功',
      });
    } catch (err) {
      // 记录错误日志
      ctx.logger.error('更新订单状态失败:', err);

      // 返回错误信息
      ctx.helper.renderFail(ctx, {
        message: err.message || '更新订单状态失败',
      });
    }
  }

  /**
   * @summary 删除订单
   * @description 删除万华商城订单
   * @return {Promise<void>} 无返回值
   */
  async deleteOrder() {
    const { ctx, service } = this;
    try {
      // 获取订单ID
      const { id } = ctx.request.body;

      // 参数校验
      ctx.validate({
        id: { type: 'string', required: true },
      }, ctx.request.body);

      // 权限校验
      const adminPower = await ctx.helper.getAdminPower(ctx);
      if (!adminPower.includes('MANAGE_ORDER')) {
        ctx.helper.renderFail(ctx, {
          message: '无操作权限',
        });
        return;
      }

      // 删除订单
      await service.manage.whOrderService.deleteOrder(id);

      // 记录操作日志
      ctx.auditLog('删除万华商城订单', `订单ID: ${id}`, 'info');

      // 返回结果
      ctx.helper.renderSuccess(ctx, {
        message: '删除订单成功',
      });
    } catch (err) {
      // 记录错误日志
      ctx.logger.error('删除订单失败:', err);

      // 返回错误信息
      ctx.helper.renderFail(ctx, {
        message: err.message || '删除订单失败',
      });
    }
  }
}

module.exports = WhOrderController;
