# 工伤模型权限系统迁移说明

## 修改内容

### 1. 权限插件替换

**原来的实现**：
```javascript
const permissionPlugin = require('../utils/permission.js');

IndustrialInjurySchema.plugin(permissionPlugin, {
  ctx,
  fieldMapping: {
    gsPower: 'gsPower',
  },
});
```

**新的实现**：
```javascript
const { createWorkspacePermissionFilter } = require('../utils/workspacePermissionFilter.js');

IndustrialInjurySchema.plugin(createWorkspacePermissionFilter(ctx, {
  filterField: 'departmentId',  // 工伤表中的部门ID字段
  storageType: 'encode',        // 通过视图 encode 字段映射到 fullId 进行权限匹配
}));
```

### 2. 数据流向对比

**原来的权限流向**：
```
工伤表.departmentId → $lookup(flatMillConstructionMaterialized, encode) → gsPower(fullId) → 正则匹配权限
```

**新的权限流向**：
```
工伤表.departmentId → 视图.encode → 视图.fullId → millConstruction_ids 权限匹配
```

### 3. 字段映射说明

- **工伤表字段**：`departmentId` (Number 类型)
- **视图映射字段**：`FlatMillConstructionMaterialized.encode` (Number 类型)
- **权限匹配字段**：`FlatMillConstructionMaterialized.fullId` (String 类型)
- **权限数据源**：`policy.js` 中的 `millConstruction_ids`

## 优势

1. **统一权限逻辑**：与其他模型使用相同的权限过滤机制
2. **减少代码重复**：不再需要在 permission.js 中特殊处理 gsPower
3. **更好的维护性**：集中管理权限过滤逻辑
4. **递归检测**：自动防止权限过滤冲突
5. **错误处理**：权限检查失败时安全返回空结果

## 兼容性

- ✅ 使用相同的权限数据源（millConstruction_ids）
- ✅ 使用相同的视图映射关系（departmentId → encode）
- ✅ 保持相同的权限匹配逻辑（fullId 层级匹配）
- ✅ 支持超级管理员权限
- ✅ 支持权限缓存机制

## 使用方法

查询时需要传递用户信息：

```javascript
// 普通查询
const injuries = await ctx.model.WhIndustrialInjury.find(
  { state: '1' },
  null,
  { 
    userid: ctx.state.adminuser._id,  // 必须传递用户ID
    authCheck: true,                  // 启用权限检查
  }
);

// 聚合查询
const stats = await ctx.model.WhIndustrialInjury.aggregate([
  { $match: { state: '1' } },
  { $group: { _id: '$eventType', count: { $sum: 1 } } }
], {
  userid: ctx.state.adminuser._id,    // 必须传递用户ID
  authCheck: true,                    // 启用权限检查
});
```

## 注意事项

1. **用户ID传递**：所有查询都需要在 options 中传递 userid
2. **权限检查开关**：可通过 authCheck: false 临时禁用权限检查
3. **超级管理员**：超级管理员自动跳过权限限制
4. **错误处理**：权限检查失败时返回空结果，确保数据安全

## 测试建议

1. 测试普通用户的权限过滤是否正确
2. 测试超级管理员是否能看到所有数据
3. 测试聚合查询的权限过滤
4. 测试权限检查失败时的错误处理
5. 验证与现有业务逻辑的兼容性
