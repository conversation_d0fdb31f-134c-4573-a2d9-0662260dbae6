const Controller = require('egg').Controller;
const {
  siteFunc,
} = require('@utils');
const _ = require('lodash');

class AdminResourceController extends Controller {
  async listByPower() {
    const { ctx, service } = this;
    try {
      const payload = {
        isPaging: '0',
      };
      const manageCates = await service.adminResource.find(payload, {
        files: 'api _id label enable routePath parentId type icon comments',
      });
      // const userid = ctx.session.adminUserInfo._id;
      // ctx.helper.setCache(`userid:${userid}`, userid);
      // const isSuperAdmin = await ctx.helper.getIsSuperAdmin(userid);
      // ctx.helper.setPolicyCache(`superAdmin:${userid}`, isSuperAdmin);
      // if (isSuperAdmin) {
      //   const superEnterprise_ids = await ctx.helper.getSuperAdminEnterpriseIds();
      //   ctx.helper.setPolicyCache(`superEnterprise_ids:${userid}`, superEnterprise_ids);
      //   const adminPower = await ctx.helper.getAdminPower(ctx);
      //   const currentCates = await siteFunc.renderNoPowerMenus(
      //     manageCates,
      //     adminPower,
      //     ctx.session.basePath
      //   );
      //   ctx.helper.renderSuccess(ctx, {
      //     data: currentCates,
      //   });
      //   return;
      // }
      // const adminPower = await ctx.helper.getPolicyAdminPower(ctx);
      // const { powerIds, enterprise_ids, dingtree_ids, millConstruction_ids } =
      //     adminPower;
      // ctx.helper.setPolicyCache(`powerIds:${userid}`, powerIds);
      // ctx.helper.setPolicyCache(`enterprise_ids:${userid}`, enterprise_ids);
      // ctx.helper.setPolicyCache(`dingtree_ids:${userid}`, dingtree_ids);
      // ctx.helper.setPolicyCache(`millConstruction_ids:${userid}`, millConstruction_ids);
      // }
      // const adminPower = await ctx.helper.getPolicyAdminPower(ctx);
      // const { powerIds, enterprise_ids, dingtree_ids, millConstruction_ids } =
      //   adminPower;
      // 检索代号：#0001  ctx.session.basePath 后台根路径
      // await ctx.helper.getUserResourceList();
      const powerIds = await ctx.helper.getScopeData('powerIds');
      const currentCates = await siteFunc.renderNoPowerMenus(
        manageCates,
        powerIds,
        ctx.session.basePath
      );

      ctx.helper.renderSuccess(ctx, {
        data: currentCates,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  async list() {
    const { ctx, service } = this;
    try {
      const payload = ctx.query;
      _.assign(payload, {
        isPaging: '0',
      });
      const adminResourceList = await service.adminResource.find(payload);

      ctx.helper.renderSuccess(ctx, {
        data: adminResourceList,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  // 获取特殊企业项目是否互相可见开关
  async getProjectVisibilityEnabled() {
    const { ctx } = this;
    try {
      const { isProjectVisibilityEnabled = '0' } = ctx.app.config;
      ctx.helper.renderSuccess(ctx, {
        data: isProjectVisibilityEnabled === '1',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  // 获取发放标准选项可见开关
  async getRadioButtonEnabled() {
    const { ctx } = this;
    try {
      const { isRadioButtonEnabled = '0', distributionStandard = 'depart' } =
        ctx.app.config;
      ctx.helper.renderSuccess(ctx, {
        data: {
          isRadioButtonEnabled,
          distributionStandard,
        },
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }
}

module.exports = AdminResourceController;
