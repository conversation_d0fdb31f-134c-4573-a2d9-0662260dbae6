// const {
//     authToken,
//   } = require('@utils');
// const _ = require('lodash');
module.exports = () => {

  // const routeWhiteList = [
  //   'routeWhiteList',
  // ];
  return async function authCompanyUser(ctx, next) {
    ctx.request.body;
    ctx.session.adminUserInfo;
    // console.log('企业用户中间件', ctx.session.adminUserInfo, routeWhiteList, ctx.request.origin, ctx.request.originalUrl, ctx.request.path, ctx.request.query);
    await next();
  };

};
