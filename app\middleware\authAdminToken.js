const {
  authToken,
} = require('@utils');
const _ = require('lodash');
module.exports = (options, app) => {

  const routeWhiteList = [
    '/admin/login',
    '/admin/dataBigScreenAuth',
    '/admin/reg',
    '/dr-admin',
    '/api/address/list',
    '/manage/getAssessmentInfo',
    '/manage/emptyRequest',
    '/manage/fileRecord',
    'survey',
    '/api/testDataPermission',
  ];

  return async function authAdminToken(ctx, next) {
    const url = ctx.request.url;
    ctx.session.adminUserInfo = '';
    let userToken = '';
    const getTokenFromCookie = ctx.cookies.get('admin_' + app.config.auth_cookie_name);
    if (ctx.request.method === 'GET') {
      userToken = ctx.query.token || getTokenFromCookie;
    } else if (ctx.request.method === 'POST') {
      userToken = ctx.request.body.token || getTokenFromCookie;
    } else if (ctx.request.method === 'DELETE') {
      userToken = ctx.request.body.token || getTokenFromCookie;
    } else if (ctx.request.method === 'PUT') {
      userToken = ctx.request.body.token || getTokenFromCookie;
    }

    if (userToken) {
      if (url === '/manage/emptyRequest') {
        // 更新cookies时间
        ctx.cookies.set('admin_' + app.config.auth_cookie_name, userToken, {
          path: '/',
          maxAge: app.config.superUserMaxAge,
          signed: true,
          httpOnly: false,
        });
      }
      const checkToken = await authToken.checkToken(userToken, app.config.encrypt_key);

      if (checkToken) {
        if (typeof checkToken === 'object') {
          const _id = checkToken._id,
            EnterpriseID = checkToken.EnterpriseID;

          const targetUser = await ctx.service.adminUser.item(ctx, {
            query: {
              _id,
            },
            populate: 'none',
            files: {
              // password: 0,
              email: 0,
            },
          });

          if (targetUser && !targetUser.enable) {
            ctx.session.adminUserInfo = '';
            ctx.cookies.set('admin_' + app.config.auth_cookie_name, '', { signed: false, maxAge: 0 });
            ctx.redirect('/admin/login');
            return false;
          }


          const getEnterpriseIDFromCookie = ctx.cookies.get('EnterpriseID', {
            httpOnly: true, // 默认就是 true
            encrypt: true, // 加密传输
          });

          if (getEnterpriseIDFromCookie) {
            ctx.cookies.set('EnterpriseID', '', { signed: false, maxAge: 0 });
            ctx.cookies.set('admin_' + app.config.auth_cookie_name, '', { signed: false, maxAge: 0 });
            ctx.session.adminUserInfo = '';
            ctx.redirect('/admin/login');
            return false;
          }


          const targetOrg = await ctx.service.adminorg.item(ctx, {
            query: {
              _id: EnterpriseID,
            },
            populate: 'none',
            files: {
              PID: 0,
              // isactive: 0,
              // cname: 0,
              code: 0,
              regAdd: 0,
              workAdd: 0,
              corp: 0,
              industryCategory: 0,
              licensePic: 0,
              adminUserId: 0,
              createTime: 0,
              updateTime: 0,
              message: 0,
            },
          });

          if (!_.isEmpty(targetUser)) {

            const {
              userName,
              name,
              _id,
              group,
              phoneNum,
            } = targetUser;
            const topEnterpriseID = await ctx.helper.getTopEnterpriseId(EnterpriseID);

            ctx.session.adminUserInfo = {
              userName,
              name,
              _id,
              group,
              phoneNum,
              isTrialUser: checkToken.isTrialUser,
              topEnterpriseID,
              EnterpriseID,
            };
            if (app.config.branch === 'by') {
              ctx.session.adminUserInfo.byToken = checkToken.byToken;
            }
            // console.log('密码有效期功能启用：', app.config.passwordValidityPeriod, '密码有效期：', app.config.passwordExpiresIn);
            // console.log('当前用户的密码有效初始时间：', targetUser.passwordExpiresAt);
            if (app.config.passwordValidityPeriod && targetUser.passwordExpiresAt) {
              if ((targetUser.passwordExpiresAt.getTime() + app.config.passwordExpiresIn) < new Date().getTime() && !ctx.session.redirected) {
                console.log('密码已过期，请重置密码');
                ctx.session.redirected = true; // 设置会话中的标识为true
                ctx.redirect('/admin/dashboard?isSetting=true');
              } else {
                // 删除redirected标识
                delete ctx.session.redirected;
              }
            }
            if (targetOrg && targetOrg._id) {
              if (targetOrg.isactive === '1') {
                ctx.session.adminUserInfo.EnterpriseID = targetOrg._id;
                ctx.session.adminUserInfo.payCategoryID = targetOrg.payCategoryID;
              } else if (targetOrg.isactive === '4' && targetOrg.experienceToDate) {
                if (new Date().getTime() < new Date(targetOrg.experienceToDate).getTime()) {
                  ctx.session.adminUserInfo.EnterpriseID = targetOrg._id;
                  ctx.session.adminUserInfo.experienceToDate = targetOrg.experienceToDate;
                } else {
                  // 体验账号到期了
                  ctx.cookies.set('admin_' + app.config.auth_cookie_name, null);
                  ctx.cookies.set('admin_doracmsapi', null);
                  ctx.session.adminUserInfo = '';
                  ctx.redirect('/');
                }
              } else { // 非体验账号和已通过情况下重定向
                if (app.config.branch === 'hz') {
                  ctx.auditLog('非体验账号和已通过情况下重定向', JSON.stringify(targetOrg), 'error');
                } else {
                  ctx.redirect('/enterprise/orgapply');
                }
              }
            } else {
              if (app.config.branch === 'hz') {
                ctx.auditLog('全局捕获的错误', '找不到checkToken中的EnterpriseID', 'error');
              } else {
                ctx.redirect('/enterprise/orgapply');
              }
            }

            // if ((_.isEmpty(targetUser.phoneNum) || _.isEmpty(targetUser.IDcard)) && !(ctx.originalUrl === '/admin/dashboard?isSetting=true')) {
            //   ctx.redirect('/admin/dashboard?isSetting=true');
            // }

            // 检索代号：#0001
            // 根据url动态设置后台根路径 可以用其他的，譬如jwt，这里临时使用session，ctx.state 无效
            if (url.indexOf('/admin') === 0) {
              ctx.session.basePath = app.config.admin_base_path;
            } else if (url.indexOf('/qy') === 0) {
              ctx.session.basePath = app.config.qy_base_path;
            } else if (url.indexOf('/user') === 0) {
              ctx.session.basePath = app.config.user_base_path;
            }

            await next();
          } else {
            if (url.indexOf('/admin') === 0) {
              ctx.redirect(`${app.config.admin_base_path}/login`);
            } else if (url.indexOf('/qy') === 0) {
              ctx.redirect(`${app.config.qy_base_path}/login`);
            } else if (url.indexOf('/user') === 0) {
              ctx.redirect(`${app.config.user_base_path}/login`);
            }
            // ctx.redirect('/admin/login');
          }
        } else {
          if (url.indexOf('/admin') === 0) {
            ctx.redirect(`${app.config.admin_base_path}/login`);
          } else if (url.indexOf('/qy') === 0) {
            ctx.redirect(`${app.config.qy_base_path}/login`);
          } else if (url.indexOf('/user') === 0) {
            ctx.redirect(`${app.config.user_base_path}/login`);
          }
          // ctx.redirect('/admin/login');
        }

      } else {
        if (url.indexOf('/admin') === 0) {
          ctx.redirect(`${app.config.admin_base_path}/login`);
        } else if (url.indexOf('/qy') === 0) {
          ctx.redirect(`${app.config.qy_base_path}/login`);
        } else if (url.indexOf('/user') === 0) {
          ctx.redirect(`${app.config.user_base_path}/login`);
        }

      }
    } else {
      // 没有配置但是包含在白名单内的路由校验
      if (!_.isEmpty(routeWhiteList)) {
        const checkWhiteRouter = _.filter(routeWhiteList, item => {
          return ctx.originalUrl.indexOf(item) >= 0;
        });
        if (!_.isEmpty(checkWhiteRouter)) {
          await next();
        } else {
          if (url.indexOf('/admin') === 0) {
            ctx.redirect(`${app.config.admin_base_path}/login`);
          } else if (url.indexOf('/qy') === 0) {
            ctx.redirect(`${app.config.qy_base_path}/login`);
          } else if (url.indexOf('/user') === 0) {
            ctx.redirect(`${app.config.user_base_path}/login`);
          }
        }
      } else {
        if (url.indexOf('/admin') === 0) {
          ctx.redirect(`${app.config.admin_base_path}/login`);
        } else if (url.indexOf('/qy') === 0) {
          ctx.redirect(`${app.config.qy_base_path}/login`);
        } else if (url.indexOf('/user') === 0) {
          ctx.redirect(`${app.config.user_base_path}/login`);
        }

      }

    }

  };

};
