const Controller = require('egg').Controller;
const moment = require('moment');
const { isArray } = require('lodash');
const jwt = require('jsonwebtoken');
// const crypto = require('crypto'); // node内置的加密模块

const {
  siteFunc,
} = require('@utils');
const fs = require('fs');
const path = require('path');
const _ = require('lodash');

class AdminUserController extends Controller {
  async logOutAction() {
    const ctx = this.ctx;
    ctx.session = null;
    ctx.cookies.set('admin_' + this.app.config.auth_cookie_name, null);
    ctx.cookies.set('admin_doracmsapi', null);
    ctx.helper.renderSuccess(ctx);
  }

  async getUserSession() {
    const {
      ctx,
      app,
      service,
    } = this;
    try {
      let noticeCounts = 0;
      if (!_.isEmpty(service.systemNotify)) {
        noticeCounts = await service.systemNotify.count({
          systemUser: ctx.session.adminUserInfo._id,
          // 'systemUser': '4JiWCMhzg',
          isRead: false,
        });
      }
      const options = {
        authCheck: false,
      };

      const adminUserInfo = await service.adminUser.item(ctx, {
        query: {
          _id: ctx.session.adminUserInfo._id,
        },
        populate: [{
          path: 'group',
          select: 'power _id enable name',
        },
        {
          path: 'employees',
          select: 'name _id',
        },
        ],
        // files: 'enable password _id email userName logo phoneNum name IDcard',
        files: 'enable _id email userName logo phoneNum name IDcard',
      }, options);
      const trialUserId = ctx.session.adminUserInfo.isTrialUser;
      // const trialUser = trialUserId ? await ctx.model.TrialAccount.findOne({ _id: trialUserId, enable: true }, { name: 1, effectiveDate: 1 }) : null;
      const trialUser = trialUserId ? await ctx.service.db.findOne('TrialAccount', { _id: trialUserId, enable: true }, { name: 1, effectiveDate: 1 }) : null;
      const butler = await service.butlerProjects.getProjectList(
        ctx.session.adminUserInfo.EnterpriseID, '', 1, 1
      );
      const redirected = ctx.session.redirected;
      const renderData = {
        redirected,
        noticeCounts,
        loginState: true,
        userInfo: adminUserInfo,
        trialUser,
        experienceToDate: ctx.session.adminUserInfo.experienceToDate, // 体验 有效期至
        butlerStatus: !!(butler && butler.totalItems),
        logoutCallbackUrl: this.app.config.logoutCallbackUrl || '', // 退出登录后的回调地址
        allowLogin: this.app.config.allowLogin, // 是否允许登录
        title: app.config.platformName,
        branch: app.config.branch || 'master',
        logoShow: app.config.platformLogoShow,
      };

      ctx.helper.renderSuccess(ctx, {
        data: renderData,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  async dashboard() {
    const { ctx, service, config } = this;
    // console.log(
    //   '\r\n\r\n----------/controller/manage/adminUser.js/dashboard---------------'
    // );
    const renderMap = [];
    const payload = {
      isPaging: '0',
    };
    // let manageCates = ctx.helper.getCache('manageCates');
    // if (!manageCates) {
    //   manageCates = await service.adminResource.find(payload, {
    //     files: 'api _id label enable routePath parentId type icon comments',
    //   });
    //   // manageCates = JSON.parse(JSON.stringify(manageCates));
    //   ctx.helper.setPolicyCache('manageCates', manageCates);
    // }
    const manageCates = await service.adminResource.find(payload, {
      files: 'api _id label enable routePath parentId type icon comments',
    });
    // let powerIds = ctx.helper.getCache('powerIds');
    // if (!powerIds) {
    // let currentCates;
    // const basePath = ctx.session.basePath;
    // const userid = ctx.session.adminUserInfo._id;
    let powerIds = null;
    // const isExistUserId = await ctx.helper.getScopeData('userid');
    try {
      await ctx.helper.clearUserPolicyCache();
      powerIds = await ctx.helper.getUserResourceList();
    } catch (error) {
      console.log('error:', error);
    }
    // if (!isExistUserId) {
    //   await ctx.helper.clearUserPolicyCache();
    //   powerIds = await ctx.helper.getUserResourceList();
    // }
    // powerIds = await ctx.helper.getScopeData('powerIds');
    // const isSuperAdmin = ctx.helper.getCache(`superAdmin:${userid}`);
    // if (isSuperAdmin) {
    //   const adminPower = await ctx.helper.getAdminPower(ctx);
    //   currentCates = await siteFunc.renderNoPowerMenus(
    //     manageCates,
    //     adminPower,
    //     basePath,
    //     false
    //   );
    // } else {
    // const adminPower = await ctx.helper.getPolicyAdminPower(ctx);
    // const { powerIds, enterprise_ids, dingtree_ids, millConstruction_ids } =
    //   adminPower;
    // ctx.helper.setPolicyCache('powerIds', powerIds);
    // ctx.helper.setPolicyCache('enterprise_ids', enterprise_ids);
    // ctx.helper.setPolicyCache('dingtree_ids', dingtree_ids);
    // ctx.helper.setPolicyCache('millConstruction_ids', millConstruction_ids);
    // }
    // console.log('manageCates:', manageCates.length);
    // 关于鉴权，此处查找登录用户的权限信息 权限为一对多时，可用这种稍加修改
    // const adminPower = await ctx.helper.getAdminPower(ctx);
    // console.log('adminPower:', adminPower.length);

    // 得到登录用户最终拥有的导航栏数据
    // 检索代号：#0001  ctx.session.basePath 后台根路径
    const basePath = ctx.session.basePath;
    const currentCates = await siteFunc.renderNoPowerMenus(
      manageCates,
      powerIds,
      basePath,
      false
    );
    // }
    // console.log('currentCates:', currentCates.length);

    if (!_.isEmpty(currentCates)) {
      const powerPathMaps = [];
      // 过滤大类菜单的routePath
      for (const cateItem of currentCates) {
        if (cateItem.parentId !== 0 && cateItem.enable) {
          powerPathMaps.push(cateItem.routePath);
        }
      }
      // console.log('powerPathMaps:', powerPathMaps.length);

      // 追加navbar到第一位
      powerPathMaps.splice(0, 0, 'dashboard');
      powerPathMaps.splice(0, 0, 'navbar');

      let params = '';
      const isAutoUpdate = config.isAutoUpdate,
        isVersionUpdate = config.isVersionUpdate;
      for (const pathItem of powerPathMaps) {
        if (isVersionUpdate) {
          params = `?ver=CHINA${config.version}`;
        } else if (isAutoUpdate) {
          const date = new Date();
          params = `?ver=C${date.getSeconds()}HI${Math.random()
            .toString(36)
            .substr(2)}N${date.getMilliseconds()}A`;
        }
        // 注意：一定要将环境变量NODE_ENV设置为development，否则将无法渲染
        if (config.env === 'local') {
          // 读取本地文件获取调试端口号
          // 注意：backstage里的各Vue项目文件夹的名字一定要与数据库adminresources表的routePath一致，否则将无法获取该Vue项目
          // baseDir: E:\demo\temp\frame
          const admin_micro_path = path.join(config.baseDir, 'backstage');
          const modulePkgPath = `${admin_micro_path}/${pathItem}/package.json`;

          if (fs.existsSync(modulePkgPath)) {
            const modulePkg = require(modulePkgPath);
            const moduleDevInfo = modulePkg.scripts.serve;
            const modulePort = moduleDevInfo.split(' --port ')[1];

            //   /config/config.local.js/dev_modules
            if (config.dev_modules.indexOf(pathItem) >= 0) {
              // 要热开发模式下的Vue项目
              renderMap.push({
                name: pathItem,
                path: `${config.admin_root_path}:${modulePort}/app.js`,
              });
            } else {
              renderMap.push({
                name: pathItem,
                path: `${
                  config.server_path + config.static.prefix
                }/${pathItem}/js/app.js${params}`,
              });
            }
            // console.log('lcoal renderMap:', renderMap.length);
          } else {
            // 警告：未读取到指定Vue项目文件夹，可能由于目标Vue项目文件夹名称与数据库adminresources表的routePath未保持一致，由于未知原因页面无法渲染
            // console.log(`\r\n警告：未读取到指定Vue项目文件夹：${pathItem}，可能由于目标Vue项目文件夹名称与数据库adminresources表的routePath未保持一致`);
            renderMap.push({
              name: pathItem,
              // path: `${config.origin + '/cms/plugins' + config.static.prefix}/admin/${pathItem}/js/app.js`,
              path: `${
                config.origin + '/cms/plugins' + config.static.prefix
              }${basePath}/${pathItem}/js/app.js${params}`,
            });
            // console.log('local renderMap:', renderMap.length);
          }
        } else {
          // 警告：环境变量NODE_ENV在非development模式下进入此
          // console.log('\r\n警告：环境变量NODE_ENV在非development模式下进入此，相关问题可看代码');
          // // 已安装的插件优先级最高，该框架暂时无用
          // let {
          //   plugins
          // } = this.app.getExtendApiList();

          // let pluginStr = `dora${pathItem.charAt(0).toUpperCase() + pathItem.slice(1)}`;

          // if (plugins.indexOf(pluginStr) >= 0 && config[pluginStr].adminUrl) {
          //   let adminUrlItem = config.admin_root_path + config[pluginStr].adminUrl;
          //   if (adminUrlItem instanceof Array) {
          //     for (const routerItem of adminUrlItem) {
          //       renderMap.push({
          //         name: routerItem.path,
          //         path: routerItem.url
          //       })
          //     }
          //   } else {
          //     renderMap.push({
          //       name: pathItem,
          //       path: adminUrlItem
          //     })
          //   }
          // } else {
          renderMap.push({
            name: pathItem,
            path: `${config.admin_root_path}/${pathItem}/js/app.js${params}`,
          });

          // console.log('prd renderMap:', renderMap.length);

          // }
        }
      }
    }
    ctx.auditLog('登录成功', '当前用户登录成功。');
    await ctx.render('manage/index.html', {
      renderMap,
      renderMapJson: JSON.stringify(renderMap),
      staticRootPath: config.static.prefix,
      siteSeo: this.app.config.siteSeo,
      adminBasePath: basePath,
      appVersion: config.pkg.version,
      appName: config.pkg.name,
      branch: config.branch,
    });
  }
  async dataBigScreenAuth() {
    const { ctx, config } = this;
    const adminuserInfo = await ctx.service.db.findOne('AdminUser', {
      $or: [
        { unitCode: config.bigDataScreenUser || '' },
        { userName: config.bigDataScreenUser || '' },
      ],
    }, {}, { authCheck: false });
    if (!adminuserInfo) {
      return await ctx.render('manage/error.html', {
        status: 401,
        message: '无权限访问',
      });
    }
    const adminUserToken = jwt.sign(
      {
        _id: adminuserInfo._id,
        EnterpriseID: adminuserInfo.newAddEnterpriseID,
      },
      this.app.config.encrypt_key,
      {
        expiresIn: '30day',
      }
    );
    // ctx.cookies.set(
    //   'admin_' + this.app.config.auth_cookie_name,
    //   adminUserToken,
    //   {
    //     path: '/',
    //     maxAge: 1000 * 60 * 60 * 24 * 30,
    //     signed: true,
    //     httpOnly: false,
    //   }
    // );
    ctx.redirect('/admin/dataBigScreen?token=' + adminUserToken);
  }
  async getBasicSiteInfo() {
    const {
      ctx,
      service,
      // app,
    } = this;
    try {
      // const {
      //   plugins,
      // } = app.getExtendApiList();

      let adminUserCount = 0,
        regUserCount = 0,
        contentCount = 0,
        messageCount = 0,
        messages = [],
        regUsers = [];
      //  loginLogs = [];

      adminUserCount = await service.adminUser.count({
        state: '1',
      });

      if (!_.isEmpty(ctx.service.user)) {
        regUserCount = await service.user.count({
          state: '1',
        });

        regUsers = await service.user.find({
          isPaging: '0',
          pageSize: 10,
        }, {
          files: {
            email: 0,
          },
        });
      }

      if (!_.isEmpty(service.content)) {
        contentCount = await service.content.count({
          state: '2',
        });
      }
      if (!_.isEmpty(service.message)) {
        messageCount = await service.message.count();
        messages = await service.message.find({
          isPaging: '0',
          pageSize: 8,
        }, {
          populate: [{
            path: 'contentId',
            select: 'stitle _id',
          },
          {
            path: 'author',
            select: 'userName _id enable date logo',
          },
          {
            path: 'replyAuthor',
            select: 'userName _id enable date logo',
          },
          {
            path: 'adminAuthor',
            select: 'userName _id enable date logo',
          },
          {
            path: 'adminReplyAuthor',
            select: 'userName _id enable date logo',
          },
          ],
        });
      }

      // const reKey = new RegExp(ctx.session.adminUserInfo.userName, 'i');
      // console.log('sdsddsxxxxxxxxxxccccccccccc')
      // console.log(reKey)

      // loginLogs = [];
      // // TODO 作为插件需要优先判断是否存在
      // if (!_.isEmpty(service.systemOptionLog)) {
      //   loginLogs = await service.systemOptionLog.find(
      //     {
      //       isPaging: '0',
      //       pageSize: 1,
      //     },
      //     {
      //       query: {
      //         type: 'login',
      //         logs: {
      //           $regex: reKey,
      //         },
      //       },
      //     }
      //   );
      // }

      // 权限标记  首页我的权限
      const fullResources = await service.adminResource.find({
        isPaging: '0',
      });
      const newResources = [];
      for (let i = 0; i < fullResources.length; i++) {
        const resourceObj = JSON.parse(JSON.stringify(fullResources[i]));
        if (resourceObj.type === '1' && !_.isEmpty(ctx.session.adminUserInfo)) {
          const adminPower = await ctx.helper.getAdminPower(ctx);
          if (adminPower && adminPower.indexOf(resourceObj._id) > -1) {
            resourceObj.hasPower = true;
          } else {
            resourceObj.hasPower = false;
          }
          newResources.push(resourceObj);
        } else {
          newResources.push(resourceObj);
        }
      }

      const renderBasicInfo = {
        adminUserCount,
        regUserCount,
        regUsers,
        contentCount,
        messageCount,
        messages,
        // loginLogs,
        resources: newResources,
      };

      ctx.helper.renderSuccess(ctx, {
        data: renderBasicInfo,
      });
    } catch (err) {
      console.log(err);
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }
  async getBasicMessage() {
    const data = {};
    const {
      ctx,
      service,
    } = this;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';

    const basicData = [ 'Employee', 'Roles', 'MillConstruction' ];
    const normalData = [ 'Ledger', 'AnnualPlan', 'Propagate' ];
    const projectData = [ 'Facility', 'Defendproducts', 'DiseasesOverhaul' ];
    const specialData = [ 'WarnNotice', 'SanitaryInspection' ];
    const promises = [];
    for (let i = 0; i < basicData.length; i++) {
      try {
        const data = await service.adminUser.getData({
          collection: basicData[i],
          EnterpriseID,
          type: 'basicData',
        });
        // console.log(222222222, data);
        if (data && data > 0) {
          promises.push({
            name: basicData[i],
            complete: true,
          });
        } else {
          promises.push({
            name: basicData[i],
            complete: false,
          });
        }
      } catch (error) {
        promises.push({
          name: basicData[i],
          complete: false,
        });
      }
    }
    for (let i = 0; i < normalData.length; i++) {
      try {
        const data = await service.adminUser.getData({
          collection: normalData[i],
          EnterpriseID,
          year: ctx.query.year,
          type: '',
        });
        if (data && data > 0) {
          promises.push({
            name: normalData[i],
            complete: true,
          });
        } else {
          promises.push({
            name: normalData[i],
            complete: false,
          });
        }
      } catch (error) {
        promises.push({
          name: normalData[i],
          complete: false,
        });
      }
    }
    for (let i = 0; i < projectData.length; i++) {
      try {
        const data = await service.adminUser.getData({
          collection: projectData[i],
          EnterpriseID,
          year: ctx.query.year,
          type: projectData[i],
        });
        if (data && data > 0) {
          promises.push({
            name: projectData[i],
            complete: true,
          });
        } else {
          promises.push({
            name: projectData[i],
            complete: false,
          });
        }
      } catch (error) {
        promises.push({
          name: normalData[i],
          complete: false,
        });
      }
    }
    for (let i = 0; i < specialData.length; i++) {
      try {
        const data = await service.adminUser.getSpecialData({
          collection: specialData[i],
          EnterpriseID,
          year: ctx.query.year,
          type: specialData[i],
        });
        if (data && data > 0) {
          promises.push({
            name: specialData[i],
            complete: true,
          });
        } else {
          promises.push({
            name: specialData[i],
            complete: false,
          });
        }
      } catch (error) {
        promises.push({
          name: normalData[i],
          complete: false,
        });
      }
    }

    const calTime = function(time) {
      const nowTime = moment();
      let completeStatusRes = 0;
      let messageRes = '未完成';
      const monthD = moment(time);
      const diffTime = nowTime.diff(monthD, 'months', true);
      if (diffTime <= 10) {
        completeStatusRes = 2;
        messageRes = '已完成';
      } else if (diffTime <= 12 && diffTime > 10) {
        completeStatusRes = 1;
        messageRes = '将到期';
      } else {
        completeStatusRes = -1;
        messageRes = '已过期';
      }
      return {
        completeStatusRes,
        messageRes,
      };
    };
    const handleNextYear = function(time) {
      const year = moment(time).format('YYYY');
      const month = moment(time).format('MM');
      const day = moment(time).format('DD');
      const newData = (parseInt(year) + 1) + '-' + month + '-' + day;
      return moment(newData).format('YYYY-MM-DD');
    };
    // 健康风险管理
    const jkfxglData = await ctx.service.adminUser.findJkfxgl(EnterpriseID);

    const jkfxglArr = [
      { name: 'OnlineDeclaration', field: 'onlineDeclarationFiles', field2: 'monthD' },
      { name: 'Healthcheck', field: 'healcheckInfo', field2: 'recentDay' },
      { name: 'JobHealth', field: 'reportTime' },
    ];

    jkfxglArr.forEach(item => {
      const time = item.field2 ? jkfxglData[item.field][item.field2] : jkfxglData[item.field];
      if (time) {
        const { completeStatusRes, messageRes } = calTime(time);
        let message = messageRes;
        if (completeStatusRes === -1 || completeStatusRes === 1) {
          message = messageRes + ' (' + handleNextYear(time) + ')';
        }
        promises.unshift({
          name: item.name,
          complete: completeStatusRes,
          message,
          checkTime: time,
        });
      } else {
        promises.unshift({
          name: item.name,
          complete: 0,
          message: '未完成',
          checkTime: time,
        });
      }
    });

    data.basicMessage = promises;
    // console.log(**********, promises);
    ctx.helper.renderSuccess(ctx, {
      data,
    });
  }
  async getMainData() {
    const {
      ctx,
      service,
    } = this;
    // const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';

    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    // const companyArr = await ctx.service.employee.findSubCompany(EnterpriseID);
    const companyArr = await ctx.helper.getScopeData('enterprise_ids');
    const personNum = await service.adminUser.getPersonNum({
      EnterpriseID,
    });

    const allPersion = await service.adminUser.getAllPersion({
      EnterpriseID,
    });
    const laborDispatchingPersion = await service.adminUser.getlaborDispatchingPersion({ EnterpriseID });
    let healthData = {};
    if (companyArr && companyArr.length !== 0) {
      const aggregatedHealthData = {
        // actuallNum: [ 0, 0, 0, 0 ],
        re_examination: [ 0, 0, 0, 0 ],
        normal: [ 0, 0, 0, 0 ],
        suspected: [ 0, 0, 0, 0 ],
        forbid: [ 0, 0, 0, 0 ],
        otherDisease: [ 0, 0, 0, 0 ],
        // years: [ '2021年', '2022年', '2023年', '2024年' ],
      };

      // for (let i = 0; i < companyArr.length; i++) {
      //   const EnterpriseID = companyArr[i];
      //   const healthData = await service.adminUser.getHealthData({ EnterpriseID });
      // console.log('healthData:', healthData);

      // aggregatedHealthData.actuallNum = aggregatedHealthData.actuallNum.map((num, index) => num + healthData.actuallNum[index]);
      //   aggregatedHealthData.re_examination = aggregatedHealthData.re_examination.map((num, index) => num + healthData.re_examination[index]);
      //   aggregatedHealthData.normal = aggregatedHealthData.normal.map((num, index) => num + healthData.normal[index]);
      //   aggregatedHealthData.suspected = aggregatedHealthData.suspected.map((num, index) => num + healthData.suspected[index]);
      //   aggregatedHealthData.forbid = aggregatedHealthData.forbid.map((num, index) => num + healthData.forbid[index]);
      //   aggregatedHealthData.otherDisease = aggregatedHealthData.otherDisease.map((num, index) => num + healthData.otherDisease[index]);
      // }
      const res = await service.adminUser.getHealthData({ enterpriseIds: companyArr });
      aggregatedHealthData.actuallNum = res.map(item => item.count);
      res.length && (aggregatedHealthData.years = res.map(item => item._id && item._id.year));
      healthData = aggregatedHealthData;
      // console.log('汇总的健康数据:', healthData);
    }

    const riskLevel = await service.adminUser.getRiskLevel({
      EnterpriseID,
    });
    // console.log('riskLevel:', riskLevel);
    const harmStatisticsArr = [].concat(...riskLevel.map(item => item.harmStatistics));
    // console.log('harmStatisticsArr:', harmStatisticsArr);

    const itemArr = [];
    const allArr = [];
    const resultArr = [];

    // 分离 'item' 和 'all'
    harmStatisticsArr.forEach(item => {
      if (item.sort === 'item') {
        itemArr.push(item);
      } else if (item.sort === 'all') {
        allArr.push(item);
      }
    });

    // 对 'all' 数组进行操作
    allArr.forEach(allItem => {
      allItem.count.forEach((countItem, index) => {
        if (!resultArr[index]) {
          resultArr[index] = { employee: [], value: 0 };
        }
        resultArr[index].employee = resultArr[index].employee.concat(countItem.employee);
        resultArr[index].value += countItem.value;
        resultArr[index].label = countItem.label;
        resultArr[index].harmFactors = countItem.harmFactors;
      });
    });

    // 将结果添加到 'item' 数组
    itemArr.push({ name: '总计', sort: 'all', count: resultArr });

    // console.log(itemArr);
    const harmStatistics = itemArr;
    // console.log('harmStatistics:', harmStatistics);
    const levels = await service.adminUser.getLevel({
      EnterpriseID,
    });
    const pipeline = [
      { $match: { EnterpriseID } },
      { $project: { employeeId: 1, name: 1, checkDate: 1 } },
      { $sort: { checkDate: -1 } },
    ];
    const suspectCheckDate = await ctx.service.db.aggregate('Suspect', pipeline, { authCheck: false });
    // console.log('体检时间选项:', suspectCheckDate);
    let yearTJOption = [];
    for (let i = 0; i < suspectCheckDate.length; i++) {
      const checkDate = new Date(suspectCheckDate[i].checkDate).getFullYear();
      yearTJOption.push(checkDate);
    }
    yearTJOption = _.uniq(yearTJOption);
    const yearss = yearTJOption;
    const { factors, physicalExamination, jobHealthCheckTime, adminInfo } = await service.adminUser.getMainData({ EnterpriseID });
    const data = {
      allPersion,
      healthData,
      personNum,
      factors,
      physicalExamination,
      levels,
      // harmStatistics: riskLevel.harmStatistics,
      harmStatistics,
      jobHealthCheckTime,
      adminInfo,
      yearss,
      laborDispatchingPersion,
    };
    // console.log('yearTJOption：', data);
    ctx.helper.renderSuccess(ctx, {
      data,
    });
  }

  async tjEcharts(ctx) {
    try {
      const year = ctx.query.year || '';
      let EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
      // const companyArr = await ctx.service.employee.findSubCompany(EnterpriseID);
      const companyArr = await ctx.helper.getScopeData('enterprise_ids');
      let tjRadio = 0; // 体检率
      const obj = {
        forbid: { value: [], name: '禁忌证', name1: '禁忌证', type: 'warning' }, // 禁忌症
        suspected: { value: [], name: '疑似', name1: '疑似职业病', type: 'danger' }, // 疑似职业病
        normal: { value: [], name: '未见异常', name1: '目前未见异常', type: 'success' }, // 目前未见异常
        otherDisease: { value: [], name: '其他', name1: '其他疾病或异常', type: 'info' }, // 其他疾病
        willRecheck: { value: [], name: '复查', name1: '复查', type: 'primary' }, // 需复查
      };
      const allShouldTjName = [];
      let timeOption = [];
      let allShouldTjNum = 0;
      const hadCheck = []; // 已体检
      const Expired = []; // 已过期
      const jdq = [];// 将到期
      let checkObj = {};
      if (companyArr && companyArr.length !== 0) {
        for (let i = 0; i < companyArr.length; i++) {
          EnterpriseID = companyArr[i];
          // console.log('EnterpriseID:', EnterpriseID);
          // 按年份分组，进行下面搜索，第一次展示的结果是取这个结果的第一个年份数据
          const pipelineSus = [
            { $match: {
              EnterpriseID,
            } },
            {
              $project: {
                year: 1,
                checkDate: 1,
              },
            },
            {
              $group: {
                _id: null,
                time: {
                  $push: {
                    year: '$year',
                    checkDate: '$checkDate',
                    name: '$name',
                  },
                },
              },
            },
            {
              $unwind:
                '$time',
            },
            {
              $sort: {
                'time.year': -1,
                'time.checkDate': -1,
              },
            },
          ];
          const suspects = await ctx.service.db.aggregate('Suspect', pipelineSus, { authCheck: false });
          // 展示再前端的体检echarts中的时间选项
          const pipelineTime = [
            { $match: {
              EnterpriseID,
            } },
            {
              $project: {
                year: 1,
                checkDate: 1,
              },
            },
            {
              $group: {
                _id: null,
                time: {
                  $push: {
                    year: '$year',
                    checkDate: '$checkDate',
                  },
                },
              } },
          ];
          const time = await ctx.service.db.aggregate('Suspect', pipelineTime, { authCheck: false });
          // console.log(JSON.stringify(time), '4444444');
          // 只有有危害因素才需要体检
          const pipelineEmployee = [
            { $match: { EnterpriseID } },
            { $unwind: '$children' },
            { $project: {
              workspace: {
                $cond: [
                  { $eq: [ '$category', 'mill' ] },
                  '$children.name',
                  '$name',
                ],
              },
              station: {
                $cond: [
                  { $eq: [ '$category', 'mill' ] },
                  '$children.children',
                  [ '$children' ],
                ],
              },
            } },
            { $unwind: '$station' },
            { $addFields: { customizeHarm: { $regexFindAll: { input: '$station.customizeHarm', regex: /[^,|，|、|\/]+/ } } } },
            { $addFields: {
              customizeHarm: {
                $map: {
                  input: '$customizeHarm',
                  as: 'item',
                  in: '$$item.match',
                },
              },
              harmFactors: {
                $map: {
                  input: '$station.harmFactors',
                  as: 'item',
                  in: { $arrayElemAt: [ '$$item', 1 ] },
                },
              },
            } },
            { $addFields: { harmFactors: { $concatArrays: [ '$customizeHarm', '$harmFactors' ] } } },
            { $match: { 'harmFactors.0': { $exists: true } } },
            { $unwind: '$station.children' },
            { $group: { _id: '$station.children.employees', stationInfo: { $push: { harmFactors: '$harmFactors', station: '$station.name', workspace: '$workspace' } } } },
            { $lookup: {
              from: 'employees', localField: '_id', foreignField: '_id', as: 'employee',
            } },
            { $addFields: { employee: { $arrayElemAt: [ '$employee', 0 ] } } },
            { $addFields: { status: '$employee.status', name: '$employee.name' } },
            {
              $lookup: {
                from: 'employeeStatusChanges', localField: '_id', foreignField: 'employee', as: 'employeeStatusChanges',
              } },
            { $addFields: { employeeStatusChange: { $arrayElemAt: [ '$employeeStatusChanges', 0 ] } } },
            {
              $match: {
                'employee.status': { $ne: 0 },
              },
            },
            { $project: { employeeStatusChanges: false } },
            { $lookup: {
              from: 'suspects',
              localField: '_id',
              foreignField: 'employeeId',
              as: 'suspects',
            } },
            {
              $project: {
                name: 1,
              },
            },
          // { $facet: {
          //   shouldCheck1: [ // 入职以来未做体检
          //     { $project: { employeeStatusChange: false } },
          //     { $match: { status: 1 } },
          //     { $match: { 'suspects.0': { $exists: false } } },
          //     { $addFields: {
          //       shouldReason: '新上岗',
          //     } },
          //   ],
          //   // 获取最近一次离岗
          //   lastOutCompany: [
          //     { $match: { status: 0 } },
          //     { $unwind: '$employeeStatusChange.statusChanges' },
          //     { $match: { 'employeeStatusChange.statusChanges.EnterpriseID': EnterpriseID, 'employeeStatusChange.statusChanges.changType': 1 } },
          //     { $sort: { 'employeeStatusChange.statusChanges.timestamp': -1 } }, // 按时间排序
          //     { $group: { _id: '$_id', firstOuts: { $push: { _id: '$employeeId', name: '$name', stationInfo: '$stationInfo', station: '$station', EnterpriseID: '$EnterpriseID', outTimestamp: '$employeeStatusChange.statusChanges.timestamp' } } } },
          //     { $addFields: { firstOut: { $arrayElemAt: [ '$firstOuts', 0 ] } } },
          //   ],
          //   lastChangeStation: [// 取最近一次转岗记录
          //     { $match: { status: 1 } },
          //     { $unwind: '$employeeStatusChange.statusChanges' },
          //     { $sort: { 'employeeStatusChange.statusChanges.timestamp': -1 } },
          //     { $match: { 'employeeStatusChange.statusChanges.EnterpriseID': EnterpriseID, 'employeeStatusChange.statusChanges.stationFrom': { $exists: true } } },
          //     { $match: { 'employeeStatusChange.statusChanges.stationsTo.0': { $exists: true } } },
          //     { $group: { _id: '$_id', info: { $push: { name: '$name', time: '$employeeStatusChange.statusChanges.timestamp', stationsTo: '$employeeStatusChange.statusChanges.stationsTo', stationFrom: '$employeeStatusChange.statusChanges.stationFrom' } } } },
          //     { $addFields: { firstChange: { $arrayElemAt: [ '$info', 0 ] } } },
          //   ],
          //   lastCheck: [// 取最近一次体检
          //     { $unwind: '$suspects' },
          //     { $project: { employeeStatusChange: false } },
          //     { $sort: { 'suspects.checkDate': -1 } }, // 按体检时间排序
          //     { $group: { _id: '$_id', suspects: { $push: { status: '$status', stationInfo: '$stationInfo', name: '$name', _id: '$_id', checkDate: '$suspects.checkDate', opinion: '$suspects.opinion', CwithO: '$suspects.CwithO', abnormalDes: '$suspects.abnormalDes' } } } }, // 增加异常描述字段
          //     { $addFields: { firstSuspects: { $arrayElemAt: [ '$suspects', 0 ] } } },
          //   ],
          // } },
          ];
          const employees = await ctx.service.db.aggregate('Employee', pipelineEmployee, { allowDiskUse: true, authCheck: false });
          // const obj = {
          //   forbid: { value: [], name: '禁忌证', name1: '禁忌证', type: 'warning' }, // 禁忌症
          //   suspected: { value: [], name: '疑似', name1: '疑似职业病', type: 'danger' }, // 疑似职业病
          //   normal: { value: [], name: '未见异常', name1: '目前未见异常', type: 'success' }, // 目前未见异常
          //   otherDisease: { value: [], name: '其他', name1: '其他疾病或异常', type: 'info' }, // 其他疾病
          //   willRecheck: { value: [], name: '复查', name1: '复查', type: 'primary' }, // 需复查
          // };
          if (suspects.length > 0) {
            // const hadCheck = []; // 已体检
            // const Expired = []; // 已过期
            // const jdq = [];// 将到期
            let year1 = '';
            if (year === '') {
              if (suspects[0].time && suspects[0].time.year) { // 有的数据是没有year字段的 则需要用checkdata字段进行判断时间
                year1 = suspects[0].time.year;
                year1 = year1.replace(/[^0-9]/ig, ''); // 数据库中有的年份存的带有’年‘有的没有，将‘年’去掉
              } else if (suspects[0].time && suspects[0].time.checkDate) {
                year1 = suspects[0].time.checkDate;
              }
            } else {
              year1 = year;
              year1 = year1.replace(/[^0-9]/ig, ''); // 数据库中有的年份存的带有’年‘有的没有，将‘年’去掉
            }
            const pipelinefirst = [
              { $match: {
                EnterpriseID,
                $or: [
                  { year: { $regex: year1.toString() } },
                  { checkDate: { $gte: new Date(year1.toString()) } },
                ],
              },
              },
              // {
              //   $match: { year: { $regex: year1 } },
              // },

            ];
            const firstTjData = await ctx.service.db.aggregate('Suspect', pipelinefirst, { authCheck: false });
            for (let i = 0; i < firstTjData.length; i++) {
              const item = firstTjData[i];
              // 将到期 已到期 过期 已体检数据
              if (new Date(item.checkDate).getTime() > new Date(new Date().setMonth(new Date().getMonth() - 12)).getTime() && new Date(item.checkDate).getTime() < new Date(new Date().setMonth(new Date().getMonth() - 10)).getTime()) {
                jdq.push({ value: item.name });
              } else if (new Date(item.checkDate).getTime() > new Date(new Date().setMonth(new Date().getMonth() - 24)).getTime() && new Date(item.checkDate).getTime() < new Date(new Date().setMonth(new Date().getMonth() - 12)).getTime()) {
                Expired.push({ value: item.name });
              } else if (new Date(item.checkDate).getTime() > new Date(new Date().setMonth(new Date().getMonth() - 12)).getTime() && new Date(item.checkDate).getTime() < new Date().getTime()) {
                hadCheck.push({ value: item.name });
              }

              if (item.CwithO === '其他疾病或异常') {
                obj.otherDisease.value.push({ name: item.name, _id: item._id });
              }
              if (item.CwithO === 'suspected') {
              // obj.suspected.value.push({ name: item.name, _id: item._id });
                if (item.handleResult === null) {
                  obj.suspected.value.push({ name: item.name, _id: item._id });
                }
              }
              if (item.CwithO === '疑似职业病') {
                if (item.handleResult === null) {
                  obj.suspected.value.push({ name: item.name, _id: item._id });
                }
              }
              if (item.CwithO === '禁忌证') {
                if (item.handleResult === null) { // 只有没有进行预警处置才统计首页相关人数
                  obj.forbid.value.push({ name: item.name, _id: item._id });
                }
              }
              // 如果复查结果为禁忌证，则增加首页禁忌证人数
              if (item.CwithO === '复查') {
                if (item.handleResult === '禁忌证' && item.secondaryHandleResult === null) {
                  obj.forbid.value.push({ name: item.name, _id: item._id });
                }
              }
              // 如果复查结果为疑似职业病，则增加首页疑似职业病人数
              if (item.CwithO === '复查') {
                if (item.handleResult === '疑似职业病' && item.secondaryHandleResult === null) {
                  obj.suspected.value.push({ name: item.name, _id: item._id });
                }
              }
              if (item.CwithO === '复查') {
              // obj.willRecheck.value.push(item.name);
                if (item.handleResult === null) {
                  obj.willRecheck.value.push({ name: item.name, _id: item._id });
                }
              }
              if (item.CwithO === '目前未见异常' || item.CwithO === '未见异常') {
                obj.normal.value.push({ name: item.name, _id: item._id });
              // obj.normal.value.push(item.name);
              }
            }
            allShouldTjNum = employees.length; // 所有应该体检的员工人员
            // const allShouldTjName = [];
            employees.forEach(item => {
              allShouldTjName.push(item.name);
            });
            // console.log(allShouldTjName, '222222222');
            tjRadio = Number(allShouldTjNum) !== 0 ? (firstTjData.length / allShouldTjNum).toFixed(2) : 0; // 员工体检率
            // let timeOption = [];
            if (time.length && time[0].time && time[0].time.length) {
              timeOption = _.uniqWith(time[0].time, _.isEqual);
              timeOption = _.sortBy(timeOption, function(item) {
                return -item.year; // 将返回前端的年份筛选进行逆序排序
              });
            }
          }
          checkObj = {
            allShouldTjNum, // 所有应该体检的员工人员
            jdq, // 将到期
            hadCheck, // 已体检
            Expired, // 已过期
            allShouldTjName, // 应该体检人员名字
          };
        }
        // console.log(JSON.stringify(obj), 'obj================');
        ctx.helper.renderSuccess(ctx, {
          data: {
            tjRadio, // 体检率
            obj, // 第一次展示的echarts体检数据
            timeOption, // 展示再echarts中的时间选项
            checkObj, // 将到期已到期已体检
          },
          message: '获取成功',
        });
      } else {

        ctx.helper.renderSuccess(ctx, {
          data: {},
          message: '暂无数据',
        });
      }

    } catch (err) {
      console.log(err, '44442');
      ctx.helper.renderFail(ctx, {
        message: err,
        data: [],
      });
    }
  }

  async tjConclusion(ctx) {
    try {
      const year = ctx.query.year || new Date().getFullYear();
      // let EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
      const enterpriseIds = await ctx.helper.getScopeData('enterprise_ids');

      const conclusionRes = await ctx.service.db.aggregate(
        'Suspect',
        [
          {
            $match: {
              EnterpriseID: { $in: enterpriseIds },
              checkDate: { $gte: new Date(year.toString()), $lt: new Date((parseInt(year) + 1).toString()) },
              CwithO: { $in: [ '其他疾病或异常', '疑似职业病', '禁忌证', '复查', '未见异常', '目前未见异常' ] },
            },
          },
          {
            $group: {
              _id: '$CwithO',
              name: { $first: '$CwithO' },
              value: { $sum: 1 },
            },
          },
        ]
        // { authCheck: false }
      );

      const conclusions = [
        { name: '禁忌证', value: 0 },
        { name: '疑似职业病', value: 0 },
        { name: '正常', value: 0 },
        { name: '其他疾病或异常', value: 0 },
        { name: '复查', value: 0 },
        { name: '总数', value: 0 },
      ];

      for (let i = 0; i < conclusionRes.length; i++) {
        const item = conclusionRes[i];
        if (item.name !== '未见异常' && item.name !== '目前未见异常') {
          const find = conclusions.find(conclusion => conclusion.name === item.name);
          if (find) {
            find.value = item.value;
          }
        } else {
          const find = conclusions[2];
          // const find = conclusions.find(conclusion => conclusion.name === '正常');
          if (find) {
            find.value += item.value;
          }
        }
        conclusions[5].value += item.value;
      }

      const appointment = await ctx.service.db.aggregate(
        'TjPlan',
        [
          {
            $match: {
              EnterpriseID: { $in: enterpriseIds },
              checkStartDate: { $lte: new Date() },
              checkEndDate: { $gte: new Date() },
            },
          },
          {
            $unwind: '$employees',
          },
          {
            $group: {
              _id: null,
              total: {
                $sum: 1,
              },
              // 应预约人数（统计所有员工）
              appointed: {
                $sum: {
                  $cond: {
                    if: {
                      $eq: [
                        '$employees.appointmentStatus',
                        1,
                      ],
                    },
                    // 已预约
                    then: 1,
                    else: 0,
                  },
                },
              },
              // 未预约
              unappoint: {
                $sum: {
                  $cond: {
                    if: {
                      $eq: [
                        '$employees.appointmentStatus',
                        0,
                      ],
                    },
                    then: 1,
                    else: 0,
                  },
                },
              },
            },
          },
          {
            $project: {
              _id: 0,
              total: 1,
              appointed: 1,
              unappoint: 1,
            },
          },
        ]
        // { authCheck: false }
      );

      ctx.helper.renderSuccess(ctx, {
        data: {
          conclusions,
          appointment,
        },
        message: '获取成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
        data: [],
      });
    }

  }

  async PPElist(ctx) {
    try {
      const enterpriseIds = await ctx.helper.getScopeData('enterprise_ids');
      const list = await ctx.service.db.aggregate(
        'ApplicationProduct',
        [
          {
            $match: {
              EnterpriseID: {
                $in: enterpriseIds,
              },
            },
          },
          {
            $group: {
              _id: '$auditStatus', // 审核状态 0 未审核 1 已通过 2 被驳回
              value: {
                $sum: 1,
              },
            },
          },
        ]
        // { authCheck: false }
      );
      const res = [
        { name: '已通过', value: 0 },
        { name: '待审核', value: 0 },
        { name: '未通过', value: 0 },
      ];

      for (let i = 0; i < list.length; i++) {
        const item = list[i];
        if (item._id === 1) {
          res[0].value = item.value;
        } else if (item._id === 0) {
          res[1].value = item.value;
        } else if (item._id === 2) {
          res[2].value = item.value;
        }
      }

      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '查询成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
        data: [],
      });
    }
  }

  async getCheckStatistics() {
    const {
      ctx,
      service,
    } = this;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    // console.log('体检年份参数:', ctx.query.year);
    const year = ctx.query.year;
    console.time('getCheckStatistics');
    const checkStatistics = await service.adminUser.getCheckStatistics({ EnterpriseID, year });
    console.timeEnd('getCheckStatistics');
    const data = {
      checkStatistics,
    };
    ctx.helper.renderSuccess(ctx, {
      data,
    });
  }

  // async getCheckStatistics() {
  //   const {
  //     ctx,
  //     service,
  //   } = this;
  //   const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
  //   console.time('getCheckStatistics');
  //   const checkStatistics = await service.adminUser.getCheckStatistics({ EnterpriseID });
  //   console.timeEnd('getCheckStatistics');
  //   console.log(checkStatistics, 'checkStatistics==============');
  //   const data = {
  //     checkStatistics,
  //   };

  //   ctx.helper.renderSuccess(ctx, {
  //     data,
  //   });
  // }
  // 初始化或设置 企业统计表
  async setStatistical() {
    const {
      ctx,
      service,
    } = this;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    const res = await service.adminUser.setStatistical({
      EnterpriseID,
    });
    if (res) {
      ctx.helper.renderSuccess(ctx, {});
    }
  }

  // 根据年份获取体检统计结果 htt
  async getHealthCheckByYear(ctx) {
    try {
      const params = ctx.request.body;
      const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
      const data = await this.ctx.service.adminUser.getCheckStatistics({ EnterpriseID, year: params.year });
      ctx.body = { status: 200, data };
    } catch (error) {
      console.log(error);
      return { status: 500, message: error };
    }
  }

  async updateStatistical() {
    const {
      ctx,
    } = this;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    try {
      await ctx.service.adminUser.updateStatistical({
        EnterpriseID,
      });
    } catch (err) {
      console.log(err.message);
    }
    ctx.helper.renderSuccess(ctx, {});
  }
  // 更新全部企业职业病人数到 统计表
  async updateOdiseases() {
    const {
      ctx,
    } = this;
    const years = [ '2020', '2019', '2018' ];
    for (let i = 0; i < years.length; i++) {
      await ctx.service.adminUser.updateOdiseases(years[i]);
    }
  }
  // 更新某家职业病人数到 统计表
  async updateOdisease() {
    const {
      ctx,
    } = this;
    // console.log(2231);
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    await ctx.service.adminUser.updateOdisease(EnterpriseID);
  }
  // 得到图表数据
  async getChartData() {
    const {
      ctx,
      service,
    } = this;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';

    const res = await service.adminUser.getChartData({
      EnterpriseID,
    });
    const sexCensus = await service.adminUser.getSexCensus({
      EnterpriseID,
    });
    const data = {
      res,
      sexCensus,
    };
    ctx.helper.renderSuccess(ctx, {
      data,
    });
  }

  // TODO: Role数据处理
  async handleRole() {
    const {
      service,
    } = this;
    await service.adminUser.handleRoles();
  }
  // 首页获取所有通知
  async getAllReadMessage() {
    const {
      ctx,
      service,
    } = this;
    const EnterpriseID = ctx.session.adminUserInfo.EnterpriseID;
    try {
      const targetItem = await service.adminorg.item(ctx, {
        query: {
          _id: EnterpriseID,
        },
        populate: [{
          path: 'adminUserId',
          select: 'group',
        }],
        files: 'adminUserId -_id',
      });
      if (!targetItem) {
        ctx.helper.renderFail(ctx, {
          message: 'EnterpriseID ERROR',
        });
        return;
      }
      const {
        pageSize,
        current,
        searchkey,
      } = ctx.query;
      const allReadMessageList = await service.messageNotification.getAllMessage(
        EnterpriseID,
        targetItem.adminUserId.group,
        2,
        pageSize || 5,
        current || 1,
        searchkey
      );

      ctx.helper.renderSuccess(ctx, {
        data: allReadMessageList,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  // 首页获取未读通知
  async getNoReadMessage() {
    const {
      ctx,
      service,
    } = this;
    const EnterpriseID = ctx.session.adminUserInfo.EnterpriseID;
    try {
      const targetItem = await service.adminorg.item(ctx, {
        query: {
          _id: EnterpriseID,
        },
        populate: [{
          path: 'adminUserId',
          select: 'group',
        }],
        files: 'adminUserId',
      });
      if (!targetItem) {
        ctx.helper.renderFail(ctx, {
          message: 'EnterpriseID ERROR',
        });
        return;
      }

      // wzq + 下一段代码有bug，这段打个补丁 ===>
      const pipelineMessage = [
        {
          $match: { 'reader.readerID': EnterpriseID, 'reader.readerGroup': targetItem.adminUserId.group },
        },
        {
          $unwind: '$reader',
        },
        {
          $match: { 'reader.readerID': EnterpriseID, 'reader.isRead': { $ne: 1 } },
        },
      ];
      const hasNoReadMessage = await ctx.service.db.aggregate('MessageNotification', pipelineMessage);
      if (!hasNoReadMessage.length) {
        return ctx.helper.renderSuccess(ctx, {
          data: {
            docs: [],
            pageInfo: {},
          },
        });
      }
      // < == wzq +

      const {
        pageSize,
        current,
        isPaging,
      } = ctx.query;
      const noReadMessageList = await service.messageNotification.getMessage(
        EnterpriseID,
        targetItem.adminUserId.group,
        0,
        pageSize,
        current,
        isPaging
      );
      // console.log(99999999999, noReadMessageList.docs[0]);


      ctx.helper.renderSuccess(ctx, {
        data: noReadMessageList,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 得到  职业健康检查异常结果  分类别数据
  async getSuspectData() {
    const {
      ctx,
    } = this;
    let nowname = '';

    const SuspectDisease = []; // 疑似
    const Contraindications = []; // 禁忌证
    const review = []; // 总复查
    const isReview = []; // 已复查

    const EnterpriseID = ctx.session.adminUserInfo.EnterpriseID;
    try {
      const gteTime = new Date(moment().subtract(1, 'years'));
      const SuspectData = await ctx.service.db.find('Suspect',
        { EnterpriseID, checkDate: { $lte: new Date(), $gte: gteTime } },
        { employeeId: 1, status: 1, CwithO: 1, checkType: 1 });
      for (let i = 0; i < SuspectData.length; i++) {
        nowname = await ctx.service.db.find('Employee', {
          _id: SuspectData[i].employeeId,
          status: 1,
          enable: true,
        });
        if (nowname.length === 0) {
          continue;
        }
        SuspectData[i].employeeId = nowname[0].name;
        SuspectData[i].status = SuspectData[i].status ? SuspectData[i].status : '-';
        if (SuspectData[i].CwithO === '疑似职业病' && SuspectData[i].checkType !== '2') {
          const exsist = SuspectDisease.findIndex(item => {
            return item.employeeId === SuspectData[i].employeeId;
          });
          if (exsist === -1) {
            SuspectDisease.push(SuspectData[i]);
          }
        }
        if (SuspectData[i].CwithO === '禁忌证' && SuspectData[i].checkType !== '2') {
          const exsist = Contraindications.findIndex(item => {
            return item.employeeId === SuspectData[i].employeeId;
          });
          if (exsist === -1) {
            Contraindications.push(SuspectData[i]);
          }
        }
        if (SuspectData[i].CwithO === '复查' && SuspectData[i].checkType !== '2' && (!SuspectData[i].checkConclusion || SuspectData[i].checkConclusion === '' || SuspectData[i].checkConclusion === '复查')) {
          review.push(SuspectData[i]);
        }
        if (SuspectData[i].checkType === '3' && SuspectData[i].CwithO !== '复查') {
          isReview.push(SuspectData[i]);
        }
      }
      // console.log(999, SuspectDisease);

      for (let i = 0; i < isReview.length; i++) {
        const index = review.findIndex(item => {
          return item.employeeId === isReview[i].employeeId;
        });
        if (index !== -1) {
          review.splice(index, 1);
        }
      }

      // 职业病
      const pipeline = [
        {
          $match: {
            EnterpriseID,
          },
        },
        {
          $lookup: {
            from: 'employees',
            foreignField: '_id',
            localField: 'employeeId',
            as: 'employees',
          },
        },
        {
          $match: {
            'employees.0.status': 1,
          },
        },
        {
          $project: {
            employeeId: 1,
            status: 1,
            diseaseName: 1,
            employees: 1,
          },
        },
      ];
      const OccupationalDiseaseData = await ctx.service.db.aggregate('Odisease', pipeline, { allowDiskUse: true });
      for (let i = 0; i < OccupationalDiseaseData.length; i++) {
        OccupationalDiseaseData[i].employeeId = OccupationalDiseaseData[i].employees[0].name;
        OccupationalDiseaseData[i].status = OccupationalDiseaseData[i].status ? OccupationalDiseaseData[i].status : '-';
        OccupationalDiseaseData[i].diseaseName = OccupationalDiseaseData[i].diseaseName ? OccupationalDiseaseData[i].diseaseName : '-';
      }

      ctx.helper.renderSuccess(ctx, {
        data: {
          SuspectDisease,
          Contraindications,
          review,
          OccupationalDiseaseData,
        },
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderFail(ctx, {
        message: 'error',
      });
    }

  }

  async getIsReadMessage() {
    const {
      ctx,
      service,
    } = this;
    const EnterpriseID = ctx.session.adminUserInfo.EnterpriseID;
    try {
      const targetItem = await service.adminorg.item(ctx, {
        query: {
          _id: EnterpriseID,
        },
        populate: [{
          path: 'adminUserId',
          select: 'group',
        }],
        files: 'adminUserId',
      });
      if (!targetItem) {
        ctx.helper.renderFail(ctx, {
          message: 'EnterpriseID ERROR',
        });
        return;
      }
      const {
        pageSize,
        current,
        isPaging,
      } = ctx.query;
      const isReadMessageList = await service.messageNotification.getMessage(
        EnterpriseID,
        targetItem.adminUserId.group,
        1,
        pageSize || 10,
        current || 1,
        isPaging
      );
      ctx.helper.renderSuccess(ctx, {
        data: isReadMessageList,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // async wxcsyayaya(ctx) {
  //   const token = 'wzqyaya';
  //   const { signature, echostr, timestamp, nonce } = ctx.query;
  //   const reqArray = [ nonce, timestamp, token ];
  //   reqArray.sort();
  //   const sortStr = reqArray.join('');
  //   const sha1 = function(str) {
  //     const shasum = crypto.createHash('sha1');
  //     return shasum.update(str, 'utf-8').digest('hex');
  //   };
  //   const sha1Str = sha1(sortStr.toString().replace(/,/g, ''));
  //   if (sha1Str === signature) {
  //     console.log('对了');
  //     ctx.body = echostr;
  //   }
  // }

  // 汪要的数据
  async updateHealthList() {
    const {
      ctx,
    } = this;

    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    // 跑数据库操作
    // 1. 先将 StatisticalTable表中 所有的 healthList是数组的 变成对象
    const res1 = await ctx.service.db.find('StatisticalTable', { EnterpriseID }, { healthList: 1, EnterpriseID: 1 });
    for (let i = 0; i < res1.length; i++) {
      // 汪 要的数据
      // await this.ctx.model.StatisticalTable.update({
      //   EnterpriseID: res1[i].EnterpriseID,
      // }, {
      //   healthListNumber: {},
      // });
      await ctx.service.db.updateOne('StatisticalTable', {
        EnterpriseID: res1[i].EnterpriseID,
      }, {
        healthListNumber: {},
      });

    }
    // 然后  便利每个表 对应push上去
    // examination
    const res2 = await ctx.service.db.find('Healthcheck', { EnterpriseID });
    for (let i = 0; i < res2.length; i++) {

      const shouldCheckNum = 'healthListNumber.' + res2[i].year + '.shouldCheckNum'; // 汪 要的数据
      const actuallNum = 'healthListNumber.' + res2[i].year + '.actuallNum'; // 汪 要的数据
      const normal = 'healthListNumber.' + res2[i].year + '.normal'; // 汪 要的数据
      const re_examination = 'healthListNumber.' + res2[i].year + '.re_examination'; // 汪 要的数据
      const suspected = 'healthListNumber.' + res2[i].year + '.suspected'; // 汪 要的数据
      const forbid = 'healthListNumber.' + res2[i].year + '.forbid'; // 汪 要的数据
      const otherDisease = 'healthListNumber.' + res2[i].year + '.otherDisease'; // 汪 要的数据

      // 汪 要的数据
      if (res2[i].checkType === '1' || res2[i].checkType === '0') { // 表示 除了离岗时
        // await this.ctx.model.StatisticalTable.updateMany({
        //   EnterpriseID: res2[i].EnterpriseID,
        // }, {
        //   $inc: {
        //     [shouldCheckNum]: Number(res2[i].shouldCheckNum ? res2[i].shouldCheckNum : 0),
        //     [actuallNum]: Number(res2[i].actuallNum ? res2[i].actuallNum : 0),
        //     [normal]: Number(res2[i].normal ? res2[i].normal : 0),
        //     [re_examination]: Number(res2[i].re_examination ? res2[i].re_examination : 0),
        //     [suspected]: Number(res2[i].suspected ? res2[i].suspected : 0),
        //     [forbid]: Number(res2[i].forbid ? res2[i].forbid : 0),
        //     [otherDisease]: Number(res2[i].otherDisease ? res2[i].otherDisease : 0),
        //   },
        // });
        await ctx.service.db.updateMany('StatisticalTable', {
          EnterpriseID: res2[i].EnterpriseID,
        }, {
          $inc: {
            [shouldCheckNum]: Number(res2[i].shouldCheckNum ? res2[i].shouldCheckNum : 0),
            [actuallNum]: Number(res2[i].actuallNum ? res2[i].actuallNum : 0),
            [normal]: Number(res2[i].normal ? res2[i].normal : 0),
            [re_examination]: Number(res2[i].re_examination ? res2[i].re_examination : 0),
            [suspected]: Number(res2[i].suspected ? res2[i].suspected : 0),
            [forbid]: Number(res2[i].forbid ? res2[i].forbid : 0),
            [otherDisease]: Number(res2[i].otherDisease ? res2[i].otherDisease : 0),
          },
        });
      }

    }

  }

  // 标记已读消息
  async isReadMessage() {
    const {
      ctx,
      service,
    } = this;
    try {
      const {
        ID,
      } = ctx.request.body;
      ID.forEach(async function(item) {
        let message = await service.messageNotification.item(ctx, {
          query: {
            _id: item,
          },
        });
        message = JSON.parse(JSON.stringify(message));
        const adminOrg = await service.adminorg.item(ctx, {
          query: {
            _id: ctx.session.adminUserInfo.EnterpriseID,
          },
          populate: [{
            path: 'adminUserId',
            select: 'group',
          }],
          files: {
            adminUserId: 1,
          },
        });
        const index = message.reader.findIndex(function(value) {
          return (
            (value.readerID === ctx.session.adminUserInfo.EnterpriseID &&
            value.readerGroup === adminOrg.adminUserId.group &&
            value.isRead === 0) || (value.readerID === ctx.session.adminUserInfo._id && value.isRead === 0)
          );
        });
        if (index < 0) {
          ctx.helper.renderFail(ctx, {
            message: '凉凉，后端出错咯',
          });
          return;
        }
        message.reader[index].isRead = 1;
        await service.messageNotification.update(ctx, item, message);
      });
      ctx.helper.renderSuccess(ctx, {
        data: 'OK',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  async handleWarnNotice() {
    const {
      ctx,
    } = this;
    // await ctx.model.WarnNotice.remove({});
    await ctx.service.db.remove('WarnNotice', {});
  }
  // 首页-危险点/法律风险
  async dangerousPoints(ctx) {
    try {
      const collections = [
        {
          name: 'riskAssessmentReport',
          selfKey: '_id',
          foreignKey: 'EnterpriseID',
          asKey: 'riskAssessmentReport',
        },
        {
          name: 'preventAssess',
          selfKey: '_id',
          foreignKey: 'EnterpriseID',
          asKey: 'preventAssessId',
        },
      ];
      const params = {
        collections,
        _id: ctx.session.adminUserInfo.EnterpriseID,
      };
      const doc = await ctx.service.dangerousPoints.findAdminorgList(ctx.model.Adminorg, params);

      const allList = [ doc ];
      // 将行业分类转换成中文字符串，方便展示
      // const industryArray = await ctx.service.industryCategory.find({ isPaging: '0' }, { sort: { sort: 1 } });
      // console.log(3333333, industryArray);
      // allList.map(async val => {
      //   if (val.industryCategory) {
      //     const buildIndustryArray = await buildIndustry(industryArray, val.industryCategory);
      //     if (buildIndustryArray) {
      //       val.industryCategory = buildIndustryArray.join(' ➤ ');
      //     }
      //   }
      //   return (val.createTime = moment(val.createTime).format('YYYY-MM-DD HH:mm:ss'));
      // });
      const allListLength = allList.length;
      // 获取企业管理员的账户信息、危害暴露等级、职业病危害综合风险等级、责任自查、分类分级等级、体检情况、申报情况、检测情况、职业病危害人数等信息、总超标点数
      for (let i = 0; i < allListLength; i++) {
        const thisList = allList[i];
        // 获取企业管理员的账户信息
        thisList.adminUserId = await ctx.service.db.find('AdminUser', { _id: thisList.adminUserId });
        // 获取每个企业的职业健康管理状况等级(责任自查)
        if (isArray(thisList.preventAssessId)) {
          if (thisList.preventAssessId && (thisList.preventAssessId[0])) {
            // 按降序排列，然后取第一个数据，根据时间判断是否过期
            const preventAssessId = thisList.preventAssessId.sort(function(a, b) {
              return b.assessDate - a.assessDate;
            });
            const { completeStatusRes } = this.calTime(preventAssessId[0].assessDate);
            thisList.preventAssess = completeStatusRes === 1 ? 0 : preventAssessId[0].level;
          }
        } else {
          if (thisList.preventAssessId && thisList.preventAssessId.level) {
            const { completeStatusRes } = this.calTime(thisList.preventAssessId.assessDate);
            thisList.preventAssess = completeStatusRes === 1 ? 0 : thisList.preventAssessId.level;
          }
        }
        // 获取每个企业的职业病危害综合风险等级
        // thisList.assessmentResult = thisList.exposeRiskLevel;
        thisList.comprehensiveLevel = '';
        if (isArray(thisList.riskAssessmentReport)) {
          if (thisList.riskAssessmentReport.length > 0) {
            thisList.comprehensiveLevel = thisList.riskAssessmentReport[0].assessmentResult;
          }
        } else {
          thisList.comprehensiveLevel = thisList.riskAssessmentReport.assessmentResult;
        }
        // if (thisList.preventAssess === 0 || !thisList.preventAssess) {
        //   thisList.comprehensiveLevel = '';
        // } else {
        //   // 有暴露等级才会有综合风险等级
        //   if (thisList.exposeRiskLevel === 0 || thisList.exposeRiskLevel) {
        //     const comprehensiveLevel = await ctx.model.RiskAssessmentReport.find({ EnterpriseID: thisList._id }, { assessmentResult: 1 }).sort({ year: -1 });
        //     if (comprehensiveLevel.length > 0) {
        //       thisList.comprehensiveLevel = comprehensiveLevel[0].assessmentResult;
        //     } else {
        //       thisList.comprehensiveLevel = '';
        //     }
        //   } else {
        //     thisList.comprehensiveLevel = '';
        //   }
        //   // const riskAssessmentReport = thisList.riskAssessmentReport;
        //   // if (riskAssessmentReport.length !== 0) {
        //   //   thisList.assessmentResult = riskAssessmentReport[0].assessExposeLevel;
        //   //   thisList.comprehensiveLevel = riskAssessmentReport[0].assessmentResult;
        //   // }
        // }
        // 获取每个企业的体检情况等信息
        thisList.isRatioArr = 0; // 体检状态（是否体检）
        thisList.ratioArr = 0; // 体检率
        thisList.re_examination = 0; // 复查人数
        thisList.forbid = 0; // 禁忌证人数
        thisList.suspected = 0; // 疑似职业病人数
        thisList.odiseases = 0; // 职业病人数
        if (thisList.healcheckInfo) {
          const harmStatisticsCount = isArray(thisList.harmStatistics) ? (thisList.harmStatistics.length ? JSON.parse(JSON.stringify(thisList.harmStatistics[thisList.harmStatistics.length - 1])).count[2].value : 0) : 0; // 接害人数
          thisList.re_examination = thisList.healcheckInfo.recheck; // 复查人数
          thisList.forbid = thisList.healcheckInfo.forbid; // 禁忌证人数
          thisList.suspected = thisList.healcheckInfo.suspected; // 疑似职业病人数
          thisList.odiseases = thisList.healcheckInfo.occupational; // 职业病人数
          const { completeStatusRes } = thisList.healcheckInfo.recentDay ? this.calTime(thisList.healcheckInfo.recentDay) : { completeStatusRes: 0 };
          thisList.isRatioArr = completeStatusRes; // 体检状态（是否体检）
          thisList.ratioArr = thisList.healcheckInfo.actuallNum ? (harmStatisticsCount ? (thisList.healcheckInfo.actuallNum / harmStatisticsCount) * 100 : 100) : 0; // 体检率
        }
        // 获取该企业的申报情况
        thisList.isOnlineDeclaration = 0;
        if (thisList.onlineDeclarationFiles && thisList.onlineDeclarationFiles.monthD) {
          if (thisList.onlineDeclarationFiles.monthD <= new Date()) {
            const { completeStatusRes } = this.calTime(thisList.onlineDeclarationFiles.monthD);
            thisList.isOnlineDeclaration = completeStatusRes;
          }
        }

        // 获取该企业的检测情况
        thisList.isJobhealths = 0;
        if (thisList.reportTime) {
          const { completeStatusRes } = this.calTime(thisList.reportTime);
          thisList.isJobhealths = completeStatusRes;
        }
        // 获取该企业的总超标点数
        thisList.exceed = 0;
        for (const key in thisList.checkResult) {
          if ((key === 'dust' || key === 'heat' || key === 'chemical' || key === 'radiation' || key === 'biological' || key === 'noise' || key === 'powerFrequencyElectric' || key === 'ultraHighRadiation' || key === 'handBorneVibration' || key === 'highFrequencyEle' || key === 'laser' || key === 'microwave' || key === 'ultraviolet' || key === 'ionizatioRadial' || key === 'ionizatioSource') && thisList.checkResult[key].exceed && thisList.checkResult[key].exceed !== 0) {
            thisList.exceed = thisList.exceed + Number(thisList.checkResult[key].exceed);
          }
        }

      }
      ctx.helper.renderSuccess(ctx, {
        data: allList,
        message: '数据获取成功',
      });
    } catch (err) {
      console.log(err);
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }
  calTime(time) {
    const nowTime = moment();
    let completeStatusRes = 0;
    let messageRes = '未完成';
    const monthD = moment(time);
    const diffTime = nowTime.diff(monthD, 'months', true);
    if (diffTime <= 10) {
      completeStatusRes = 3;
      messageRes = '已完成';
    } else if (diffTime <= 12 && diffTime > 10) {
      completeStatusRes = 2;
      messageRes = '将到期';
    } else {
      completeStatusRes = 1;
      messageRes = '已过期';
    }
    return {
      completeStatusRes,
      messageRes,
    };
  }

  // 企业绑定当前新添加企业ID 是企业添加多个企业时用于告知是添加的哪个企业，以此获取其审核状态
  async bindEnterpriseID() {
    const { ctx, service } = this;
    const {
      id,
    } = ctx.query;
    try {
      await service.adminUser.update(ctx, ctx.session.adminUserInfo._id || '', { newAddEnterpriseID: id });
      ctx.helper.renderSuccess(ctx);
    } catch (error) {
      ctx.auditLog('错误', `企业绑定当前新添加企业ID错误：${error.stack} 。`, 'error');
      ctx.helper.renderFail(ctx);
    }
  }
  async task() {
    try {
      console.time('task_total_execution');
      const EnterpriseID = this.ctx.session.adminUserInfo ? this.ctx.session.adminUserInfo.EnterpriseID : '';
      const completeList = {
        employees: {
          completion: 0,
        },
        millConstruction: {
          completion: 0,
        },
        warnNotice: {
          completion: 0,
        },
        onlineDeclaration: {
          completion: 0,
          validPeriod: null,
        },
        ledger: {
          completion: 0,
          validPeriod: null,
        },
        manageSystem: {
          completion: 0,
          validPeriod: null,
        },
        role: {
          completion: 0,
          validPeriod: null,
        },
        facilities: {
          completion: 0,
          validPeriod: null,
        },
        defendproducts: {
          completion: 0,
          validPeriod: null,
        },
        manageEmergency: {
          completion: 0,
          validPeriod: null,
        },
        diseasesOverhaul: {
          completion: 0,
          validPeriod: null,
        },
        preventionFunds: {
          completion: 0,
          validPeriod: null,
        },
        jobHealth: {
          completion: 0,
          validPeriod: null,
        },
        healthcheck: {
          completion: 0,
          validPeriod: null,
        },
        adminTraining: {
          completion: 0,
          validPeriod: null,
        },
        employeesTrainingPlan: {
          completion: 0,
          validPeriod: null,
        },
      };

      // 第一批：简单的 findOne 查询并行执行
      console.time('batch1_simple_queries');
      const [
        employees,
        millConstruction,
        warnNotice,
        facilities,
        manageSystem,
        role,
      ] = await Promise.all([
        this.ctx.service.db.findOne('Employee', { enable: true, EnterpriseID, departs: { $size: 1 } }, { _id: 1 }),
        this.ctx.service.db.findOne('MillConstruction', { EnterpriseID }, { _id: 1 }),
        this.ctx.service.db.findOne('WarnNotice', { EnterpriseID, 'tableData.0': { $exists: true } }, { _id: 1 }),
        this.ctx.service.db.findOne('Facility', { EnterpriseID }, { formData: 1 }),
        this.ctx.service.db.findOne('ManageSystem', { companyId: EnterpriseID }, { _id: 1 }),
        this.ctx.service.db.findOne('Roles', { EnterpriseID, 'formData.userId.1': { $exists: true } }, { _id: 1 }),
      ]);
      console.timeEnd('batch1_simple_queries');

      // 处理第一批查询结果
      try {
        completeList.employees = {
          completion: employees ? 100 : 0,
        };
        completeList.millConstruction = {
          completion: millConstruction ? 100 : 0,
        };
        completeList.warnNotice = {
          completion: warnNotice ? 100 : 0,
        };
        completeList.facilities = {
          completion: (facilities && facilities.formData && facilities.formData.length > 0) ? 100 : 0,
        };
        completeList.manageSystem = {
          completion: manageSystem ? 100 : 0,
        };
        completeList.role = {
          completion: role ? 100 : 0,
        };
      } catch (error) {
        console.error('处理第一批查询结果时出错:', error);
      }

      // 第二批：时间相关的查询并行执行
      console.time('batch2_time_queries');
      const [
        onlineDeclarationRes,
        adminTraining,
        employeesTrainingPlan,
        ledger,
        manageEmergency,
        diseasesOverhaul,
        preventionFunds,
        healthcheck,
      ] = await Promise.all([
        this.ctx.service.db.find('OnlineDeclarationFiles', { EnterpriseID, monthD: { $lte: new Date() } }, { year: 1, monthD: 1 }, { sort: { monthD: -1 } }),
        this.ctx.service.db.find('Propagate', { EnterpriseID, type: { $in: [ '管理人员培训', '负责人培训' ] }, implementData: { $lte: new Date() } }, { implementData: 1 }, { sort: { implementData: -1 } }),
        this.ctx.service.db.find('Propagate', { EnterpriseID, type: '劳动者培训', implementData: { $lte: new Date() } }, { implementData: 1 }, { sort: { implementData: -1 } }),
        this.ctx.service.db.find('Ledger', { EnterpriseID, year: { $lte: new Date() } }, { year: 1 }, { sort: { year: -1 } }),
        this.ctx.service.db.find('ManageEmergency', { EnterpriseID, year: { $lte: new Date() } }, { year: 1 }, { sort: { year: -1 } }),
        this.ctx.service.db.find('DiseasesOverhaul', { EnterpriseID, checkTime: { $lte: new Date() } }, { checkTime: 1 }, { sort: { checkTime: -1 }, limit: 1 }),
        this.ctx.service.db.find('AnnualPlan', { EnterpriseID, year: { $lte: new Date().getFullYear() } }, { year: 1 }, { sort: { year: -1 }, limit: 1 }),
        this.ctx.service.db.find('Suspect', { EnterpriseID, checkDate: { $lte: new Date() } }, { checkDate: 1 }, { sort: { checkDate: -1 }, limit: 1 }),
      ]);
      console.timeEnd('batch2_time_queries');

      // 处理第二批查询结果
      console.time('batch2_processing');
      try {
        // 危害申报
        if (onlineDeclarationRes && onlineDeclarationRes[0]) {
          const time = onlineDeclarationRes[0].monthD || onlineDeclarationRes[0].year;
          const diffMonth = moment().diff(moment(time), 'months');
          if (diffMonth <= 12) {
            completeList.onlineDeclaration = {
              completion: 100,
              validPeriod: moment(time).add(1, 'y'),
            };
          } else {
            completeList.onlineDeclaration = {
              completion: 0,
              validPeriod: moment(time).add(1, 'y'),
            };
          }
        } else {
          completeList.onlineDeclaration = {
            completion: 0,
          };
        }

        // 管理人员培训
        if (adminTraining && adminTraining[0] && adminTraining[0].implementData) {
          const diffMonth = moment().diff(moment(adminTraining[0].implementData), 'months');
          if (diffMonth <= 12) {
            completeList.adminTraining = {
              completion: 100,
              validPeriod: moment(adminTraining[0].implementData).add(1, 'y'),
            };
          } else {
            completeList.adminTraining = {
              completion: 0,
              validPeriod: moment(adminTraining[0].implementData).add(1, 'y'),
            };
          }
        } else {
          completeList.adminTraining = {
            completion: 0,
          };
        }

        // 员工培训记录
        if (employeesTrainingPlan && employeesTrainingPlan[0] && employeesTrainingPlan[0].implementData) {
          const diffMonth = moment().diff(moment(employeesTrainingPlan[0].implementData), 'months');
          if (diffMonth <= 12) {
            completeList.employeesTrainingPlan = {
              completion: 100,
              validPeriod: moment(employeesTrainingPlan[0].implementData).add(1, 'y'),
            };
          } else {
            completeList.employeesTrainingPlan = {
              completion: 0,
              validPeriod: moment(employeesTrainingPlan[0].implementData).add(1, 'y'),
            };
          }
        } else {
          completeList.employeesTrainingPlan = {
            completion: 0,
          };
        }
      } catch (error) {
        console.error('处理第二批查询结果时出错:', error);
      }

      // 继续处理其他查询结果
      try {
        // 原料设备
        if (ledger && ledger[0] && ledger[0].year) {
          const diffMonth = moment().diff(moment(ledger[0].year), 'months');
          if (diffMonth <= 12) {
            completeList.ledger = {
              completion: 100,
              validPeriod: moment(ledger[0].year).add(1, 'y'),
            };
          } else {
            completeList.ledger = {
              completion: 0,
              validPeriod: moment(ledger[0].year).add(1, 'y'),
            };
          }
        } else {
          completeList.ledger = {
            completion: 0,
          };
        }

        // 应急救援演练
        if (manageEmergency && manageEmergency[0] && manageEmergency[0].year) {
          const diffMonth = moment().diff(moment(manageEmergency[0].year), 'months');
          if (diffMonth <= 12) {
            completeList.manageEmergency = {
              completion: 100,
              validPeriod: moment(manageEmergency[0].year).add(1, 'y'),
            };
          } else {
            completeList.manageEmergency = {
              completion: 0,
              validPeriod: moment(manageEmergency[0].year).add(1, 'y'),
            };
          }
        } else {
          completeList.manageEmergency = {
            completion: 0,
          };
        }

        // 设施设备检测维修
        if (diseasesOverhaul && diseasesOverhaul[0] && diseasesOverhaul[0].checkTime) {
          const diffMonth = moment().diff(moment(diseasesOverhaul[0].checkTime), 'months');
          if (diffMonth <= 12) {
            completeList.diseasesOverhaul = {
              completion: 100,
              validPeriod: moment(diseasesOverhaul[0].checkTime).add(1, 'y'),
            };
          } else {
            completeList.diseasesOverhaul = {
              completion: 0,
              validPeriod: moment(diseasesOverhaul[0].checkTime).add(1, 'y'),
            };
          }
        } else {
          completeList.diseasesOverhaul = {
            completion: 0,
          };
        }

        // 防治经费管理
        if (preventionFunds.length > 0) {
          if (parseInt(preventionFunds[0].year) === new Date().getFullYear()) {
            completeList.preventionFunds = {
              completion: 100,
              validPeriod: moment(preventionFunds[0].year).add(1, 'y'),
            };
          } else {
            completeList.preventionFunds = {
              completion: 0,
              validPeriod: moment(preventionFunds[0].year).add(1, 'y'),
            };
          }
        } else {
          completeList.preventionFunds = {
            completion: 0,
          };
        }

        // 体检档案
        if (healthcheck && healthcheck[0] && healthcheck[0].checkDate) {
          const diffMonth = moment().diff(moment(healthcheck[0].checkDate), 'months');
          if (diffMonth <= 12) {
            completeList.healthcheck = {
              completion: 100,
              validPeriod: moment(healthcheck[0].checkDate).add(1, 'y'),
            };
          } else {
            completeList.healthcheck = {
              completion: 0,
              validPeriod: moment(healthcheck[0].checkDate).add(1, 'y'),
            };
          }
        } else {
          completeList.healthcheck = {
            completion: 0,
          };
        }
      } catch (error) {
        console.error('处理其他查询结果时出错:', error);
      }
      console.timeEnd('batch2_processing');

      // 第三批：复杂聚合查询并行执行
      console.time('batch3_complex_queries');
      const defendproductsPipeline = [
        {
          $match: {
            EnterpriseID,
          },
        },
        {
          $unwind: '$formData',
        },
        {
          $match: {
            'formData.date': { $lte: new Date() },
          },
        },
        {
          $sort: { 'formData.date': -1 },
        },
        {
          $limit: 1,
        },
        {
          $addFields: { date: '$formData.date' },
        },
        {
          $project: { date: 1 },
        },
      ];

      const jobHealthPipeline = [
        {
          $match: { EnterpriseID },
        },
        {
          $addFields: { reportTime: { $toDate: '$reportTime' } },
        },
        {
          $match: { reportTime: { $lte: new Date() } },
        },
        {
          $lookup: {
            from: 'checkAssessment',
            foreignField: 'jobHealthId',
            localField: '_id',
            as: 'checkAssessment',
          },
        },
        {
          $match: { 'checkAssessment.0': { $exists: true } },
        },
        {
          $sort: { reportTime: -1 },
        },
        {
          $limit: 1,
        },
      ];

      const [
        defendproductsResult,
        jobHealthResult,
      ] = await Promise.all([
        this.ctx.service.db.aggregate('Defendproducts', defendproductsPipeline),
        this.ctx.service.db.aggregate('JobHealth', jobHealthPipeline),
      ]);
      console.timeEnd('batch3_complex_queries');

      // 处理第三批查询结果
      console.time('batch3_processing');
      try {
        // 个人防护
        if (defendproductsResult && defendproductsResult[0] && defendproductsResult[0].date) {
          const diffMonth = moment().diff(moment(defendproductsResult[0].date), 'months');
          if (diffMonth <= 9) {
            completeList.defendproducts = {
              completion: 100,
              validPeriod: moment(defendproductsResult[0].date).add(1, 'y'),
            };
          } else {
            completeList.defendproducts = {
              completion: 0,
              validPeriod: moment(defendproductsResult[0].date).add(1, 'y'),
            };
          }
        } else {
          completeList.defendproducts = {
            completion: 0,
          };
        }

        // 定期检测或者现状评价
        if (jobHealthResult && jobHealthResult[0] && jobHealthResult[0].reportTime) {
          const diffMonth = moment().diff(moment(jobHealthResult[0].reportTime), 'months');
          if (diffMonth <= 12) {
            completeList.jobHealth = {
              completion: 100,
              validPeriod: moment(jobHealthResult[0].reportTime).add(1, 'y'),
            };
          } else {
            completeList.jobHealth = {
              completion: 0,
              validPeriod: moment(jobHealthResult[0].reportTime).add(1, 'y'),
            };
          }
        } else {
          completeList.jobHealth = {
            completion: 0,
          };
        }
      } catch (error) {
        console.error('处理第三批查询结果时出错:', error);
      }
      console.timeEnd('batch3_processing');

      // 最终处理：计算完成率并更新数据库
      console.time('final_processing');
      let completeNum = 0;
      let allCount = 0;
      Object.keys(completeList).forEach(key => {
        if (completeList[key].completion === 100) {
          completeNum++;
        }
        allCount++;
      });
      const completeRate = Math.round((completeNum / allCount) * 100);

      // 并行执行数据库更新操作
      await Promise.all([
        this.ctx.service.db.updateMany('Adminorg', { _id: EnterpriseID }, { $set: { totalCompletion: completeRate } }),
        this.ctx.service.db.updateMany('FilesCompleteness', { EnterpriseID }, { $set: completeList }),
      ]);

      // 清除相关缓存
      await this.clearFilesCompletenessCache(EnterpriseID);

      console.timeEnd('final_processing');
      console.timeEnd('task_total_execution');
    } catch (error) {
      console.log('统计企业档案完成率定时任务报错', error);
    }
  }

  // 获取企业档案完成情况
  async getFilesCompleteness() {
    const { ctx } = this;
    console.time('getFilesCompleteness_total');

    const EnterpriseID = ctx.session.adminUserInfo && ctx.session.adminUserInfo.EnterpriseID;
    const cacheKey = `files_completeness:${EnterpriseID}`;

    // 尝试从缓存获取
    console.time('cache_check');
    try {
      if (ctx.app.redis) {
        const cachedData = await ctx.app.redis.get(cacheKey);
        if (cachedData) {
          console.timeEnd('cache_check');
          console.timeEnd('getFilesCompleteness_total');
          console.log('从缓存返回档案完成度数据');
          return ctx.helper.renderSuccess(ctx, {
            data: JSON.parse(cachedData),
            message: '数据获取成功（缓存）',
          });
        }
      }
    } catch (cacheError) {
      console.log('缓存读取失败，继续执行计算:', cacheError.message);
    }
    console.timeEnd('cache_check');

    // 缓存未命中，执行计算
    console.time('parallel_task_and_get');
    const [ , EnterpriseFiles ] = await Promise.all([
      this.task(),
      ctx.service.filesCompleteness.get(),
    ]);
    console.timeEnd('parallel_task_and_get');

    // 优化深拷贝：使用浅拷贝 + 选择性深拷贝
    console.time('data_processing');
    const data = { ...EnterpriseFiles.toObject() };

    // 计算总完成率 - 优化循环
    let completedNum = 0,
      undoneNum = 0;
    const blacklistSet = new Set([ 'sanitaryInspection', 'record', 'warnSignGenerate' ]);
    const currentTime = new Date().getTime(); // 提取到循环外

    Object.keys(data).forEach(key => {
      const item = data[key];
      if (item instanceof Object) {
        item.valid = !item.validPeriod || new Date(item.validPeriod).getTime() > currentTime;
        if (!blacklistSet.has(key)) {
          undoneNum++;
          if (item.completion && item.valid) completedNum++;
        }
      }
    });

    data.totalCompletion = parseInt(completedNum * 100 / undoneNum);
    console.timeEnd('data_processing');

    // 缓存结果（30分钟过期）
    console.time('cache_set');
    try {
      if (ctx.app.redis) {
        await ctx.app.redis.setex(cacheKey, 1800, JSON.stringify(data));
        console.log('档案完成度数据已缓存');
      }
    } catch (cacheError) {
      console.log('缓存设置失败:', cacheError.message);
    }
    console.timeEnd('cache_set');
    console.timeEnd('getFilesCompleteness_total');

    // 返回数据
    ctx.helper.renderSuccess(ctx, {
      data,
      message: '数据获取成功',
    });
  }

  // 清除档案完成度缓存
  async clearFilesCompletenessCache(EnterpriseID) {
    try {
      if (this.ctx.app.redis) {
        const cacheKey = `files_completeness:${EnterpriseID}`;
        await this.ctx.app.redis.del(cacheKey);
        console.log(`已清除企业 ${EnterpriseID} 的档案完成度缓存`);
      }
    } catch (error) {
      console.log('清除缓存失败:', error.message);
    }
  }

  // 获取最近一年的体检统计结果
  async getCheckData() {
    const { ctx } = this;
    try {
      const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
      const data = await this.ctx.service.adminUser.getCheckResult({ EnterpriseID });
      ctx.body = { status: 200, data };
    } catch (error) {
      console.log(error);
      return { status: 500, message: error };
    }
  }
  // getEveryYear  获取所有年份的体检数据
  async getEveryYearHealthCheck(ctx) {
    try {
      const year = ctx.query.year;
      const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
      const data = await ctx.service.db.find('Adminorg', { _id: EnterpriseID }, { healcheckInfo: 1 });
      // console.log(data, '最近一次的体检结果');
      let everyYearHealthCheck = [];
      const arr = [];
      const NowYear = new Date().getFullYear();
      if (year !== NowYear) {
        everyYearHealthCheck = await ctx.service.db.find('Healthcheck', { EnterpriseID, year });
      }
      if (everyYearHealthCheck.length > 0 && year) {
        let re_examination = 0;
        let suspected = 0;
        let forbid = 0;
        let otherDisease = 0;
        let recheck = 0;
        let normal = 0;
        let actuallNum = 0;
        let shouldCheckNum = 0;
        for (let i = 0; i < everyYearHealthCheck; i++) {
          re_examination += everyYearHealthCheck[i].re_examination;
          suspected += everyYearHealthCheck[i].suspected;
          forbid += everyYearHealthCheck[i].forbid;
          otherDisease += everyYearHealthCheck[i].otherDisease;
          recheck += everyYearHealthCheck[i].recheck;
          normal += everyYearHealthCheck[i].normal;
          actuallNum += everyYearHealthCheck[i].actuallNum;
          shouldCheckNum += everyYearHealthCheck[i].shouldCheckNum;
        }
        arr.push({ re_examination, suspected, forbid, otherDisease, recheck, normal, actuallNum, shouldCheckNum });
        ctx.body = { status: 200, arr };
      }
      ctx.body = { status: 200, data };
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        data: err,
        status: 500,
      });
    }
  }

  async getHarmFactorsData(harmStatistics) {
    let touchHarmEmployees = [];
    const harmFactorsData = []; // 危害因素接害人数数据
    let harmFactors = [];
    let noiseFactors = {};
    let physicsFactors = { label: '物理因素', value: 0, harmFactors: [], employee: [] };
    if (harmStatistics && harmStatistics[0]) {
      harmFactors = harmStatistics.filter(item => item.sort === 'all')[0].count;
      harmFactors.forEach(item => {
        if (item.label.indexOf('化学') !== -1 || item.label.indexOf('生物') !== -1 || item.label.indexOf('粉尘') !== -1 || item.label.indexOf('放射') !== -1) {
          item.harmFactors && item.harmFactors[0] && item.harmFactors.push({ label: '总计', value: item.value });
          harmFactorsData.push(item);
        } else if (item.label.indexOf('物理') !== -1) {
          physicsFactors = item;
          physicsFactors.label = '物理因素';
        } else if (item.label.indexOf('噪声') !== -1) {
          noiseFactors = { label: '噪声', value: item.employee.length, employee: item.employee };
        }
      });
      if (noiseFactors + '' !== '{}' && noiseFactors.value > 0) {
        physicsFactors.harmFactors.push(noiseFactors);
        physicsFactors.employee = physicsFactors.employee.concat(noiseFactors.employee);
        physicsFactors.value = Array.from(new Set(physicsFactors.employee)).length;
      }
      physicsFactors.harmFactors[0] && physicsFactors.harmFactors.push({ label: '总计', value: physicsFactors.value });
      // if (physicsFactors && physicsFactors.harmFactors[0]) {
      //   harmFactorsData.push(physicsFactors);
      // }
      harmFactorsData.push(physicsFactors);
      // 接触职业病危害人数
      touchHarmEmployees = harmFactors.filter(item2 => item2.label === '劳动者接害总人数');
      touchHarmEmployees = touchHarmEmployees[0] ? touchHarmEmployees[0].employee : [];
    }
    return { touchHarmEmployees, harmFactorsData };
  }

  // harmStatisticsPerson  获取所有接害人数
  async harmStatisticsPerson(ctx) {
    try {
      const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';

      // 尝试从Redis缓存获取数据
      const cachedData = await ctx.service.redisData.getHarmStatisticsCache(ctx, EnterpriseID);
      if (cachedData) {
        ctx.helper.renderSuccess(ctx, {
          data: cachedData,
          fromCache: true,
        });
        return;
      }

      // console.log('EnterpriseID:666666', EnterpriseID);
      const pipeline = [
        { $match: { EnterpriseID } },
        { $unwind: '$children' },
        { $project: {
          workspace: {
            $cond: [
              { $eq: [ '$category', 'mill' ] },
              '$children.name',
              '$name',
            ],
          },
          station: {
            $cond: [
              { $eq: [ '$category', 'mill' ] },
              '$children.children',
              [ '$children' ],
            ],
          },
        } },
        { $unwind: '$station' },
        { $addFields: { customizeHarm: { $regexFindAll: { input: '$station.customizeHarm', regex: /[^,|，|、|\/]+/ } } } },
        { $addFields: {
          customizeHarm: {
            $map: {
              input: '$customizeHarm',
              as: 'item',
              in: '$$item.match',
            },
          },
          harmFactors: {
            $map: {
              input: '$station.harmFactors',
              as: 'item',
              in: { $arrayElemAt: [ '$$item', 0 ] },
            },
          },
          harmFactors2: {
            $map: {
              input: '$station.harmFactors',
              as: 'item',
              in: { $arrayElemAt: [ '$$item', 1 ] },
            },
          },
        } },
        { $addFields: { harmFactors: { $concatArrays: [ '$customizeHarm', '$harmFactors', '$harmFactors2' ] } } },
        { $match: { 'harmFactors.0': { $exists: true } } },
        { $unwind: '$station.children' },

        { $group: { _id: '$station.children.employees', stationInfo: { $push: { harmFactors: '$harmFactors', station: '$station.name', workspace: '$workspace', ischeckresult: '$checkResult' } } } },

        { $lookup: {
          from: 'employees', localField: '_id', foreignField: '_id', as: 'employee',
        } },
        {
          $match: {
            'employee.status': { $ne: 0 },
          },
        },
        {
          $lookup: {
            from: 'suspects', localField: '_id', foreignField: 'employeeId', as: 'suspects',
          },
        },
        // {
        //   $lookup: {
        //     from: 'checkResults', localField: '_id', foreignField: 'employeeId', as: 'suspects',
        //   },
        // },

      ];
      const persons = await ctx.service.db.aggregate('MillConstruction', pipeline, { allowDiskUse: true });
      // 优化CheckResult查询，只获取必要字段
      const stationInfo = await ctx.service.db.find('CheckResult', { EnterpriseID }, {
        biologicalFactors: 1,
        chemistryFactors: 1,
        dustFactors: 1,
        handBorneVibrationFactors: 1,
        heatFactors: 1,
        highFrequencyEleFactors: 1,
        laserFactors: 1,
        microwaveFactors: 1,
        noiseFactors: 1,
        powerFrequencyElectric: 1,
        SiO2Factors: 1,
        ultraHighRadiationFactors: 1,
        ultravioletFactors: 1,
        ionizatioSourceFactors: 1,
        ionizatioRadialFactors: 1,
      });

      // 优化数据处理逻辑 - 使用Map提高性能
      const factorArrays = {
        swArr: [],
        hxArr: [],
        fcArr: [],
        heatFactorsArr: [],
        handBorneVibrationArr: [],
        highFrequencyEleFactorsArr: [],
        laserFactorsArr: [],
        microwaveFactorsArr: [],
        noiseFactorsArr: [],
        powerFrequencyElectricArr: [],
        SiO2FactorsArr: [],
        ultraHighRadiationFactorsArr: [],
        ultravioletFactorsArr: [],
        ionizatioSourceFactorsArr: [],
        ionizatioRadialFactorsArr: [],
      };

      // 定义因素类型映射，减少重复代码
      const factorMapping = [
        { field: 'biologicalFactors', arrayKey: 'swArr' },
        { field: 'chemistryFactors', arrayKey: 'hxArr' },
        { field: 'dustFactors', arrayKey: 'fcArr' },
        { field: 'heatFactors', arrayKey: 'heatFactorsArr' },
        { field: 'handBorneVibrationFactors', arrayKey: 'handBorneVibrationArr' },
        { field: 'highFrequencyEleFactors', arrayKey: 'highFrequencyEleFactorsArr' },
        { field: 'laserFactors', arrayKey: 'laserFactorsArr' },
        { field: 'microwaveFactors', arrayKey: 'microwaveFactorsArr' },
        { field: 'noiseFactors', arrayKey: 'noiseFactorsArr' },
        { field: 'powerFrequencyElectric', arrayKey: 'powerFrequencyElectricArr' },
        { field: 'SiO2Factors', arrayKey: 'SiO2FactorsArr' },
        { field: 'ultraHighRadiationFactors', arrayKey: 'ultraHighRadiationFactorsArr' },
        { field: 'ultravioletFactors', arrayKey: 'ultravioletFactorsArr' },
        { field: 'ionizatioSourceFactors', arrayKey: 'ionizatioSourceFactorsArr' },
        { field: 'ionizatioRadialFactors', arrayKey: 'ionizatioRadialFactorsArr' },
      ];

      // 优化循环处理逻辑
      for (const item of stationInfo) {
        for (const { field, arrayKey } of factorMapping) {
          if (item[field] && item[field].formData && item[field].formData.length > 0) {
            const ele = item[field].formData;
            factorArrays[arrayKey] = _.uniqBy([ ...factorArrays[arrayKey], ...ele ], 'station');
          }
        }
      }

      // 构建返回数据
      const resultData = {
        persons,
        stationObj: factorArrays,
      };

      // 将结果缓存到Redis，缓存30分钟
      await ctx.service.redisData.setHarmStatisticsCache(ctx, EnterpriseID, resultData, 1800);

      // console.log(persons, '2222222222222');
      ctx.helper.renderSuccess(ctx, {
        data: resultData,
        fromCache: false,
      });
    } catch (err) {
      console.log(err, '4444444444');
      ctx.helper.renderFail(ctx, {
        data: err,
      });
    }
  }


  // stationCheck(ctx) {
  //   try {
  //     const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
  //     console.log('企业ID：', EnterpriseID);
  //     const rees
  //   } catch (err) {
  //     console.log(err, '获取岗位检测错误信息');
  //     ctx.helper.renderFail(ctx, {
  //       data: err,
  //       message: 'error',
  //     });

  //   }
  // }

  async jcTend(ctx) {
    try {
      const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
      // console.log('企业ID：', EnterpriseID);
      const jcAllYearResult = await ctx.service.db.find('CheckAssessment', { EnterpriseID });
      console.log('所有年的所有的检测数据：', jcAllYearResult);
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        data: err,
        message: '请求失败',
      });
    }
  }
  async whjcData(ctx) {
    try {
      // 第一次请求不携带参数，不知道那年有检测数据，通过查找CheckAssessment表时间逆序排序，
      // 找到体检年份，那第一个时间年份查找检测数据，再将所有的检测年份和第一次的检测数据一起返回前端，第二次请求的时候通过点击第一次返回前端的年份进行数据查找。注意如果CheckAssessment表没有查到数据，说明改企业没有检测数据   统计四年的检测结果做趋势图数据
      // console.log('检测数据年份参数:', ctx.query.year);
      // const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
      // const enterpriseIds = await ctx.service.employee.findSubCompany(EnterpriseID, 'include');
      const enterpriseIds = await ctx.helper.getScopeData('enterprise_ids');
      const time = await ctx.service.db.find('CheckAssessment', { EnterpriseID: { $in: enterpriseIds } }, { year: 1, jobHealthId: 1 }, { sort: { year: -1 } });
      if (time.length) {
        // const firstData = await ctx.model.CheckAssessment.find({ EnterpriseID, $expr: { $eq: [{ $year: '$year' }, parseInt(ctx.query.year) ] } });
        const pipelineFirst = [
          { $match: { EnterpriseID: { $in: enterpriseIds } } },
          {
            $addFields: {
              newYear: {
                $toInt: {
                  $dateToString: {
                    date: '$year',
                    format: '%Y',
                    timezone: '+08:00',
                  },
                },
              },
            },
          },
          {
            $match: {
              newYear: parseInt(ctx.query.year),
            },
          },
        ];
        const firstData = await ctx.service.db.aggregate('CheckAssessment', pipelineFirst);
        const factorsArr = [];
        let hxpoint = 0;
        let hxExceed = 0;
        let dustpoint = 0;
        let dustExceed = 0;
        let wlpoint = 0;
        let wlExceed = 0;
        let fspoint = 0;
        let fsExceed = 0;
        const hx = {};
        const dust = {};
        const wl = {};
        const fs = {};
        let swpoint = 0;
        let swExceed = 0;
        const sw = {};
        for (let i = 0; i < firstData.length; i++) {
          const item = firstData[i];
          if (item.chemistryFactors) {
            for (let u = 0; u < item.chemistryFactors.formData.length; u++) {
              if (item.chemistryFactors.formData[u].checkResult === '不符合' || item.chemistryFactors.formData[u].checkResult === '不合格') {
                hxExceed++;
              } else {
                hxpoint++;
              }
            }
          }
          if (item.dustFactors) {
            for (let u = 0; u < item.dustFactors.formData.length; u++) {
              if (item.dustFactors.formData[u].checkResult === '不符合' || item.dustFactors.formData[u].checkResult === '不合格') {
                dustExceed++;
              } else {
                dustpoint++;
              }
            }
          }
          if (item.noiseFactors) {
            for (let u = 0; u < item.noiseFactors.formData.length; u++) {
              if (item.noiseFactors.formData[u].checkResult === '不符合' || item.noiseFactors.formData[u].checkResult === '不合格') {
                wlExceed++;
              } else {
                wlpoint++;
              }
            }
          }
          if (item.heatFactors) {
            for (let u = 0; u < item.heatFactors.formData.length; u++) {
              if (item.heatFactors.formData[u].checkResult === '不符合' || item.heatFactors.formData[u].checkResult === '不合格') {
                wlExceed++;
              } else {
                wlpoint++;
              }
            }
          }
          if (item.handBorneVibrationFactors) {
            for (let u = 0; u < item.handBorneVibrationFactors.formData.length; u++) {
              if (item.handBorneVibrationFactors.formData[u].checkResult === '不符合' || item.handBorneVibrationFactors.formData[u].checkResult === '不合格') {
                wlExceed++;
              } else {
                wlpoint++;
              }
            }
          }
          if (item.highFrequencyEleFactors) {
            for (let u = 0; u < item.highFrequencyEleFactors.formData.length; u++) {
              if (item.highFrequencyEleFactors.formData[u].checkResult === '不符合' || item.highFrequencyEleFactors.formData[u].checkResult === '不合格') {
                wlExceed++;
              } else {
                wlpoint++;
              }
            }
          }
          if (item.laserFactors) {
            for (let u = 0; u < item.laserFactors.formData.length; u++) {
              if (item.laserFactors.formData[u].checkResult === '不符合' || item.laserFactors.formData[u].checkResult === '不合格') {
                wlExceed++;
              } else {
                wlpoint++;
              }
            }
          }
          if (item.microwaveFactors) {
            for (let u = 0; u < item.microwaveFactors.formData.length; u++) {
              if (item.microwaveFactors.formData[u].checkResult === '不符合' || item.microwaveFactors.formData[u].checkResult === '不合格') {
                wlExceed++;
              } else {
                wlpoint++;
              }
            }
          }
          if (item.powerFrequencyElectric) {
            for (let u = 0; u < item.powerFrequencyElectric.formData.length; u++) {
              if (item.powerFrequencyElectric.formData[u].checkResult === '不符合' || item.powerFrequencyElectric.formData[u].checkResult === '不合格') {
                wlExceed++;
              } else {
                wlpoint++;
              }
            }
          }
          if (item.ultraHighRadiationFactors) {
            for (let u = 0; u < item.ultraHighRadiationFactors.formData.length; u++) {
              if (item.ultraHighRadiationFactors.formData[u].checkResult === '不符合' || item.ultraHighRadiationFactors.formData[u].checkResult === '不合格') {
                wlExceed++;
              } else {
                wlpoint++;
              }
            }
          }
          if (item.ultravioletFactors) {
            for (let u = 0; u < item.ultravioletFactors.formData.length; u++) {
              if (item.ultravioletFactors.formData[u].checkResult === '不符合' || item.ultravioletFactors.formData[u].checkResult === '不合格') {
                wlExceed++;
              } else {
                wlpoint++;
              }
            }
          }
          if (item.ionizatioSourceFactors) {
            for (let u = 0; u < item.ionizatioSourceFactors.formData.length; u++) {
              if (item.ionizatioSourceFactors.formData[u].checkResult === '不符合' || item.ionizatioSourceFactors.formData[u].checkResult === '不合格') {
                fsExceed++;
              } else {
                fspoint++;
              }
            }
          }
          if (item.ionizatioRadialFactors) {
            for (let u = 0; u < item.ionizatioRadialFactors.formData.length; u++) {
              if (item.ionizatioRadialFactors.formData[u].checkResult === '不符合' || item.ionizatioRadialFactors.formData[u].checkResult === '不合格') {
                fsExceed++;
              } else {
                fspoint++;
              }
            }
          }
          if (item.biologicalFactors) {
            for (let u = 0; u < item.biologicalFactors.formData.length; u++) {
              if (item.biologicalFactors.formData[u].checkResult === '不符合' || item.biologicalFactors.formData[u].checkResult === '不合格') {
                swExceed++;
              } else {
                swpoint++;
              }
            }
          }
        }
        hx.hege = hxpoint;
        hx.bhg = hxExceed;
        dust.hege = dustpoint;
        dust.bhg = dustExceed;
        sw.hege = swpoint;
        sw.bhg = swExceed;
        wl.hege = wlpoint;
        wl.bhg = wlExceed;
        fs.hege = fspoint;
        fs.bhg = fsExceed;
        factorsArr.push({ hx }, { dust }, { sw }, { wl }, { fs });
        const yearNow = new Date().getFullYear();
        const yearsAll = [ yearNow - 3, yearNow - 2, yearNow - 1, yearNow ];
        const pipeline = [
          { $match: { EnterpriseID: { $in: enterpriseIds } } },
          {
            $addFields: {
              newYear: {
                $toInt: {
                  $dateToString: {
                    date: '$year',
                    format: '%Y',
                    timezone: '+08:00',
                  },
                },
              },
            },
          },
          {
            $match: {
              newYear: { $in: yearsAll }, // createTime
            },
          },
          { $sort: { newYear: 1 } },
          // {
          //   $project: {
          //     totalpoint: 1,
          //     totalexceed: 1,
          //     EnterpriseID: 1,
          //     year: 1,
          //     jobHealthId: 1,
          //     newYear: 1,
          //   },
          // },
        ];
        const dataAll = await ctx.service.db.aggregate('CheckAssessment', pipeline);
        const arr = dataAll.map(item => {
          const statistics = []; // 危害因素检测数据 统计
          let totalNum = 0,
            totalAbnormalNum = 0;
          const total = new Set(),
            abnormal = new Set();
            // 检测合格率计算不包括 SiO2Factors ,不统计放射的数据 ionizatioSourceFactors ionizatioRadialFactors
          const checkResults = [ 'chemistryFactors', 'dustFactors', 'noiseFactors', 'microwaveFactors', 'powerFrequencyElectric', 'ultraHighRadiationFactors', 'ultravioletFactors', 'biologicalFactors', 'handBorneVibrationFactors', 'heatFactors', 'highFrequencyEleFactors', 'laserFactors' ];
          for (const key of checkResults) {
            let abnormalNum = 0; // 该类别的超标点数
            if (
              typeof item[key] === 'object' && item[key]
            ) {
              if (
                item[key].formData &&
                item[key].formData.length
              ) {
                totalNum += item[key].formData.length;
                for (const ele of item[key].formData) {
                  let workType = '';
                  if (ele.station) {
                    workType = (ele.workspace || '') + ele.station;
                  } else {
                    workType = (ele.checkAddressDetail || ele.checkAddress || '') + (ele.workType || '');
                  }
                  total.add(workType);
                  if (ele.checkResult === '不符合' || ele.checkResult === '不合格') {
                    abnormal.add(workType);
                    abnormalNum++;
                    totalAbnormalNum++;
                  }
                }
              }
              // 处理危害因素检测数据  点数 超标点数
              statistics.push({
                name: item[key].name + '点数',
                total: item[key].formData.length,
                abnormal: abnormalNum,
              });
            }
          }
          return { [item.newYear]: { totalNum, totalAbnormalNum } };
        });
        // console.log('arr:', arr);
        // 初始化累加结果的对象
        const result = {};
        yearsAll.forEach(year => {
          result[year] = { totalNum: 0, totalAbnormalNum: 0 };
        });
        // 遍历数组，累加每个年份对应的 totalNum 和 totalAbnormalNum
        arr.forEach(item => {
          for (const year in item) {
            result[year].totalNum += item[year].totalNum;
            result[year].totalAbnormalNum += item[year].totalAbnormalNum;
          }
        });
        const jcTrendarr = [];
        for (const year in result) {
          const percent = result[year].totalNum ? ((result[year].totalNum - result[year].totalAbnormalNum) / result[year].totalNum * 100).toFixed(2) : 0;
          jcTrendarr.push(percent);
        }
        ctx.helper.renderSuccess(ctx, {
          data: {
            time: yearsAll,
            factorsArr,
            jcTrendarr,
          },
          message: 'success',
        });
      } else {
        ctx.helper.renderSuccess(ctx, {
          data: {
            factorsArr: [],
            jcTrendarr: [],
          },
          message: '暂无检测数据',
        });
      }
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        data: err,
        message: '请求失败',
      });
    }
  }
  async getEducationData(ctx) {
    try {
      // const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
      // let majorPerson = 0; // 主要负责人
      // let managerPerson = 0; // 管理人员
      // let trainLobarPerson = 0;// 参加培训的劳动者人员
      // const trainLobarPersonName = [];// 参加培训的劳动者人员名字
      // let trainManagePerson = 0; // 参与培训的管理人员
      // let trainMajorPerson = 0; // 参与培训的主要负责人
      // let LobarHours = 0; // 劳动者人员人学时
      // let MajorHours = 0; // 主要负责人学时
      // let ManageHours = 0; // 管理人员学时
      // const totalLaborPerson = [];
      // const allperson = await ctx.model.Roles.findOne({ EnterpriseID });
      // for (let i = 0; i < allperson.formData.length; i++) {
      //   const item = allperson.formData[i];
      //   if (item.role === '领导小组组长') {
      //     majorPerson = _.flattenDepth(item.userId, 1);
      //   }
      //   if (item.role === '领导小组副组长') {
      //     majorPerson = _.unionWith(majorPerson, _.flattenDepth(item.userId, 1), _.isEqual);
      //   }
      //   if (item.role === '专职管理人员') {
      //     managerPerson = _.flattenDepth(item.userId, 1);
      //   }
      //   if (item.role === '兼职管理人员') {
      //     managerPerson = _.unionWith(managerPerson, _.flattenDepth(item.userId, 1), _.isEqual);
      //   }
      // }
      // const trainAllPerson = await ctx.model.Propagate.find({ EnterpriseID });
      // // console.log('所有参与培训的人员：', trainAllPerson);
      // for (let i = 0; i < trainAllPerson.length; i++) {
      //   const item = trainAllPerson[i];
      //   if (item.type === '劳动者培训') {
      //     trainLobarPerson = item.personnel;
      //     LobarHours = item.hours;
      //     for (let i = 0; i < trainLobarPerson.length; i++) {
      //       const name = trainLobarPerson[i].pop();
      //       trainLobarPersonName.push(name);
      //     }
      //   }
      //   if (item.type === '管理人员培训') {
      //     trainManagePerson = item.personnel;
      //     ManageHours = item.hours;
      //   }
      //   if (item.type === '负责人培训') {
      //     trainMajorPerson = item.personnel;
      //     MajorHours = item.hours;
      //   }
      // }
      // // console.log('员工的名字：', trainLobarPersonName);
      // // console.log('参与培训的劳动者：', trainLobarPerson);
      // // console.log('参与培训的管理人员：', trainManagePerson);
      // // console.log('参与培训的主要负责人：', trainMajorPerson);
      // let employeeTrainingPerson = []; // 所有参与培训的员工 （上面的那些是培训记录）
      // let requiredCoursesHours = 0;
      // const employeeTrainingPersonName = []; // 所有参与培训的员工的名字
      // // const employeeTraining = await ctx.model.EmployeesTrainingPlan.find({ EnterpriseID }).populate([{
      // //   path: 'authorID',
      // //   select: 'name',
      // // }]).populate([{ path: 'employees', select: 'name' }]);
      // // for (let i = 0; i < employeeTraining.length; i++) {
      // //   const item = employeeTraining[i];
      // //   employeeTrainingPerson = item.employees;
      // //   for (let i = 0; i < employeeTrainingPerson.length; i++) {
      // //     const name = employeeTrainingPerson[i].name;
      // //     employeeTrainingPersonName.push(name);
      // //   }
      // //   requiredCoursesHours = item.requiredCoursesHours;
      // // }
      // // 计算员工培训率 需要去重  计算培训总课时不需要去重
      // const totallaborName = _.unionWith(employeeTrainingPersonName, trainLobarPersonName, _.isEqual); // 去重后所有参与培训的员工
      // // 员工总学时
      // const totalLaborsHours = employeeTrainingPersonName.length * requiredCoursesHours + LobarHours * trainLobarPersonName.length;
      // console.log(totalLaborsHours, 'totalLaborsHours]]]]]]]]]]');
      // // 主要负责人所有学时
      // const totalMajorHours = trainMajorPerson * MajorHours;
      // // 管理人员所有学时
      // const totalManageHours = trainManagePerson.length * ManageHours;
      // console.log(totalManageHours, 'totalManageHours==', trainManagePerson.length, ManageHours);
      // // console.log('主要负责人人数：', majorPerson);
      // // console.log('管理人员人数：', managerPerson);
      // const data = {
      //   totalManageHours, // 管理人员所有学时
      //   totalMajorHours, // 主要负责人所有学时
      //   totalLaborsHours, // 员工总学时
      //   trainManagePerson, // 培训管理人员
      //   trainMajorPerson, // 培训主要负责人
      //   totallaborName, // 培训员工
      //   majorPerson: majorPerson.length, // 主要负责人总人数
      //   managerPerson: managerPerson.length, // 管理人员人数
      // };

      const enterpriseIds = await ctx.helper.getScopeData('enterprise_ids');
      // console.log('教育培训年份：', ctx.query.year);
      const majorPerson = []; // 主要负责人总数
      const managerPerson = []; // 管理人员总数
      const trainLobarPerson = [];// 参加培训的劳动者人员
      const trainManagePerson = []; // 参与培训的管理人员
      const trainMajorPerson = []; // 参与培训的主要负责人
      let LobarHours = 0; // 劳动者人员人学时
      let MajorHours = 0; // 主要负责人学时
      let ManageHours = 0; // 管理人员学时
      const allperson = await ctx.service.db.find('Roles', { EnterpriseID: { $in: enterpriseIds } });
      for (let j = 0; j < allperson.length; j++) {
        const formData = allperson[j].formData;
        for (let i = 0; i < formData.length; i++) {
          const item = formData[i];
          if (item.role === '领导小组组长') {
            majorPerson.push(...(item.userId.map(ele => ele[1])));
          }
          if (item.role === '领导小组副组长' && ctx.app.config.branch !== 'by') {
            managerPerson.push(...item.userId.map(ele => ele[1]));
          }
          if (item.role === '专职管理人员') {
            managerPerson.push(...item.userId.map(ele => ele[1]));
          }
          if (item.role === '兼职管理人员') {
            managerPerson.push(...item.userId.map(ele => ele[1]));
          }
        }
      }
      const trainAllPerson = await ctx.service.db.find('Propagate', { EnterpriseID: { $in: enterpriseIds }, $expr: { $eq: [{ $year: '$implementData' }, parseInt(ctx.query.year) ] } });
      // console.log('所有参与培训的人员：', trainAllPerson);
      for (let i = 0; i < trainAllPerson.length; i++) {
        const item = trainAllPerson[i];
        if (item.type === '劳动者培训') {
          LobarHours += +item.hours * item.personnel.length || 0;
          for (let i = 0; i < item.personnel.length; i++) {
            const unitCode = item.personnel[i].unitCode;
            trainLobarPerson.push(unitCode);
          }
        }
        if (item.type === '管理人员培训') {
          ManageHours += +item.hours * item.personnel.length || 0;
          for (let i = 0; i < item.personnel.length; i++) {
            const unitCode = item.personnel[i].unitCode;
            trainManagePerson.push(unitCode);
          }
        }
        if (item.type === '负责人培训') {
          MajorHours += +item.hours;
          for (let i = 0; i < item.personnel.length; i++) {
            const unitCode = item.personnel[i].unitCode;
            trainMajorPerson.push(unitCode);
          }
        }
      }
      const data = {
        trainLobarPerson, // 参与培训的员工
        LobarHours, // 员工学士
        trainManagePerson, // 参与培训的管理人员
        ManageHours, // 管理人员学时
        trainMajorPerson, // 参与培训的主要负责人
        MajorHours, // 主要负责人学时
        majorPerson, // 负责人
        managerPerson, // 管理人员
      };
      ctx.helper.renderSuccess(ctx, {
        data,
      });
    } catch (err) {
      ctx.helper.renderSuccess(ctx, {
        data: err,
      });
    }
  }
}

module.exports = AdminUserController;
