const Controller = require('egg').Controller;
// const multiparty = require('multiparty');
const path = require('path');
const fs = require('fs');
const moment = require('moment');
class StandardLibraryController extends Controller {
  // 批量上传文件
  async uploadPdf() {
    const { ctx, app } = this;
    try {
      const { EnterpriseID, _id } = ctx.session.adminUserInfo;
      const stream = await ctx.getFileStream();
      const name = stream.fields.name;
      const regex1 = /^(.*?)([\u4e00-\u9fa5].*)\.pdf$/i;
      const regex2 = /^(.*?)([\u4e00-\u9fa5]?.*)\.pdf$/i;
      if (!regex1.test(name) && !regex2.test(name)) {
        ctx.helper.renderCustom(ctx, {
          message: '文件名格式不正确',
          data: '文件名格式不正确',
          status: 500,
        });
        return;
      }
      const timestamp = new Date().getTime();
      const randomCode = Math.floor(Math.random() * 1000000)
        .toString()
        .padStart(6, '0');
      const filename = `/${timestamp}${randomCode}.pdf`;
      const writePath = path.join(
        app.config.standardLibrary_path,
        `/${filename}`
      );
      const standardInfo = {
        downLoadPath: filename,
        uploader: _id,
        EnterpriseID,
      };
      if (regex1.test(name)) {
        const match = name.match(regex1);
        standardInfo.standardNumber = match[1].trim();
        standardInfo.standardName = match[2];
      } else if (regex2.test(name)) {
        const match = name.match(regex2);
        standardInfo.standardNumber = match[2].trim();
        standardInfo.standardName = '';
      }
      // const res = await this.ctx.service.standardLibrary.checkStandardNumber(
      //   standardInfo.standardNumber
      // );
      // if (res.length > 0) {
      //   ctx.helper.renderCustom(ctx, {
      //     message: '标准号已存在',
      //     data: '标准号已存在',
      //     status: 500,
      //   });
      //   return;
      // }
      await ctx.helper.pipe({
        readableStream: stream,
        target: writePath,
      });
      await this.ctx.service.standardLibrary.createMany(standardInfo);
      ctx.helper.renderSuccess(ctx, {
        status: 200,
        message: '上传成功',
      });
    } catch (error) {
      ctx.helper.renderCustom(ctx, {
        message: error,
        status: 500,
      });
    }
  }

  // 获取标准库列表
  async getStandardList() {
    const { ctx, app } = this;
    try {
      const data = ctx.request.body;
      const res = await this.service.standardLibrary.getStandardList(data);
      // res.list = res.list.map(item => {
      //   return {
      //     ...item,
      //     publishDate: moment(item.publishDate).format('YYYY-MM-DD'),
      //     implementDate: moment(item.implementDate).format('YYYY-MM-DD'),
      //     path: `/static${app.config.standardLibrary_http_path}/${item.downLoadPath}`,
      //   };
      // });
      if (res && res.list && res.list.length > 0) {
        for (let i = 0; i < res.list.length; i++) {
          if (res.list[i].publishDate) {
            res.list[i].publishDate = moment(res.list[i].publishDate).format(
              'YYYY-MM-DD'
            );
          }
          if (res.list[i].implementDate) {
            res.list[i].implementDate = moment(res.list[i].implementDate).format(
              'YYYY-MM-DD'
            );
          }
          res.list[i].path = await ctx.helper.concatenatePath({
            path: `${app.config.standardLibrary_http_path}${res.list[i].downLoadPath}`,
          });
        }
      }
      ctx.helper.renderSuccess(ctx, {
        status: 200,
        data: res,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 编辑标准库信息
  async editStandardInfo() {
    const { ctx, app } = this;
    try {
      const data = ctx.request.body;
      const { _id, EnterpriseID } = ctx.session.adminUserInfo;
      data.uploader = _id;
      data.EnterpriseID = EnterpriseID;
      const res = await this.service.standardLibrary.editStandardInfo(data);
      if (res === '标准编号已存在') {
        ctx.helper.renderFail(ctx, {
          message: res,
        });
        return;
      }
      res.publishDate && (res.publishDate = moment(res.publishDate).format(
        'YYYY-MM-DD'
      ));
      res.implementDate &&
        (res.implementDate = moment(res.implementDate).format('YYYY-MM-DD'));
      res.path = `/static${app.config.standardLibrary_http_path}/${res.downLoadPath}`;
      ctx.helper.renderSuccess(ctx, {
        status: 200,
        message: '编辑成功',
        data: res,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 删除标准库信息
  async delStandardInfo() {
    const { ctx, app } = this;
    try {
      const data = ctx.request.body;
      const deletePath = path.join(
        app.config.standardLibrary_path,
        `${data.downLoadPath}`);
      if (fs.existsSync(deletePath)) {
        await fs.unlinkSync(deletePath);
      }
      await this.service.standardLibrary.delStandardInfo(data);
      ctx.helper.renderSuccess(ctx, {
        status: 200,
        message: '删除成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 替换文件
  async replaceFile() {
    const { ctx, app } = this;
    try {
      const stream = await ctx.getFileStream();
      const { EnterpriseID, _id } = ctx.session.adminUserInfo;

      const { data } = stream.fields;
      console.log(stream.fields, JSON.parse(data), 123);
      const name = stream.filename;
      // const replacePath = path.join(
      //   app.config.standardLibrary_path,
      //   JSON.parse(data).downLoadPath
      // );
      const timestamp = new Date().getTime();
      const randomCode = Math.floor(Math.random() * 1000000)
        .toString()
        .padStart(6, '0');
      const newFilePath = `/${timestamp}${randomCode}.pdf`;
      const newPath = path.join(
        app.config.standardLibrary_path,
        `/${newFilePath}`
      );
      const regex1 = /^(.*?)([\u4e00-\u9fa5].*)\.pdf$/i;
      const regex2 = /^(.*?)([\u4e00-\u9fa5]?.*)\.pdf$/i;
      if (!regex1.test(name) && !regex2.test(name)) {
        ctx.helper.renderCustom(ctx, {
          message: '文件名格式不正确',
          data: '文件名格式不正确',
          status: 500,
        });
        return;
      }
      let standardInfo = {};
      standardInfo = {
        downLoadPath: newFilePath,
        uploader: _id,
        EnterpriseID,
      };
      if (regex1.test(name)) {
        const match = name.match(regex1);
        standardInfo.standardNumber = match[1].trim();
        standardInfo.standardName = match[2];
      } else if (regex2.test(name)) {
        const match = name.match(regex2);
        standardInfo.standardNumber = match[2].trim();
        standardInfo.standardName = '';
      }
      const result = await this.ctx.service.standardLibrary.checkStandardNumber(
        standardInfo.standardNumber,
        JSON.parse(data)._id
      );
      if (result.length > 0) {
        ctx.helper.renderCustom(ctx, {
          message: '标准号已存在',
          data: '标准号已存在',
          status: 500,
        });
        return;
      }

      // 删除旧文件
      // if (fs.existsSync(replacePath)) {
      //   fs.unlinkSync(replacePath);
      // }
      // const writeStream = fs.createWriteStream(newPath);

      // await awaitWriteStream(stream.pipe(writeStream)); // 将文件流管道到写入流
      await ctx.helper.pipe({
        readableStream: stream,
        target: newPath,
      });
      const res = await this.service.standardLibrary.replaceFile(
        standardInfo,
        JSON.parse(data)._id
      );
      res.publishDate && (res.publishDate = moment(res.publishDate).format(
        'YYYY-MM-DD'
      ));
      res.publishDate && (res.implementDate = moment(res.implementDate).format('YYYY-MM-DD'));
      res.path = `/static${app.config.standardLibrary_http_path}/${res.downLoadPath}`;
      ctx.helper.renderSuccess(ctx, {
        status: 200,
        message: '替换成功',
        data: res,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

}
module.exports = StandardLibraryController;
