const path = require('path');
const fs = require('fs');
const awaitStreamReady = require('await-stream-ready').write;
const streamWormhole = require('stream-wormhole');
const { mkdirp } = require('mkdirp');
const shortid = require('shortid');

const WarnNoticeController = {
  async statistics(ctx, EnterpriseID) {
    const doc = await ctx.service.db.findOne('WarnNotice', { EnterpriseID }, { tableData: 1 });
    if (doc && doc.tableData.length > 0) {
      await ctx.service.filesCompleteness.update({ $set: { warnNotice: { completion: 100 } } });
    } else {
      await ctx.service.filesCompleteness.update({ $set: { warnNotice: { completion: 0 } } });
    }
  },
  async index(ctx) {
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    const res = await ctx.service.warnNotice.index(EnterpriseID); // 数据表里存入的数据
    const doc = await ctx.service.warnNotice.autoBuildWarnSign({ id: EnterpriseID }); // 根据车间岗位整理得出的数据
    if (res) {
      res.tableData.forEach(item => {
        if (item.custom) {
          doc.push(item);
        }
        for (let i = 0; i < doc.length; i++) {
          doc[i].id = shortid.generate();
          if (item.configPlaceID === doc[i].configPlaceID && item.workPlaceID === doc[i].workPlaceID) {
            const fields = [ 'putUpStatus', 'putUpDate', 'warnContent', 'signNumber', 'informProject', 'responPerson', 'notificationCard', '_id' ];
            fields.forEach(field => {
              doc[i][field] = item[field];
            });
          }
        }
      });
    }
    const params = ctx.query;
    const start = (params.current - 1) * params.pageSize;
    const end = params.current * params.pageSize;
    const newDoc = doc.slice(start, end);

    const url = '/static/upload/images/notificationcard/';
    const data = {
      url,
      doc: newDoc,
      allNotificationCard: res ? res.notificationCard : [],
      total: doc.length,
    };
    ctx.helper.renderSuccess(ctx, {
      data,
      status: 200,
    });
  },

  // 上传告知卡
  async uploadCard(ctx, app) {
    // const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    const stream = await ctx.getFileStream();
    // const mimeType = stream.mimeType;
    // // 只允许上传pdf、docx文件
    // if (!mimeType) {
    //   if (!mimeType.includes('pdf')) {
    //     throw new Error('只允许上传pdf、docx文件');
    //   }
    //   if (!mimeType.includes('vnd.openxmlformats-officedocument.wordprocessingml.document')) {
    //     throw new Error('只允许上传pdf、docx文件');
    //   }
    // }
    // 危害因素为上传的文件名去除后缀
    const harmFactor = stream.filename.split('.')[0];
    const filename = 'notification_card' + Math.random().toString(36).substr(8) + '_' + path.extname(stream.filename);
    // 图片最后保存的路径
    const fullPath = path.join(app.config.image_upload_path, 'notificationcard');
    await mkdirp(fullPath).then(async () => {
      const target = fullPath + '/' + filename;
      const writeStream = fs.createWriteStream(target);
      try {
        await awaitStreamReady(stream.pipe(writeStream));
      } catch (err) {
        await streamWormhole(stream);
      }
      ctx.body = {
        uploaded: true,
        url: '/static' + app.config.image_upload_http_path + '/notificationcard/' + filename,
      };
      // 上传成功后，将文件信息保存到数据库
      const params = {
        harmFactor,
        filePath: filename,
        fileName: stream.filename,
        // 上传的时间
        time: new Date(),
        fileType: stream.mimeType,
      };
      await ctx.service.warnNotice.uploadCard(params);
    });
  },

  // 保存提交上来的表单数据
  async create(ctx) {
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    const reqData = ctx.request.body;
    const result = []; // 定义result数组
    const doc = await ctx.service.warnNotice.find(EnterpriseID);
    if (!doc) {
      await ctx.service.warnNotice.createDoc(EnterpriseID);
    }
    console.log('前端请求数据：', reqData);
    for (let i = 0; i < reqData.length; i++) {
      const newdata = Object.assign(reqData[i]);
      // 处理数组字段，转换为字符串格式存储
      if (Array.isArray(newdata.warnContent)) {
        newdata.warnContent = newdata.warnContent.join(',');
      }
      if (Array.isArray(newdata.informProject)) {
        newdata.informProject = newdata.informProject.join(',');
      }
      // 调用create方法创建新数据
      const res = await ctx.service.warnNotice.create(newdata, EnterpriseID);
      console.log('创建结果：', res);
      result.push(res);
    }
    try {
      this.statistics(ctx, EnterpriseID);
    } catch (error) {
      throw error;
    }
    // await ctx.service.adminUser.updateCompleteList({ EnterpriseID, year: reqData.year, item: 'WarnNotice', type: 'add' });
    ctx.helper.renderSuccess(ctx, {
      message: 'success',
      status: 200,
    });
  },
  // 更新数据
  async update(ctx) {
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    const reqData = ctx.request.body;
    const result = [];
    const doc = await ctx.service.warnNotice.find(EnterpriseID);
    if (!doc) {
      await ctx.service.warnNotice.createDoc(EnterpriseID);
    }
    console.log('前端请求数据：', reqData);
    for (let i = 0; i < reqData.length; i++) {
      const newdata = Object.assign(reqData[i]);
      // 处理数组字段，转换为字符串格式存储
      if (Array.isArray(newdata.warnContent)) {
        newdata.warnContent = newdata.warnContent.join(',');
      }
      if (Array.isArray(newdata.informProject)) {
        newdata.informProject = newdata.informProject.join(',');
      }
      // 调用update方法而不是create方法
      const res = await ctx.service.warnNotice.update(newdata, EnterpriseID);
      console.log('更新结果：', res);
      result.push(res);
    }

    try {
      this.statistics(ctx, EnterpriseID);
    } catch (error) {
      throw error;
    }
    // await ctx.service.adminUser.updateCompleteList({ EnterpriseID, year: reqData.year, item: 'WarnNotice', type: 'add' });
    ctx.helper.renderSuccess(ctx, {
      message: 'success',
      status: 200,
    });
  },
  async delete(ctx) {
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    const ids = ctx.request.body;
    const res = await ctx.service.warnNotice.delete(EnterpriseID, ids);
    const doc = await ctx.service.warnNotice.index(EnterpriseID); // 数据表里存入的数据
    // if (doc.tableData.length === 0) {
    //   await ctx.service.adminUser.updateCompleteList({ EnterpriseID, year: res.year, item: 'WarnNotice', type: 'sub' });
    // }
    const doc2 = await ctx.service.warnNotice.autoBuildWarnSign(EnterpriseID); // 根据车间岗位整理得出的数据
    if (doc) {
      doc.tableData.forEach(item => {
        for (let i = 0; i < doc2.length; i++) {
          if (item.configPlaceID === doc2[i].configPlaceID) {
            const fields = [ 'putUpStatus', 'putUpDate', 'warnContent', 'signNumber', 'informProject', 'responPerson', 'notificationCard', '_id' ];
            fields.forEach(field => {
              doc2[i][field] = item[field];
            });
          }
        }
      });
    }

    if (res) {
      try {
        this.statistics(ctx, EnterpriseID);
      } catch (error) {
        throw error;
      }
      ctx.helper.renderSuccess(ctx, {
        message: 'success',
        status: 200,
        data: doc2,
      });
    }
  },
  async copyData(ctx) {
    const { _id, year } = ctx.request.body;
    await ctx.service.warnNotice.copyDate(_id, year);
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    const res = await ctx.service.warnNotice.index(EnterpriseID);
    // await ctx.service.adminUser.updateCompleteList({ EnterpriseID, year, item: 'WarnNotice', type: 'add' });
    ctx.helper.renderSuccess(ctx, {
      status: 200,
      message: 'success getTableData',
      data: res,
    });
  },
  async getWarnList(ctx) {
    const params = ctx.query;
    const { res } = await ctx.service.warnNotice.index2(params); // 数据表里存入的数据
    const doc = await ctx.service.warnNotice.autoBuildWarnSign(params); // 根据车间岗位整理得出的数据
    const newRes = [];
    res.forEach(item => {
      newRes.push(item.tableData);
    });
    if (newRes.length > 0) {
      newRes.forEach(item => {
        if (item.custom) {
          doc.push(item);
        }
        for (let i = 0; i < doc.length; i++) {
          if (item.configPlaceID === doc[i].configPlaceID && item.workPlaceID === doc[i].workPlaceID) {
            const fields = [ 'putUpStatus', 'putUpDate', 'warnContent', 'signNumber', 'informProject', 'responPerson', 'notificationCard', '_id' ];
            fields.forEach(field => {
              doc[i][field] = item[field];
            });
          }
        }
      });
    }

    const start = (params.current - 1) * params.pageSize;
    const end = params.current * params.pageSize;
    const newDoc = doc.slice(start, end);

    ctx.helper.renderSuccess(ctx, {
      data: { doc: newDoc, total: doc.length },
      status: 200,
    });
  },

};

module.exports = WarnNoticeController;
