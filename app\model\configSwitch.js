/**
 * 开关表
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const ConfigSwitchSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    // 车间岗位初始化开关(必须拥有人员编码)
    initWorkshopStation: {
      type: Boolean,
      default: false,
    },
  }, {
    timestamps: true,
  });

  return mongoose.model('ConfigSwitch', ConfigSwitchSchema, 'configSwitchs');
};
