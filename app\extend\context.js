const pkg = require('../../package.json');
// const _ = require('lodash');

module.exports = {
  auditLog(title = '警告：未输入任何记录！', message = '警告：未输入任何记录！', level = 'debug') {
    const { session, request, logger } = this;
    try {
      const loggerTitle = '操作记录：',
        userInfo = session.adminUserInfo,
        header = request.header,
        json = {
          title,
          userID: (userInfo && userInfo._id) || '暂未获取到当前用户ID！',
          EnterpriseID: (userInfo && userInfo.EnterpriseID) || '暂未获取到当前用户相关企业ID！',
          message,
          userAgent: {
            IP: header['x-real-ip'] || header.host,
            AGENT: header['user-agent'],
          },
        };
      switch (level) {
        case 'debug':
          logger.debug(loggerTitle, json);
          break;
        case 'info':
          logger.info(loggerTitle, json);
          break;
        case 'warn':
          logger.warn(loggerTitle, json);
          break;
        case 'error':
          json.message = json.message.replace(/\n/g, '');
          logger.error(loggerTitle, new Error(JSON.stringify(json, null, 2)).message);
          break;
        default:
          break;
      }
    } catch (err) {
      logger.error('ctx.auditLog报错了', JSON.stringify(err.stack, null, 2));
    }
  },

  // 获取页脚信息
  async getEnvConfig() {
    const { ctx, config } = this;
    try {
      const envConfig = await ctx.curl(`${config.iServiceHost}/api/envConfig`, {
        dataType: 'json',
        data: { host: ctx.host },
      });
      return {
        recordNumber: envConfig.data.data.recordNumber || '',
        footerContent: envConfig.data.data.footerContent || [],
      };
    } catch (err) {
      return {
        recordNumber: '',
        footerContent: [],
      };
    }
  },
  // 获取加密公钥
  async getPublicKey() {
    const { ctx } = this;
    const publicKeyRes = await ctx.curl(
      `${this.config.iServiceHost}/crypto/getPublicKey`,
      {
        method: 'GET',
        dataType: 'json', // 返回的数据类型
        data: {},
      }
    );
    if (publicKeyRes.status !== 200) {
      throw new Error('获取公钥失败');
    }
    return { publicKey: publicKeyRes.data.data.publicKey };
  },
  async getSiteInfo() {
    const ctx = this;
    // console.log('--ctx.originalUrl--', ctx.originalUrl);
    const configs = await ctx.helper.reqJsonData('systemConfig/getConfig');
    const {
      siteName,
      siteDiscription,
      siteKeywords,
      siteAltKeywords,
      ogTitle,
      siteLogo,
      siteDomain,
    } = configs || [];

    const { title, des, keywords } = ctx.params;
    const pageTitle = title ? title + ' | ' + siteName : siteName;
    const discription = des ? des : siteDiscription;
    const key = keywords ? keywords : siteKeywords;
    const altkey = siteAltKeywords || '';
    return {
      title: pageTitle,
      logo: siteLogo,
      url: siteDomain,
      ogTitle,
      discription,
      key,
      altkey,
      configs: configs || [],
      version: pkg.version,
      lang: ctx.session.locale,
      router: ctx.originalUrl.split('/')[1],
    };
  },

  async getPageData() {
    const ctx = this;
    // const payload = ctx.params;
    const pageData = {
      pageType: ctx.pageType,
      staticRootPath: ctx.app.config.static.prefix,
      siteSeo: ctx.app.config.siteSeo,
      logoSrc: `/static/images/${ctx.app.config.branch}/logo.png`,
      ...(await this.getEnvConfig()), // 页脚信息
      navigation: ctx.app.config.navigation,
    };
    if (ctx.pageType === 'regulation') {
      // const data = await ctx.helper.reqJsonData('contentCategory/getOne', { name: '在线咨询' });
      // pageData.currentCateList = await ctx.helper.reqJsonData('contentCategory/getChildrenById', { typeId: data._id });
      // const item = await ctx.helper.reqJsonData('content/getList', { typeId: 'uel5pKPEo' });
      // console.log('---item---', item);
      // const item = await ctx.helper.reqJsonData('content/getList', { typeId: data._id });

    }
    await ctx.render(`${ctx.app.config.branch}/${ctx.tempPage}`, pageData);
  },
};
