videojs.addLanguage('sv', {
  ", opens captions settings dialog": ", öppnar dialogruta för textning",
  ", opens descriptions settings dialog": ", öppnar dialogruta för inställningar",
  ", opens subtitles settings dialog": ", öppnar dialogruta för undertexter",
  ", selected": ", vald",
  "A network error caused the media download to fail part-way.": "Ett nätverksfel gjorde att nedladdningen av videon avbröts.",
  "Audio Player": "Ljudspelare",
  "Audio Track": "Ljudspår",
  "Background": "Bakgrund",
  "Beginning of dialog window. Escape will cancel and close the window.": "Början av dialogfönster. Escape avbryter och stänger fönstret.",
  "Black": "Svart",
  "Blue": "Blå",
  "Caption Settings Dialog": "Dialogruta för textningsinställningar",
  "Captions": "Text på",
  "Casual": "Casual",
  "Chapters": "<PERSON><PERSON><PERSON>",
  "Close": "<PERSON><PERSON><PERSON>",
  "Close Modal Dialog": "Stäng dialogruta",
  "Current Time": "Aktuell tid",
  "Cyan": "<PERSON><PERSON>",
  "Depressed": "Deprimerad",
  "Descriptions": "Beskrivningar",
  "Done": "Klar",
  "Dropshadow": "DropSkugga",
  "Duration": "Total tid",
  "End of dialog window.": "Slutet av dialogfönster.",
  "Font Family": "Typsnittsfamilj",
  "Font Size": "Textstorlek",
  "Fullscreen": "Fullskärm",
  "Green": "Grön",
  "LIVE": "LIVE",
  "Loaded": "Laddad",
  "Magenta": "Magenta",
  "Modal Window": "dialogruta",
  "Monospace Sans-Serif": "Monospace Sans-Serif",
  "Monospace Serif": "Monospace Serif",
  "Mute": "Ljud av",
  "No compatible source was found for this media.": "Det gick inte att hitta någon kompatibel källa för den här videon.",
  "Non-Fullscreen": "Ej fullskärm",
  "None": "Ingen",
  "Opaque": "Opak",
  "Pause": "Pausa",
  "Play": "Spela",
  "Play Video": "Spela upp video",
  "Playback Rate": "Uppspelningshastighet",
  "Progress": "Förlopp",
  "Progress Bar": "förloppsmätare",
  "Proportional Sans-Serif": "Proportionell Sans-Serif",
  "Proportional Serif": "Proportionell Serif",
  "Raised": "Raised",
  "Red": "Röd",
  "Remaining Time": "Återstående tid",
  "Replay": "Spela upp igen",
  "Reset": "Återställ",
  "Script": "Manus",
  "Seek to live, currently behind live": "Återgå till live, uppspelningen är inte live",
  "Seek to live, currently playing live": "Återgå till live, uppspelningen är live",
  "Semi-Transparent": "Semi-transparent",
  "Small Caps": "Small-Caps",
  "Stream Type": "Strömningstyp",
  "Subtitles": "Text på",
  "Text": "Text",
  "Text Edge Style": "Textkantstil",
  "The media could not be loaded, either because the server or network failed or because the format is not supported.": "Det gick inte att ladda videon, antingen på grund av ett server- eller nätverksfel, eller för att formatet inte stöds.",
  "The media is encrypted and we do not have the keys to decrypt it.": "Mediat är krypterat och vi har inte nycklarna för att dekryptera det.",
  "The media playback was aborted due to a corruption problem or because the media used features your browser did not support.": "Uppspelningen avbröts på grund av att videon är skadad, eller också för att videon använder funktioner som din webbläsare inte stöder.",
  "This is a modal window": "Det här är ett dialogruta",
  "This modal can be closed by pressing the Escape key or activating the close button.": "Den här dialogrutan kan stängas genom att trycka på Escape-tangenten eller stäng knappen.",
  "Transparent": "Transparent",
  "Uniform": "Uniform",
  "Unmute": "Ljud på",
  "Video Player": "Videospelare",
  "Volume Level": "Volymnivå",
  "White": "Vit",
  "Window": "Fönster",
  "Yellow": "Gul",
  "You aborted the media playback": "Du har avbrutit videouppspelningen.",
  "captions off": "Text av",
  "captions settings": "textningsinställningar",
  "descriptions off": "beskrivningar av",
  "descriptions settings": "beskrivningsinställningar",
  "progress bar timing: currentTime={1} duration={2}": "{1} av {2}",
  "restore all settings to the default values": "återställ alla inställningar till standardvärden",
  "subtitles off": "Text av",
  "subtitles settings": "undertextsinställningar",
  "{1} is loading.": "{1} laddar."
});