<template>
  <div class="page">
    <div slot="header" class="header" style="padding: 0 0">
      <div class="header-title" style="margin-left: -3px">警示标识管理</div>
      <HelpBtn
        class="helpBtn"
        title="警示标识管理教程"
        videoSrc="/static/video/警示告知管理.mp4"
      />
      <el-select
        v-if="isShowSelect"
        v-model="value"
        filterable
        clearable
        @clear="backHome"
        size="small"
        placeholder="查看更多公司"
        @change="changeCompany"
        style="position: relative; right: -20px"
      >
        <el-option
          v-for="item in subCompanyList"
          :key="item.id"
          :label="item.cname"
          :value="item.id"
        >
        </el-option>
      </el-select>
      <el-button
        type="primary"
        size="small"
        @click="backHome"
        v-show="btnIsShow"
        style="margin-left: 40px"
        >返回</el-button
      >
    </div>
    <div class="tab">
      <!-- 页面骨架屏 -->
      <div v-if="pageLoading" class="skeleton-container">
        <div class="skeleton-header">
          <el-skeleton-item variant="text" style="width: 200px; height: 32px;" />
          <div class="skeleton-buttons">
            <el-skeleton-item variant="button" style="width: 80px; height: 32px;" />
            <el-skeleton-item variant="button" style="width: 80px; height: 32px;" />
            <el-skeleton-item variant="button" style="width: 80px; height: 32px;" />
          </div>
        </div>
        <div class="skeleton-table">
          <el-skeleton :rows="10" animated>
            <template slot="template">
              <div class="skeleton-table-row">
                <el-skeleton-item variant="text" style="width: 60px;" />
                <el-skeleton-item variant="text" style="width: 80px;" />
                <el-skeleton-item variant="text" style="width: 120px;" />
                <el-skeleton-item variant="text" style="width: 140px;" />
                <el-skeleton-item variant="text" style="width: 100px;" />
                <el-skeleton-item variant="text" style="width: 100px;" />
                <el-skeleton-item variant="text" style="width: 100px;" />
                <el-skeleton-item variant="text" style="width: 100px;" />
                <el-skeleton-item variant="text" style="width: 120px;" />
                <el-skeleton-item variant="text" style="width: 100px;" />
                <el-skeleton-item variant="text" style="width: 100px;" />
                <el-skeleton-item variant="text" style="width: 100px;" />
              </div>
            </template>
          </el-skeleton>
        </div>
      </div>

      <div class="loginCompany" v-show="isShow && !pageLoading">
        <div class="optDv">
          <div>
            <transition name="fade">
              <el-button
                type="primary"
                size="small"
                :disabled="!isShowMultiDel"
                @click="multiPut"
                >批量张贴</el-button
              >
            </transition>
            <el-popover style="margin-left: 10px" placement="right" trigger="hover">
              <div class="popoverItem" @click="handleGetPdf(2)">导出全部</div>
              <div class="popoverItem" @click="handleGetPdf(1)">导出已张贴</div>
              <div class="popoverItem" @click="handleGetPdf(0)">导出未张贴</div>
              <div class="popoverItem" @click="handleGetPdf(3)">
                自定义导出，勾选✔后导出
              </div>
              <el-button slot="reference" size="mini">导出excel</el-button>
            </el-popover>
          </div>

          <div>
            <el-button
              size="mini"
              type="primary"
              @click="addOne"
              style="margin-right: 20px"
              >新增</el-button
            >
          </div>
        </div>
        <el-form
          :model="form"
          :show-message="true"
          ref="form"
          style="width: 98%; margin: auto"
        >
          <el-table
            ref="multipleTable"
            border
            :data="form.tableData"
            :header-cell-style="{ background: '#f5f7fa' }"
            @selection-change="handleSelectionChange"
            @cell-dblclick="(row, column) => cellDblclick(row, column, 'tableData')"
            :height="height"
            class="main-table"
          >
            <el-table-column type="selection" align="center" width="60">
            </el-table-column>

            <!-- 序号列 -->
            <el-table-column
              type="index"
              :index="indexMethod"
              label="序号"
              width="60"
              align="center"
            ></el-table-column>

            <!-- 车间 -->
            <el-table-column
              prop="workPlace"
              label="车间"
              min-width="120"
              align="center"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <span class="ellipsis-text" :title="scope.row.workPlace">
                  {{ scope.row.workPlace }}
                </span>
              </template>
            </el-table-column>

            <!-- 岗位 -->
            <el-table-column
              prop="configPlace"
              label="岗位"
              min-width="140"
              align="center"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <span class="ellipsis-text" :title="scope.row.configPlace">
                  {{ scope.row.configPlace }}
                </span>
              </template>
            </el-table-column>

            <!-- 告知项目 -->
            <el-table-column
              prop="informProject"
              label="告知项目"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                <el-popover
                  placement="top"
                  width="300"
                  trigger="hover"
                  v-if="getArrayData(scope.row.informProject).length > 0"
                >
                  <div class="popover-tags">
                    <el-tag
                      v-for="(tag, index) in getArrayData(scope.row.informProject)"
                      :key="index"
                      size="small"
                      class="popover-tag"
                    >
                      {{ tag }}
                    </el-tag>
                  </div>
                  <el-button
                    slot="reference"
                    type="text"
                    size="small"
                    class="view-button"
                  >
                    查看 ({{ getArrayData(scope.row.informProject).length }})
                  </el-button>
                </el-popover>
                <span v-else class="empty-text">-</span>
              </template>
            </el-table-column>

            <!-- 警示标识 -->
            <el-table-column
              prop="warnContent"
              label="警示标识"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                <el-popover
                  placement="top"
                  width="350"
                  trigger="click"
                  v-if="getArrayData(scope.row.warnContent).length > 0"
                  popper-class="warn-content-popover"
                >
                  <div class="popover-tags">
                    <div class="popover-header">
                      <div class="popover-title">警示标识列表</div>
                      <div class="popover-tip">（鼠标悬浮标识可查看图片）</div>
                    </div>
                    <div class="tags-container">
                      <el-tag
                        v-for="(tag, index) in getArrayData(scope.row.warnContent)"
                        :key="index"
                        size="small"
                        type="warning"
                        class="popover-tag warn-tag-item"
                      >
                        <WarnImagePopover
                          :tag="tag"
                          :imageMap="tagImageMap"
                          :imageSources="warnImages"
                        />
                      </el-tag>
                    </div>
                  </div>
                  <el-button
                    slot="reference"
                    type="text"
                    size="small"
                    class="view-button"
                  >
                    查看 ({{ getArrayData(scope.row.warnContent).length }})
                  </el-button>
                </el-popover>
                <span v-else class="empty-text">-</span>
              </template>
            </el-table-column>

            <!-- 告知卡 -->
            <el-table-column
              prop="notificationCard"
              label="告知卡"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                <el-popover
                  placement="top"
                  width="300"
                  trigger="hover"
                  v-if="getArrayData(scope.row.notificationCard).length > 0"
                >
                  <div class="popover-tags">
                    <el-tag
                      v-for="(tag, index) in getArrayData(scope.row.notificationCard)"
                      :key="index"
                      size="small"
                      type="info"
                      class="popover-tag clickable-tag"
                      @click="handleTagClick(tag)"
                    >
                      {{ tag }}
                    </el-tag>
                  </div>
                  <el-button
                    slot="reference"
                    type="text"
                    size="small"
                    class="view-button"
                  >
                    查看 ({{ getArrayData(scope.row.notificationCard).length }})
                  </el-button>
                </el-popover>
                <span v-else class="empty-text">-</span>
              </template>
            </el-table-column>

            <!-- 标识数量 -->
            <el-table-column
              prop="signNumber"
              label="标识数量"
              width="100"
              align="center"
            ></el-table-column>

            <!-- 责任人 -->
            <el-table-column
              prop="responPerson"
              label="责任人"
              width="120"
              align="center"
            ></el-table-column>

            <!-- 张贴日期 -->
            <el-table-column
              prop="putUpDate"
              label="张贴日期"
              width="120"
              align="center"
            >
              <template slot-scope="scope">
                <span>{{ formatTime(scope.row.putUpDate) }}</span>
              </template>
            </el-table-column>

            <!-- 张贴情况 -->
            <el-table-column
              prop="putUpStatus"
              label="张贴情况"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                <el-tag
                  :type="scope.row.putUpStatus ? 'success' : 'warning'"
                  size="small"
                >
                  {{ scope.row.putUpStatus ? '已张贴' : '未张贴' }}
                </el-tag>
              </template>
            </el-table-column>

            <!-- 操作 -->
            <el-table-column
              label="操作"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                <div class="action-buttons">
                  <el-button
                    v-if="!scope.row.putUpStatus"
                    size="mini"
                    type="primary"
                    @click="editRow(scope.row, scope.$index)"
                    icon="el-icon-edit"
                    title="编辑"
                  ></el-button>

                  <el-button
                    v-if="!scope.row.putUpStatus"
                    size="mini"
                    type="success"
                    @click="confirmPutUp(scope.row, scope.$index)"
                    icon="el-icon-check"
                    title="张贴"
                  ></el-button>

                  <el-button
                    v-if="scope.row.putUpStatus"
                    size="mini"
                    type="danger"
                    @click="delPutUp(scope.row, scope.$index)"
                    icon="el-icon-delete"
                    title="取下"
                  ></el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
        <!-- 分页条 -->
        <div style="width: 100%; height: 50px; text-align: center">
          <el-pagination
            style="height: 40px; width: auto; margin-top: 15px"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="currentPage2"
            :page-sizes="[10, 20, 50, 100, 200, total]"
            :page-size="allPage"
            layout="sizes, prev, pager, next, total"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
      <sub-company-display
        v-show="!isShow"
        :sub-company-list="subCompanyList"
        :current-id="currentId"
        :warn-notice-list="form.tableData"
        :table-height="height2"
        :current-page="currentPage3"
        :page-size="totalPage"
        :total="total"
        :tagImageMap="tagImageMap"
        :warnImages="warnImages"
        @size-change="onSizeChange"
        @current-change="onCurrentChange"
      />
    </div>

    <el-dialog title="导出pdf" :visible.sync="dialogTableVisible" width="80%">
      <div slot="title">
        <el-button size="mini" type="primary" @click="getExcel">确认导出EXCEL</el-button>
      </div>
      <div
        class="pdf-header"
        style="
          font-weight: bold;
          font-size: 25px;
          padding: 30px 8px;
          width: 100%;
          border-bottom: 1px solid rgba(0, 0, 0, 0.85);
          color: rgba(0, 0, 0, 0.85);
          position: fixed;
          top: -100vh;
        "
      >
        <div style="display: flex; justify-content: center; align-items: center; margin-top: 0px">
          警示标识管理
        </div>
      </div>
      <div
        class="pdf-footer"
        style="
          font-weight: bold;
          padding: 30px 8px;
          width: 100%;
          border-top: 1px solid rgba(0, 0, 0, 0.85);
          position: fixed;
          top: -100vh;
        "
      >
        <div style="display: flex; justify-content: center; align-items: center; margin-top: 0px">
          第
          <div class="pdf-footer-page"></div>
          页 / 共
          <div class="pdf-footer-page-count"></div>
          页
        </div>
      </div>
      <el-table :data="dataSelect" id="pdfdom">
        <el-table-column
          type="index"
          :index="indexMethod"
          label="序号"
          width="50"
          align="center"
        ></el-table-column>
        <el-table-column
          align="center"
          :label="item.label"
          v-for="item in tableField.slice(0, tableField.length - 1)"
          :key="item.prop"
        >
          <template slot-scope="scope">
            <div v-if="Array.isArray(scope.row[item.prop])">
              {{ scope.row[item.prop].join('、 ') }}
            </div>
            <div v-else-if="item.prop == 'putUpDate'">
              {{ formatTime(scope.row[item.prop]) }}
            </div>
            <div v-else>
              {{ scope.row[item.prop] }}
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <el-dialog :visible.sync="dialogVisible" width="50%">
      <template v-slot:title>
        <div>
          <span>告知卡预览</span>
          <el-button size="small" style="float: right; margin-right: 50px;" @click="downloadImage" icon="el-icon-download">下载</el-button>
        </div>
      </template>
      <img :src="getTimestampedUrl(notificationCardUrl)" class="tag-preview-image" style="max-width: 100%; max-height: 100%; height: auto;">
    </el-dialog>

    <!-- 编辑弹窗 -->
    <el-dialog
      :title="currentEditIndex === -1 ? '新增警示标识' : '编辑警示标识'"
      :visible.sync="editDialogVisible"
      width="60%"
      :close-on-click-modal="false"
      class="edit-dialog"
    >
      <el-form
        :model="currentEditRow"
        :rules="rules"
        ref="editForm"
        label-width="100px"
        class="edit-form"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="车间" prop="workPlace">
              <el-input
                v-model="currentEditRow.workPlace"
                placeholder="请输入车间名称"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="岗位" prop="configPlace">
              <el-input
                v-model="currentEditRow.configPlace"
                placeholder="请输入岗位名称"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="告知项目" prop="informProject">
          <div class="tag-input-container">
            <el-tag
              v-for="(tag, index) in getArrayData(currentEditRow.informProject)"
              :key="index"
              closable
              @close="removeTag('informProject', index)"
              class="edit-tag"
            >
              {{ tag }}
            </el-tag>
            <el-input
              v-if="inputVisible2"
              ref="saveTagInput2"
              v-model="inputValue2"
              size="small"
              @keyup.enter.native="addTag('informProject')"
              @blur="addTag('informProject')"
              class="tag-input"
            ></el-input>
            <el-button
              v-else
              size="small"
              @click="showTagInput('informProject')"
              icon="el-icon-plus"
              class="add-tag-btn"
            >
              添加项目
            </el-button>
          </div>
        </el-form-item>

        <el-form-item label="警示标识" prop="warnContent">
          <div class="tag-input-container">
            <el-tag
              v-for="(tag, index) in getArrayData(currentEditRow.warnContent)"
              :key="index"
              closable
              @close="removeWarnTag(index)"
              type="warning"
              class="edit-tag"
            >
              <WarnImagePopover
                :tag="tag"
                :imageMap="tagImageMap"
                :imageSources="warnImages"
              />
            </el-tag>
            <el-select
              v-if="inputVisible"
              ref="warnSelect"
              v-model="selectedWarnTag"
              placeholder="请选择警示标识"
              size="small"
              @change="handleWarnTagSelect"
              @blur="handleWarnSelectBlur"
              class="tag-select"
              clearable
            >
              <el-option
                v-for="(item, index) in availableWarnTags"
                :key="index"
                :label="item"
                :value="item"
              >
                <span style="float: left">{{ item }}</span>
                <img
                  v-if="tagImageMap[item]"
                  :src="warnImages[tagImageMap[item]]"
                  style="float: right; width: 20px; height: 20px; margin-top: 2px;"
                />
              </el-option>
            </el-select>
            <el-button
              v-else
              size="small"
              @click="showWarnSelect"
              icon="el-icon-plus"
              class="add-tag-btn"
            >
              添加标识
            </el-button>
          </div>
        </el-form-item>

        <el-form-item label="告知卡" prop="notificationCard">
          <div class="tag-input-container">
            <el-tag
              v-for="(tag, index) in getArrayData(currentEditRow.notificationCard)"
              :key="index"
              closable
              @close="removeTag('notificationCard', index)"
              type="info"
              class="edit-tag clickable-tag"
              @click="handleTagClick(tag)"
            >
              {{ tag }}
            </el-tag>
            <el-autocomplete
              v-if="inputVisible3"
              ref="saveTagInput3"
              v-model="inputValue3"
              :fetch-suggestions="querySearch"
              placeholder="请输入告知卡名称"
              size="small"
              @keyup.enter.native="addTag('notificationCard')"
              @blur="handleBlurNotificationCard"
              @select="handleSelectInDialog"
              class="tag-input"
              clearable
            ></el-autocomplete>
            <el-button
              v-else
              size="small"
              @click="showTagInput('notificationCard')"
              icon="el-icon-plus"
              class="add-tag-btn"
            >
              添加告知卡
            </el-button>
          </div>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="标识数量">
              <el-input
                :value="getSignNumber"
                placeholder="根据警示标识自动计算"
                readonly
                disabled
              >
                <template slot="append">个</template>
              </el-input>
              <div class="form-tip">标识数量根据警示标识数量自动计算</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="责任人" prop="responPerson">
              <el-select
                v-model="currentEditRow.responPerson"
                filterable
                remote
                reserve-keyword
                placeholder="请选择责任人"
                :remote-method="remoteMethod"
                :loading="loading"
                style="width: 100%"
              >
                <el-option
                  v-for="item in employees"
                  :key="item.employeeId || item.name"
                  :label="item.name"
                  :value="item.name"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="张贴日期" prop="putUpDate">
              <el-date-picker
                v-model="currentEditRow.putUpDate"
                type="date"
                placeholder="选择日期"
                style="width: 100%"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveEdit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import serverApi from "../api";
import UploadExcel from "../components/UploadExcel/index";
import { fillMixin } from "@/utils/fillMixin.js";
import moment from "moment";
// import { PlTable, PlTableColumn } from "pl-table"; // 改用 element-ui 的表格组件
import axios from "axios";
import HelpBtn from "../../../publicMethods/components/HelpBtn";
import * as XLSX from "xlsx";
import WarnImagePopover from "../components/WarnImagePopover.vue";
import SubCompanyDisplay from "../components/SubCompanyDisplay.vue";

function debounce(func, wait) {
  let timeout;
  return function(...args) {
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      func.apply(this, args);
    }, wait);
  };
}
export default {
  data() {
    return {
      value: "",
      isShow: true,
      btnIsShow: false,
      currentCname: "",
      subCompanyList: [],

      activeName: "first",
      dialogTableVisible: false,
      dataSelect: [],
      multipleSelection: [],
      isShowMultiDel: false,
      tableField: [
        { prop: "workPlace", label: "车间", width: "110px" },
        { prop: "configPlace", label: "岗位", width: "140px" },
        { prop: "informProject", label: "告知项目", minWidth: "170px" },
        { prop: "warnContent", label: "警示标识", minWidth: "180px" },
        { prop: "notificationCard", label: "告知卡", width: "150px" },
        { prop: "signNumber", label: "标识数量", width: "90px" },
        { prop: "responPerson", label: "责任人", width: "150px" },
        { prop: "putUpDate", label: "张贴日期", width: "150px" },
        { prop: "putUpStatus", label: "张贴情况", width: "170px" },
      ],
      form: {
        tableData: [],
      },
      rules: {
        workPlace: [{ required: true, message: "作业区必填" }],
        informProject: [{ required: true, message: "告知项目必填" }],
        configPlace: [{ required: true, message: "配置地点必填" }],
        warnContent: [{ required: true, message: "警示内容必填" }],
        // 'signNumber': [{required: true, message: '标识数量必填'}, {validator: validateNum, message: '请输入正确的格式'}],
        responPerson: [{ required: true, message: "责任人必填" }],
        putUpDate: [{ required: true, message: "张贴日期必填" }],
      },
      htmlTitle: "警示标识管理",
      employees: [],
      fileList: [],
      allNotificationCard: [], //所有告知卡的危害因素名称
      notificationCardInfo: [], //告知卡的所有信息
      notificationCardName: "", //告知卡的危害因素名称
      dialogVisible: false,  //告知卡预览弹出框
      url: "",
      notificationCardUrl: "", //告知卡预览的url
      inputVisible: false,
      inputVisible2: false,
      inputVisible3: false,
      inputValue: "",
      inputValue2: "",
      inputValue3: "",
      selectedWarnTag: "", // 选中的警示标识
      picthline: 0,
      picthline2: 0,
      picthline3: 0,
      // 警示图片URL集中管理 - 使用懒加载优化
      warnImages: {},
      // 标签与图片的映射关系
      tagImageMap: {
        "噪声有害": "urlNoise",
        "戴护耳器": "urlEar",
        "当心中毒": "urlPoisoning",
        "当心有毒气体": "urlHarmGas",
        "戴防护镜": "urlEyeShield",
        "戴防毒面具": "urlGasMark",
        "戴防护手套": "urlGauntlet",
        "穿防护服": "urlProtectiveClothing",
        "穿防护鞋": "urlProtectiveShootes",
        "注意通风": "urlVentilate",
        "注意防尘": "urlDust",
        "注意高温": "urlHeat",
        "当心弧光": "urlArcLight",
        "当心感染": "urlInfect",
        "当心腐蚀": "urlCorrosion",
        "当心电离辐射": "urlIonize",
        "戴防尘口罩": "urlMask"
      },
      images: [
        { name: "噪声有害", url: "@/assets/噪声有害.png" },
        { name: "戴护耳器", url: "@/assets/戴护耳器.png" },
        { name: "当心中毒", url: "@/assets/当心中毒.png" },
        { name: "当心有毒气体", url: "@/assets/当心有毒气体.png" },
        { name: "戴防护镜", url: "@/assets/戴防护镜.png" },
        { name: "戴防毒面具", url: "@/assets/戴防毒面具.png" },
        { name: "戴防护手套", url: "@/assets/戴防护手套.png" },
        { name: "穿防护服", url: "@/assets/穿防护服.png" },
        { name: "穿防护鞋", url: "@/assets/穿防护鞋.png" },
        { name: "注意通风", url: "@/assets/注意通风.png" },
        { name: "注意防尘", url: "@/assets/注意防尘.png" },
        { name: "注意高温", url: "@/assets/注意高温.png" },
        { name: "当心弧光", url: "@/assets/当心弧光.png" },
        { name: "当心感染", url: "@/assets/当心感染.png" },
        { name: "当心腐蚀", url: "@/assets/当心腐蚀.png" },
        { name: "当心电离辐射", url: "@/assets/当心电离辐射.png" },
      ],
      total: 0, //总条目数
      allPage: 10, //每页几条
      currentPage2: 1, //当前第几页
      arr: [],
      visible: false,
      height: 0,
      subTotal: 0,
      totalPage: 10,
      currentPage3: 1,
      currentId: "",
      selectedDataIds: [],
      isShowSelect: false,//是否显示查看更多公司下拉框
      loading: false,
      pageLoading: true, // 页面初始加载状态
      // 编辑弹窗相关
      editDialogVisible: false,
      currentEditRow: {},
      currentEditIndex: -1,
    };
  },
  watch: {
    multipleSelection(val) {
      this.isShowMultiDel = val.length > 0;
    },
    arr: {
      handler(newVal) {
        if (newVal.length > 0) {
          this.form.tableData = this.form.tableData.slice(0,this.allPage);
        }
      },
    },
    // 监听表格数据变化，自动保存责任人和时间
    'form.tableData': {
      handler(newData, oldData) {
        if (newData && oldData && newData.length === oldData.length) {
          // 检查是否有责任人或张贴日期的变化
          for (let i = 0; i < newData.length; i++) {
            const newItem = newData[i];
            const oldItem = oldData[i];
            if (newItem && oldItem &&
                (newItem.responPerson !== oldItem.responPerson ||
                 newItem.putUpDate !== oldItem.putUpDate)) {
              // 如果有变化且数据完整，自动保存
              if (newItem.responPerson && newItem.putUpDate && !newItem.putUpStatus) {
                this.debouncedAutoSave(newItem);
              }
            }
          }
        }
      },
      deep: true
    }
  },
  computed: {
    formatTime() {
      return function(time) {
        return moment(time).isValid() ? moment(time).format("YYYY-MM-DD") : "暂未张贴";
      };
    },
    // 获取警示图片URL的计算属性
    getWarnImageUrl() {
      return (tagName) => {
        const imageKey = this.tagImageMap[tagName];
        return imageKey ? this.warnImages[imageKey] : '';
      };
    },

    // 计算标识数量
    getSignNumber() {
      const warnContent = this.getArrayData(this.currentEditRow.warnContent);
      return warnContent.length;
    },

    // 获取可用的警示标识选项（排除已选择的）
    availableWarnTags() {
      const allTags = Object.keys(this.tagImageMap);
      const selectedTags = this.getArrayData(this.currentEditRow.warnContent);
      const available = allTags.filter(tag => !selectedTags.includes(tag));
      console.log('所有标识:', allTags);
      console.log('已选标识:', selectedTags);
      console.log('可选标识:', available);
      return available;
    }
  },
  async created() {
    this.pageLoading = true;

    try {
      // 初始化警示图片 - 懒加载优化
      this.initWarnImages();

      // 设置表格高度
      this.height = document.documentElement.clientHeight - 280 + "px";
      this.height2 = document.documentElement.clientHeight - 230 + "px";

      // 并行加载基础数据，提高加载速度
      const [enterPriseData, subCompanyData] = await Promise.all([
        serverApi.getOrg(),
        serverApi.getAllSubCompany()
      ]);

      // 处理企业信息
      this.currentCname = enterPriseData.data.cname;

      // 处理子公司数据
      this.isShowSelect = subCompanyData.data.length > 1;
      subCompanyData.data.shift();
      this.subCompanyList = subCompanyData.data.map(item => ({
        _id: item.value,
        id: item.value,
        cname: item.text,
      }));

      // 获取表格数据
      await this.loadData();

    } catch (error) {
      console.error('初始化数据失败:', error);
      this.$message.error('初始化数据失败，请刷新页面重试');
    } finally {
      // 确保加载状态被清除
      this.pageLoading = false;
    }
  },
  // mixins: [fillMixin, getPdfFn],
  mixins: [fillMixin],
  methods: {
    // 获取数组数据（统一处理数组和字符串）
    getArrayData(data) {
      if (Array.isArray(data)) {
        return data.filter(item => item && item.trim());
      }
      if (typeof data === 'string' && data.trim()) {
        return data.split(',').filter(item => item && item.trim());
      }
      return [];
    },

    // 编辑行数据
    editRow(row, index) {
      console.log('编辑行数据 - 原始row:', {
        _id: row._id,
        id: row.id,
        configPlaceID: row.configPlaceID,
        putUpStatus: row.putUpStatus
      });

      // 深拷贝数据并确保数组格式正确
      this.currentEditRow = {
        ...row,
        informProject: this.getArrayData(row.informProject),
        warnContent: this.getArrayData(row.warnContent),
        notificationCard: this.getArrayData(row.notificationCard)
      };

      console.log('编辑行数据 - 设置后的currentEditRow:', {
        _id: this.currentEditRow._id,
        id: this.currentEditRow.id,
        configPlaceID: this.currentEditRow.configPlaceID,
        putUpStatus: this.currentEditRow.putUpStatus
      });

      this.currentEditIndex = index;
      this.editDialogVisible = true;

      // 重置输入状态
      this.inputVisible = false;
      this.inputVisible2 = false;
      this.inputVisible3 = false;
      this.inputValue = '';
      this.inputValue2 = '';
      this.inputValue3 = '';
      this.selectedWarnTag = '';
    },

    // 获取当前公司名称
    getCurrentCompanyName() {
      if (this.currentId) {
        const company = this.subCompanyList.find(item => item.id === this.currentId);
        return company ? company.cname : '未知公司';
      }
      return this.currentCname;
    },

    // 获取列宽度
    getColumnWidth(prop) {
      const widthMap = {
        'workPlace': '120',
        'configPlace': '140',
        'informProject': '180',
        'warnContent': '200',
        'notificationCard': '150',
        'signNumber': '100',
        'responPerson': '120',
        'putUpDate': '120'
      };
      return widthMap[prop] || '100';
    },

    // 使用节流版本的防抖函数，优化性能
    remoteMethod: debounce(function(e) {
      if (e.length < 2) {
        this.$message.warning('请输入至少2个字符');
        return;
      }
      
      this.loading = true;
      serverApi.getEmployeeInfo({ 
        searchName: e, 
        loadingConfig: { isLoadingMaskDisable: true } 
      }).then((res) => {
        this.loading = false;
        if (res.status === 200) {
          // 递归提取员工
          const extractEmployees = (data) => {
            let employees = [];
            if (data.children && data.children.length > 0) {
              data.children.forEach(child => {
                if (child.name && child.EnterpriseID) {
                  employees.push({
                    name: child.name.split("（")[0],
                    EnterpriseID: child.EnterpriseID,
                    departs: child.departs,
                    employeeId: child.employeeId
                  });
                }
                employees = employees.concat(extractEmployees(child));
              });
            }
            return employees;
          };

          // 提取并去重
          const tempEmployees = [];
          res.data.forEach(item => {
            tempEmployees.push(...extractEmployees(item));
          });
          
          // 使用更高效的去重方式
          const uniqueMap = new Map();
          tempEmployees.forEach(emp => {
            if (!uniqueMap.has(emp.name)) {
              uniqueMap.set(emp.name, emp);
            }
          });
          
          this.employees = Array.from(uniqueMap.values());
        }
      }).catch(err => {
        this.loading = false;
        this.$message.error('获取员工信息失败');
      });
    }, 500),  // 降低防抖时间以提高响应性

    // 防抖自动保存方法
    // debouncedAutoSave: debounce(async function(data) {
    //   try {
    //     // 只保存责任人和时间，不改变张贴状态
    //     const saveData = {
    //       configPlaceID: data.configPlaceID,
    //       responPerson: data.responPerson,
    //       putUpDate: data.putUpDate,
    //       putUpStatus: false // 确保不会意外张贴
    //     };
    //     await this.submit([saveData]);
    //     console.log('自动保存成功:', data.configPlaceID);
    //   } catch (error) {
    //     console.error('自动保存失败:', error);
    //   }
    // }, 2000),  // 2秒防抖，避免频繁保存

    // 序号计算方法，考虑分页
    indexMethod(index) {
      return (this.currentPage2 - 1) * this.allPage + index + 1;
    },
    async backHome() {
      this.currentId = null;
      this.isShow = true;
      this.btnIsShow = false;
      
      // 重置分页参数
      this.currentPage2 = 1;
      this.allPage = 10;
      this.value=''
      // 重新加载主数据
      await this.loadData();
    },
    clear() {
      this.currentId = null;
      this.isShow = true;
      this.btnIsShow = false;
    },
    async changeCompany(id) {
      try {
        this.loading = true;
        this.isShow = false;
        this.btnIsShow = true;
        this.currentId = id;

        if (id) {
          let res = await axios.get("/manage/warnnotice/getWarnList", {
            params: { id, current: 1, pageSize: this.allPage },
          });

          let list = res.data.data.doc;
          this.subTotal = res.data.data.total;

          if (list && list.length > 0) {
            const processedData = list.map((item, index) => {
              // 格式化日期
              if (item.putUpDate) {
                item.putUpDate = moment(item.putUpDate).format("YYYY-MM-DD");
              }

              // 处理警示内容 - 与主数据处理保持一致
              if (item.warnContent) {
                if (typeof item.warnContent === 'string') {
                  item.warnContent = item.warnContent.split(",").filter(content => content.trim());
                }
              } else {
                item.warnContent = [];
              }

              // 处理告知项目 - 与主数据处理保持一致
              if (item.informProject) {
                if (typeof item.informProject === 'string') {
                  // 去除开头的逗号，然后分割并过滤空项
                  item.informProject = item.informProject.replace(/^,+/, '').split(",").filter(project => project.trim());
                }
              } else {
                item.informProject = [];
              }

              // 处理告知卡 - 与主数据处理保持一致
              if (item.notificationCard) {
                if (typeof item.notificationCard === 'string') {
                  item.notificationCard = item.notificationCard.split(",").filter(card => card.trim());
                }
              } else {
                item.notificationCard = [];
              }

              // 添加序号
              item.index = index + 1;

              return item;
            });

            // 使用正确的数据属性，与主数据加载保持一致
            this.form.tableData = processedData;
            this.total = this.subTotal;

            // 筛选已张贴的数据供导出使用
            this.dataSelect = processedData.filter(item => item.putUpStatus);

            // 存储原始数据供其他地方使用
            this.arr = processedData;

            console.log('子公司数据加载完成:', {
              processedDataLength: processedData.length,
              total: this.subTotal,
              currentId: this.currentId
            });

          } else {
            this.form.tableData = [];
            this.total = 0;
            this.dataSelect = [];
            this.arr = [];
          }
        }

        this.loading = false;
      } catch (error) {
        console.error('加载子公司数据失败:', error);
        this.$message.error('加载子公司数据失败');
        this.form.tableData = [];
        this.total = 0;
        this.loading = false;
      }
    },
    // 修复子公司分页处理
async onSizeChange(val) {
  this.totalPage = val;
  
  try {
    let res = await axios.get("/manage/warnnotice/getWarnList", {
      params: { 
        id: this.currentId, 
        current: this.currentPage3, 
        pageSize: val 
      },
    });
    
    const list = res.data.data.doc;
    if (list && list.length > 0) {
      const processedData = list.map(item => {
        // 格式化日期
        if (item.putUpDate) {
          item.putUpDate = moment(item.putUpDate).format("YYYY-MM-DD");
        }

        // 处理警示内容 - 与主数据处理保持一致
        if (item.warnContent) {
          if (typeof item.warnContent === 'string') {
            item.warnContent = item.warnContent.split(",").filter(content => content.trim());
          }
        } else {
          item.warnContent = [];
        }

        // 处理告知项目 - 与主数据处理保持一致
        if (item.informProject) {
          if (typeof item.informProject === 'string') {
            // 去除开头的逗号，然后分割并过滤空项
            item.informProject = item.informProject.replace(/^,+/, '').split(",").filter(project => project.trim());
          }
        } else {
          item.informProject = [];
        }

        // 处理告知卡 - 与主数据处理保持一致
        if (item.notificationCard) {
          if (typeof item.notificationCard === 'string') {
            item.notificationCard = item.notificationCard.split(",").filter(card => card.trim());
          }
        } else {
          item.notificationCard = [];
        }
        
        return item;
      });
      
      // 更新父组件的数据，确保子组件能正确显示
      this.form.tableData = processedData;
      this.total = res.data.data.total || 0;
    } else {
      this.form.tableData = [];
      this.total = 0;
    }
    
  } catch (error) {
    console.error('加载子公司数据失败:', error);
    this.$message.error('加载子公司数据失败');
    this.form.tableData = [];
    this.total = 0;
  }
},
   // 修复子公司分页页码变化处理
async onCurrentChange(val) {
  this.currentPage3 = val;
  
  try {
    let res = await axios.get("/manage/warnnotice/getWarnList", {
      params: { 
        id: this.currentId, 
        current: val, 
        pageSize: this.totalPage 
      },
    });
    
    const list = res.data.data.doc;
    if (list && list.length > 0) {
      const processedData = list.map(item => {
        // 格式化日期
        if (item.putUpDate) {
          item.putUpDate = moment(item.putUpDate).format("YYYY-MM-DD");
        }

        // 处理警示内容 - 与主数据处理保持一致
        if (item.warnContent) {
          if (typeof item.warnContent === 'string') {
            item.warnContent = item.warnContent.split(",").filter(content => content.trim());
          }
        } else {
          item.warnContent = [];
        }

        // 处理告知项目 - 与主数据处理保持一致
        if (item.informProject) {
          if (typeof item.informProject === 'string') {
            // 去除开头的逗号，然后分割并过滤空项
            item.informProject = item.informProject.replace(/^,+/, '').split(",").filter(project => project.trim());
          }
        } else {
          item.informProject = [];
        }

        // 处理告知卡 - 与主数据处理保持一致
        if (item.notificationCard) {
          if (typeof item.notificationCard === 'string') {
            item.notificationCard = item.notificationCard.split(",").filter(card => card.trim());
          }
        } else {
          item.notificationCard = [];
        }
        
        return item;
      });
      
      // 更新父组件的数据，确保子组件能正确显示
      this.form.tableData = processedData;
      this.total = res.data.data.total || 0;
    } else {
      this.form.tableData = [];
      this.total = 0;
    }
    
  } catch (error) {
    console.error('加载子公司数据失败:', error);
    this.$message.error('加载子公司数据失败');
    this.form.tableData = [];
    this.total = 0;
  }
},

    // 优化加载数据方法，将重复代码抽取为公共方法
    async loadData(params = {}) {
      try {
        const defaultParams = {
          current: this.currentPage2 || 1,
          pageSize: this.allPage || 10,
        };

        const requestParams = { ...defaultParams, ...params };
        const res = await serverApi.getDatas(requestParams);

        if (!res.data) {
          // 初始化为空数组
          this.arr = [];
          this.form.tableData = [];
          this.total = 0;
          this.dataSelect = [];
          throw new Error('未获取到数据');
        }

        this.url = res.data.url;

        // 只在首次加载时处理告知卡数据，避免重复处理
        if (!this.notificationCardInfo.length) {
          // 冻结不需要响应式的大数据，提高性能
          this.notificationCardInfo = Object.freeze(res.data.allNotificationCard || []);
          this.allNotificationCard = Object.freeze(
            (res.data.allNotificationCard || []).map(card => ({
              value: card.harmFactor,
              label: card.harmFactor
            }))
          );

          // 告知卡数据加载完成
          console.log('告知卡数据加载完成，共', this.allNotificationCard.length, '项');
        }
        
        // 预处理数据，确保数组类型一致性
        const processedData = (res.data.doc || []).map(item => {
          // 深拷贝避免修改原始数据
          const newItem = { ...item };
          
          // 确保数组类型一致性
          newItem.warnContent = Array.isArray(newItem.warnContent) 
            ? newItem.warnContent 
            : (newItem.warnContent ? newItem.warnContent.split(",") : []);
            
          newItem.informProject = Array.isArray(newItem.informProject)
            ? newItem.informProject
            : (newItem.informProject ? newItem.informProject.replace(/^,+/, '').split(",").filter(project => project.trim()) : []);
            
          newItem.notificationCard = Array.isArray(newItem.notificationCard) 
            ? newItem.notificationCard 
            : (newItem.notificationCard ? newItem.notificationCard.split(",") : []);
          
          return newItem;
        });
        
        // 直接设置数据，不使用批处理（批处理可能导致显示问题）
        this.form.tableData = processedData;
        this.total = res.data.total || 0;
        // 调试信息
        console.log('加载数据完成:', {
          processedDataLength: processedData.length,
          total: this.total,
          allPage: this.allPage,
          // 检查前几条数据的ID字段
          sampleData: processedData.slice(0, 3).map(item => ({
            _id: item._id,
            id: item.id,
            configPlaceID: item.configPlaceID,
            putUpStatus: item.putUpStatus
          })),
          currentPage: this.currentPage2
        });

        // 筛选已张贴的数据供导出使用
        this.dataSelect = processedData.filter(item => item.putUpStatus);

        // 存储原始数据供其他地方使用
        this.arr = processedData;
        return res;
      } catch (error) {
        console.error('加载数据失败:', error);
        this.$message.error('加载数据失败，请稍后重试');

        // 错误时初始化为空数组
        this.arr = [];
        this.form.tableData = [];
        this.total = 0;
        this.dataSelect = [];
        return null;
      }
    },
    
    // 批处理渲染数据方法
    renderDataInBatches(data) {
      // 清空现有数据
      this.form.tableData = [];
      // 如果没有数据，直接返回
      if (!data || data.length === 0) {
        return;
      }

      // 根据数据量动态调整批处理大小
      const batchSize = data.length > 100 ? 10 : 5;
      let currentIndex = 0;
      
      // 处理一批数据的函数
      const processBatch = () => {
        // 如果已处理完所有数据，则停止
        if (currentIndex >= data.length) {
          return;
        }
        
        // 取出当前批次的数据
        const endIndex = Math.min(currentIndex + batchSize, data.length);
        const batch = data.slice(currentIndex, endIndex);
        
        // 添加到表格数据中
        this.form.tableData.push(...batch);
        
        // 更新索引
        currentIndex = endIndex;
        
        // 使用requestAnimationFrame安排下一批处理
        // 这样可以让浏览器有时间响应用户交互
        window.requestAnimationFrame(processBatch);
      };
      
      // 开始处理第一批
      processBatch();
    },

    // 分页每页条数变化处理
    async handleSizeChange(val) {
      this.currentPage2 = 1;
      this.allPage = val;
      await this.loadData();
    },

    // 分页页码变化处理
    async handleCurrentChange(val) {
      this.currentPage2 = val;
      await this.loadData();
    },
    handleTableSelectChange(tableSelectData) {
      this.selectedDataIds = tableSelectData;
    },
    showInput(index) {
      this.inputVisible = true;
      this.picthline = index;
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    showInput2(index) {
      this.inputVisible2 = true;
      this.picthline2 = index;
      this.$nextTick((_) => {
        this.$refs.saveTagInput2.$refs.input.focus();
      });
    },
    showInput3(index) {
      this.inputVisible3 = true;
      this.picthline3 = index;
      this.$nextTick((_) => {
        this.$refs.saveTagInput3.$refs.input.focus();
      });
    },
    handleInputConfirm(index, name, evn) {
      let inputValue = this.inputValue;
      this.form.tableData[index][name].push(this.inputValue);
      this.inputValue = "";
      this.inputVisible = false;
    },
    handleInputConfirm2(index, name, evn) {
      if (this.inputValue2.trim() === "") {
        this.inputVisible2 = false;
        return;
      }
      let inputValue2 = this.inputValue2;
      this.form.tableData[index][name].push(this.inputValue2);
      this.inputValue2 = "";
      this.inputVisible2 = false;
    },
    handleInputConfirm3(index, name, evn) {
      let inputValue3 = this.inputValue3;
      this.form.tableData[index][name].push(inputValue3);
      this.inputValue3 = "";
      this.inputVisible3 = false;
    },
    handleClose(index, name, tag) {
      this.form.tableData[index][name].splice(
        this.form.tableData[index][name].indexOf(tag),
        1
      );
    },
    handleClose2(index, name, tag) {
      this.form.tableData[index][name].splice(
        this.form.tableData[index][name].indexOf(tag),
        1
      );
    },
    cellDblclick(row, column, type) {
      const tableField = [
        { name: "workPlace", label: "车间" },
        { name: "informProject", label: "告知项目" },
        { name: "configPlace", label: "岗位" },
        { name: "warnContent", label: "警示内容" },
        { name: "notificationCard", label: "告知卡"},
        { name: "signNumber", label: "标识数量" },
        { name: "responPerson", label: "责任人" },
        { name: "putUpDate", label: "张贴日期" },
      ];
      try {
        if (column.label !== "张贴情况") {
          this.fillHandler(row, column, type, tableField, "form");
        }
      } catch (err) {
        console.log(err);
      }
    },
    validateObj(data) {
      const _this = this;
      let isVaild = true;
      let errorInfo = {
        message: [],
        index: "",
      };
      console.log(3333, data);
      const tableField = {};
      this.tableField.forEach((item) => {
        tableField[item.prop] = item.label;
      });
      Object.keys(data).forEach((item) => {
        if (
          !data[item] &&
          item !== "putUpStatus" &&
          item !== "millID" &&
          item !== "configPlaceID"
        ) {
          errorInfo.index = _this.form.tableData.findIndex((item2) => item2 === data) + 1;
          errorInfo.message.push(tableField[item]);
          isVaild = false;
        }
      });
      return {
        isVaild,
        errorInfo,
      };
    },
    // 确认张贴
    async confirmPutUp(data, rowIndex) {
      const _this = this;
      const { isVaild, errorInfo } = this.validateObj(data);
      if (isVaild) {
        data.putUpDate = moment(data.putUpDate).format();
        data.putUpStatus=true
        const res = await this.updateRecord([data]);
        if (res.message == "success") {
          _this.$message({
            message: "张贴成功",
            type: "success",
          });
        }
      } else {
        _this.$notify({
          title: "序号：" + errorInfo.index,
          message: errorInfo.message.join("，") + "必填",
          type: "warning",
        });
      }
    },
    async submit(data) {
      return await serverApi.submitData(data);
    },
    // 更新记录
    async updateRecord(data) {
      return await serverApi.updateData(data);
    },
    // 处理点击 tag 的方法
    handleTagClick(tag) {
      console.log(tag, 'tag');
      this.notificationCardName = tag;
      // 将tag与this.notificationCardInfo中的harmFactor进行匹配，匹配到后将对应的filePath与url相加赋值给this.notificationCardUrl
      let tagArr = this.notificationCardInfo.filter((item) => item.harmFactor === tag);
      let tagObj = tagArr[0];
      let filePath = tagObj.filePath;
      this.notificationCardUrl = this.url + filePath;
      this.dialogVisible = true;
    },
    getTimestampedUrl(url) {
      // 添加时间戳参数以防止缓存
      return `${url}?timestamp=${Date.now()}`;
    },
    // 图片下载
    downloadImage() {
      const link = document.createElement('a');
      const notificationCardName = this.notificationCardName
      link.href = this.notificationCardUrl;
      link.download = notificationCardName; // 可以根据需要更改文件名
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    // 关闭弹出框的方法
    closeDialog() {
      this.notificationCardUrl = "";
      this.dialogVisible = false;
    },
    getPdf() {
      const loading = this.$loading({
        lock: true,
        text: "导出中",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });

      const element = document.querySelector("#pdfdom");
      // const element = document.querySelector(".pdf-panel");
      const header = document.querySelector(".pdf-header");
      const footer = document.querySelector(".pdf-footer");

      const pdfLoader = new PdfLoader(element, {
        header: header,
        footer: footer,
        // outputType:'file',
        fileName: "警示标识管理",
        direction: "p",
        isPageMessage: true,
      });
      pdfLoader.getPdf().then((res) => {
        console.log("[ 导出成功] >", res);
        loading.close();
        this.$message.success("导出成功");
      });
    },
    getExcel() {
      const data = this.dataSelect;

      if (!data || data.length === 0) {
        alert('没有可导出的数据');
        return;
      }

      const wb = XLSX.utils.book_new(); // 创建一个新的工作簿

      data.forEach((item, index) => {
        // 获取日期并格式化为 "YYYY-MM-DD" 格式
        const formattedDate = item.putUpDate ? new Date(item.putUpDate).toLocaleDateString('zh-CN') : '--';

        // 根据每个项目生成数据，初始化表头
        const sheetData = [
          // 添加标题行，合并单元格
          [{ 
            v: '职业危害因素检测结果公示牌', 
            s: { 
              alignment: { horizontal: 'center', vertical: 'center' }, 
              font: { bold: true, size: 14 } 
            }
          }],
          // 添加 "检测单元" 和 "检测日期" 的合并单元格行，左右对齐
          [
            { 
              v: `检测单元：${item.workPlace || '--'}`, 
              s: { 
                alignment: { horizontal: 'left', vertical: 'center' }
              }
            },
            {}, {}, {}, { 
              v: `检测日期：${formattedDate}`, 
              s: { 
                alignment: { horizontal: 'right', vertical: 'center' }
              }
            }
          ],
          // 添加原始数据的表头
          ['检测地点', '检测项目', '检测值', '检测限值', '结果判定']
        ];

        // 填充每条数据，确保字段为空时填充 '--'
        item.informProject.forEach(project => {
          sheetData.push([
            item.configPlace || '--',  // 检测地点
            project || '--',           // 检测项目
            '--',                      // 检测值
            '--',                      // 检测限值
            '--'                       // 结果判定
          ]);
        });

        // 生成工作表名，确保工作表名称唯一
        const sheetName = `${item.workPlace || 'Sheet'}_${index + 1}`;

        // 将数据转换为工作表
        const ws = XLSX.utils.aoa_to_sheet(sheetData);

        // 合并表头的单元格（横跨5列）
        ws['!merges'] = [
          { s: { r: 0, c: 0 }, e: { r: 0, c: 4 } },  // 合并标题行
          { s: { r: 1, c: 0 }, e: { r: 1, c: 3 } },  // 合并"检测单元"字段
          { s: { r: 1, c: 4 }, e: { r: 1, c: 4 } }   // 合并"检测日期"字段
        ];

        // 设置列宽
        ws['!cols'] = [
          { wpx: 150 }, // A列：检测地点
          { wpx: 100 }, // B列：检测项目
          {},           // C列：检测值，默认宽度
          {},           // D列：检测限值，默认宽度
          { wpx: 120 }  // E列：结果判定
        ];

        // 设置第3行行高
        ws['!rows'] = [
          {},
          {},
          { hpx: 20 },  // 设置第3行的行高为30像素
        ];

        // 将工作表添加到工作簿
        XLSX.utils.book_append_sheet(wb, ws, sheetName);
      });

      // 生成 Excel 文件并触发下载
      XLSX.writeFile(wb, '职业危害因素检测结果公示牌.xlsx');
    },

    // 合计行
    getSummares(param) {
      const { data } = param;
      let sums = [];

      // 计算统计数据
      const totalCount = data.length;
      const totalSignNumber = data.reduce((prev, cur) => {
        if (cur.signNumber) {
          return (prev += parseInt(cur.signNumber));
        }
        return prev;
      }, 0);
      const postedCount = data.filter(item => item.putUpStatus).length;
      const unpostedCount = totalCount - postedCount;

      // 将所有统计信息放在第一列，格式美观
      sums[0] = `📊 数据统计：共 ${totalCount} 条记录 | 标识总数 ${totalSignNumber} 个 | 已张贴 ${postedCount} 条 | 未张贴 ${unpostedCount} 条`;

      // 其他列都为空
      for (let i = 1; i < 12; i++) {
        sums[i] = "";
      }

      this.form.total = totalSignNumber;
      return [sums];
    },
    goBack() {
      console.log(1234);
      
      this.$router.go(-1);
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
      // 更新selectedDataIds用于自定义导出
      this.selectedDataIds = val.map(item => item.id || item.configPlaceID);
    },
    // 批量张贴
    async multiPut() {
      const _this = this;
      const notifyArr = [];
      let isTrue = true;
      this.multipleSelection.forEach((item) => {
        // console.log(item);
        const { isVaild, errorInfo } = _this.validateObj(item);
        if (!isVaild && notifyArr.length < 5) {
          isTrue = false;
          const not = _this.$notify({
            title: "序号：" + errorInfo.index,
            message: errorInfo.message.join("，") + "必填",
            type: "warning",
          });
          notifyArr.push(not);
        }
      });
      if (isTrue) {
        const res = await this.submit(this.multipleSelection);
        // console.log(111, res);
        if (res.message == "success") {
          this.multipleSelection.forEach((item) => {
            item.putUpStatus = true;
          });

          this.$refs.multipleTable.clearSelection();

          _this.$message({
            message: "张贴成功",
            type: "success",
          });
        }
      }
    },
    // 取下
    async delPutUp(row) {
      await serverApi.delete([row._id]);
      this.loadData();
    },
    handleGetPdf(type) {
      this.dataSelect = [];
      if (type == 0) {
        this.dataSelect = this.arr.filter((item) => !item.putUpStatus);
      } else if (type == 1) {
        this.dataSelect = this.arr.filter((item) => item.putUpStatus);
      } else if (type == 2) {
        this.dataSelect = this.arr;
      } else if (type == 3) {
        this.dataSelect = this.arr.filter(item => this.selectedDataIds.includes(item.id));
      }
      
      if (this.dataSelect.length === 0) {
        this.$message.warning('没有符合条件的数据可导出');
        return;
      }
      
      this.dialogTableVisible = true;
    },
    addOne() {
      // 改为打开编辑弹窗进行新增
      this.currentEditRow = {
        custom: true,
        workPlace: "",
        configPlace: "",
        informProject: [],
        warnContent: [],
        notificationCard: [],
        signNumber: "",
        responPerson: "",
        putUpDate: "",
        putUpStatus: false,
      };
      this.currentEditIndex = -1;
      this.editDialogVisible = true;

      // 重置输入状态
      this.inputVisible = false;
      this.inputVisible2 = false;
      this.inputVisible3 = false;
      this.inputValue = '';
      this.inputValue2 = '';
      this.inputValue3 = '';
      this.selectedWarnTag = '';

      // 重置输入状态
      this.inputVisible = false;
      this.inputVisible2 = false;
      this.inputVisible3 = false;
      this.inputValue = '';
      this.inputValue2 = '';
      this.inputValue3 = '';
    },

    // 显示标签输入框
    showTagInput(type) {
      if (type === 'informProject') {
        this.inputVisible2 = true;
        this.$nextTick(() => {
          this.$refs.saveTagInput2.$refs.input.focus();
        });
      } else if (type === 'warnContent') {
        this.inputVisible = true;
        this.$nextTick(() => {
          this.$refs.saveTagInput.$refs.input.focus();
        });
      } else if (type === 'notificationCard') {
        this.inputVisible3 = true;
        this.$nextTick(() => {
          this.$refs.saveTagInput3.$refs.input.focus();
        });
      }
    },

    // 添加标签
    addTag(type) {
      let value = '';
      if (type === 'informProject') {
        value = this.inputValue2.trim();
        if (value) {
          if (!Array.isArray(this.currentEditRow.informProject)) {
            this.currentEditRow.informProject = [];
          }
          this.currentEditRow.informProject.push(value);
          this.inputValue2 = '';
        }
        this.inputVisible2 = false;
      } else if (type === 'warnContent') {
        value = this.inputValue.trim();
        if (value) {
          if (!Array.isArray(this.currentEditRow.warnContent)) {
            this.currentEditRow.warnContent = [];
          }
          this.currentEditRow.warnContent.push(value);
          this.inputValue = '';
        }
        this.inputVisible = false;
      } else if (type === 'notificationCard') {
        value = this.inputValue3.trim();
        console.log('添加告知卡 - inputValue3:', this.inputValue3, 'value:', value);

        if (value) {
          if (!Array.isArray(this.currentEditRow.notificationCard)) {
            this.currentEditRow.notificationCard = [];
          }

          // 检查是否已存在相同的告知卡
          if (!this.currentEditRow.notificationCard.includes(value)) {
            this.currentEditRow.notificationCard.push(value);
            console.log('告知卡添加成功:', value);
            console.log('当前告知卡列表:', this.currentEditRow.notificationCard);
          } else {
            console.log('告知卡已存在:', value);
            this.$message.warning('该告知卡已存在');
          }

          this.inputValue3 = '';
        } else {
          console.log('告知卡值为空，无法添加');
        }
        this.inputVisible3 = false;
      }
    },

    // 移除标签
    removeTag(type, index) {
      if (Array.isArray(this.currentEditRow[type])) {
        this.currentEditRow[type].splice(index, 1);
      }
    },

    // 保存编辑
    async saveEdit() {
      try {
        await this.$refs.editForm.validate();

        // 确保数据格式正确
        // 确保标识数量与警示标识数量一致
        this.updateSignNumber();

        const saveData = {
          ...this.currentEditRow,
          // 确保数组字段不为空
          informProject: this.currentEditRow.informProject || [],
          warnContent: this.currentEditRow.warnContent || [],
          notificationCard: this.currentEditRow.notificationCard || [],
          // 确保标识数量正确
          signNumber: this.currentEditRow.signNumber || "0"
        };

        // 调用后端API保存数据
        try {
          if (this.currentEditIndex === -1) {
            // 新增 - 调用submitData接口
            const apiData = {
              ...saveData,
              informProject: Array.isArray(saveData.informProject)
                ? saveData.informProject
                : (saveData.informProject ? saveData.informProject.split(',') : []),
              warnContent: Array.isArray(saveData.warnContent)
                ? saveData.warnContent
                : (saveData.warnContent ? saveData.warnContent.split(',') : []),
              putUpStatus: false // 新增默认不张贴
            };
            await this.submit([apiData]);
            this.form.tableData.unshift(saveData);
          } else {
            // 编辑 - 调用updateData接口
            const apiData = {
              ...saveData,
              informProject: Array.isArray(saveData.informProject)
                ? saveData.informProject
                : (saveData.informProject ? saveData.informProject.split(',') : []),
              warnContent: Array.isArray(saveData.warnContent)
                ? saveData.warnContent
                : (saveData.warnContent ? saveData.warnContent.split(',') : []),
              _id: this.currentEditRow._id, // 使用_id字段
              putUpStatus: this.currentEditRow.putUpStatus || false // 保持原有状态
            };

            console.log('准备调用编辑接口:', {
              _id: apiData._id,
              configPlaceID: apiData.configPlaceID,
              putUpStatus: apiData.putUpStatus
            });
            const res = await this.updateRecord([apiData]);
            this.$set(this.form.tableData, this.currentEditIndex, saveData);
          }

          // 刷新数据以确保与数据库同步
          await this.loadData({
            current: this.currentPage2,
            pageSize: this.allPage
          });

          this.editDialogVisible = false;
          this.$message.success(this.currentEditIndex === -1 ? '新增成功' : '编辑成功');

          // 重置编辑状态
          this.currentEditRow = {};
          this.currentEditIndex = -1;
        } catch (apiError) {
          console.error('保存失败:', apiError);
          this.$message.error('保存失败，请重试');
        }
      } catch (error) {
        console.error('表单验证失败:', error);
        this.$message.error('请检查必填字段是否完整');
      }
    },
    querySearch(queryString, cb) {
      const allNotificationCard = this.allNotificationCard;
      console.log('查询告知卡 - queryString:', queryString);
      console.log('所有告知卡数据:', allNotificationCard);

      const results = queryString ? allNotificationCard.filter(this.createFilter(queryString)) : allNotificationCard;
      console.log('过滤后的结果:', results);

      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
      };
    },
    handleSelect(selectedValue) {
      this.inputValue3 = selectedValue.value;
      this.addTag('notificationCard');
    },

    // 编辑弹窗中的选择处理
    handleSelectInDialog(selectedValue) {
      console.log('选择的告知卡:', selectedValue);
      this.inputValue3 = selectedValue.value;

      // 使用 nextTick 确保 inputValue3 已经更新
      this.$nextTick(() => {
        this.addTag('notificationCard');
      });
    },

    // 告知卡输入框失焦处理
    handleBlurNotificationCard() {
      // 延迟执行，避免与select事件冲突
      setTimeout(() => {
        if (this.inputValue3.trim()) {
          this.addTag('notificationCard');
        } else {
          this.inputVisible3 = false;
        }
      }, 200);
    },

    // 显示警示标识选择框
    showWarnSelect() {
      this.inputVisible = true;
      this.selectedWarnTag = '';
      this.$nextTick(() => {
        if (this.$refs.warnSelect) {
          this.$refs.warnSelect.focus();
        }
      });
    },

    // 隐藏警示标识选择框
    hideWarnSelect() {
      this.inputVisible = false;
      this.selectedWarnTag = '';
    },

    // 处理警示标识选择
    handleWarnTagSelect(value) {
      console.log('选择的警示标识:', value);
      if (value) {
        this.selectedWarnTag = value;
        // 使用 nextTick 确保数据更新后再添加
        this.$nextTick(() => {
          this.addWarnTag();
        });
      }
    },

    // 处理选择框失焦
    handleWarnSelectBlur() {
      // 延迟执行，避免与change事件冲突
      setTimeout(() => {
        if (!this.selectedWarnTag) {
          this.hideWarnSelect();
        }
      }, 200);
    },

    // 添加警示标识
    addWarnTag() {
      console.log('添加警示标识 - selectedWarnTag:', this.selectedWarnTag);

      if (this.selectedWarnTag) {
        if (!Array.isArray(this.currentEditRow.warnContent)) {
          this.currentEditRow.warnContent = [];
        }

        if (!this.currentEditRow.warnContent.includes(this.selectedWarnTag)) {
          this.currentEditRow.warnContent.push(this.selectedWarnTag);
          // 自动更新标识数量
          this.updateSignNumber();
          console.log('警示标识添加成功:', this.selectedWarnTag);
          console.log('当前警示标识列表:', this.currentEditRow.warnContent);
        } else {
          console.log('警示标识已存在:', this.selectedWarnTag);
          this.$message.warning('该警示标识已存在');
        }
      } else {
        console.log('警示标识值为空，无法添加');
      }

      this.hideWarnSelect();
    },

    // 移除警示标识
    removeWarnTag(index) {
      if (Array.isArray(this.currentEditRow.warnContent)) {
        this.currentEditRow.warnContent.splice(index, 1);
        // 自动更新标识数量
        this.updateSignNumber();
      }
    },

    // 更新标识数量
    updateSignNumber() {
      const warnContent = this.getArrayData(this.currentEditRow.warnContent);
      const count = warnContent.length;
      this.currentEditRow.signNumber = count.toString();
      console.log('更新标识数量:', count);
    },

    // 初始化警示图片 - 懒加载优化
    initWarnImages() {
      // 使用懒加载方式初始化图片，只在需要时加载
      this.warnImages = {
        urlNoise: require("@/assets/噪声有害.png"),
        urlEar: require("@/assets/戴护耳器.png"),
        urlPoisoning: require("@/assets/当心中毒.png"),
        urlHarmGas: require("@/assets/当心有毒气体.png"),
        urlEyeShield: require("@/assets/戴防护镜.png"),
        urlGasMark: require("@/assets/戴防毒面具.png"),
        urlGauntlet: require("@/assets/戴防护手套.png"),
        urlProtectiveClothing: require("@/assets/穿防护服.png"),
        urlProtectiveShootes: require("@/assets/穿防护鞋.png"),
        urlVentilate: require("@/assets/注意通风.png"),
        urlDust: require("@/assets/注意防尘.png"),
        urlHeat: require("@/assets/注意高温.png"),
        urlArcLight: require("@/assets/当心弧光.png"),
        urlInfect: require("@/assets/当心感染.png"),
        urlCorrosion: require("@/assets/当心腐蚀.png"),
        urlIonize: require("@/assets/当心电离辐射.png"),
        urlMask: require("@/assets/戴防尘口罩.png"),
      };
    },
  },
  components: {
    UploadExcel,
    // PlTable,
    // PlTableColumn,
    HelpBtn,
    WarnImagePopover,
    SubCompanyDisplay
  },
};
</script>

<style lang="scss">
// 可复用的样式变量
$primary-color: #409EFF;
$border-color: #dcdfe6;
$hover-color: #f5f5f5;
$success-color: #67C23A;
$warning-color: #E6A23C;
$danger-color: #F56C6C;

// 弹出菜单项样式
.popoverItem {
  height: 30px;
  line-height: 30px;
  cursor: pointer;
  width: 100%;
  
  &:hover {
    background: $hover-color;
  }
}

// 标题样式
.title {
  padding-left: 20px;
  font-size: 16px;
  color: #303133;
  border-left: 1px solid $border-color;
  margin-left: 20px;
}

// 头部样式
.header {
  align-items: center;
  display: flex;
  margin-bottom: 20px;
  
  .header-title {
    margin-right: 40px;
    margin-left: 20px;
  }
}

// 页面样式
.page {
  background-color: white;
  margin-left: 20px;
  margin-top: 20px;
  height: 96%;
}

// 表单样式优化
.el-form-item {
  margin-right: 0;
  margin-bottom: 0px;
}

// 动画效果
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.4s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}

// 滚动条美化
::v-deep .el-textarea__inner::-webkit-scrollbar {
  width: 5px; // 横向滚动条
  height: 6px; // 纵向滚动条 必写
}

::v-deep .el-textarea__inner::-webkit-scrollbar-thumb {
  background-color: #ddd;
  border-radius: 5px;
}

// 输入框边框样式
::v-deep .el-input__inner,
::v-deep .el-textarea__inner {
  border: 0px;
}

// 表格底部间距
::v-deep .el-table__body-wrapper,
.el-table__footer-wrapper,
.el-table__header-wrapper {
  padding: 0 0 60px 0;
}

// 操作区域样式
.optDv {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  margin-left: 20px;
}

// 按钮样式
.modalBtn {
  margin-left: 10px;
}

// 标签样式
.el-tag + .el-tag {
  margin-right: 5px;
  margin-left: 5px;
}

// 新标签按钮样式
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

// 新标签输入框样式
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}

// 弹出框样式
.el-popover.myPopover {
  background: rgba(0, 0, 0, 0);
  text-align: center;
}

// 标签预览图样式
.tag-preview-image {
  width: 100%;
  height: auto;
  max-height: 400px;
  object-fit: contain;
}

// 表格样式优化
.tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
  align-items: center;
  min-height: 32px;
  padding: 4px;
}

.table-tag {
  margin: 2px;
  border-radius: 4px;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &.clickable-tag {
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }

  // 警示标识图片悬浮样式修复
  &.warn-tag-with-image {
    position: relative;
    z-index: 1;

    // 确保悬浮图片显示在正确位置
    ::v-deep .el-popover__reference {
      position: relative;
      z-index: 2;
    }
  }
}

// 操作按钮容器
.action-buttons {
  display: flex;
  gap: 6px;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  padding: 4px;

  .el-button {
    &.el-button--mini {
      padding: 5px;
      width: 24px;
      height: 24px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;

      i {
        font-size: 12px;
        margin: 0;
      }
    }
  }
}

// 编辑弹窗样式
.edit-dialog {
  .edit-form {
    .tag-input-container {
      border: 1px solid #dcdfe6;
      border-radius: 8px;
      padding: 8px;
      min-height: 40px;
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
      align-items: center;

      .edit-tag {
        margin: 0;
        border-radius: 4px;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        &.clickable-tag {
          cursor: pointer;
        }
      }

      .tag-input {
        width: 120px;
        margin: 0;
      }

      .add-tag-btn {
        border: 1px dashed #dcdfe6;
        background: transparent;
        color: #909399;

        &:hover {
          border-color: $primary-color;
          color: $primary-color;
        }
      }
    }
  }

  .dialog-footer {
    text-align: right;
    padding-top: 20px;
  }
}



// 修复表格底部间距和背景
.main-table {
  background: #fff !important;

  ::v-deep .el-table__body-wrapper {
    background: #fff !important;
    padding-bottom: 0 !important;
  }

  ::v-deep .el-table__footer-wrapper {
    background: #fff !important;
    padding-bottom: 0 !important;
  }

  // 移除表格底部的额外空间
  ::v-deep .el-table__body-wrapper::after,
  ::v-deep .el-table__footer-wrapper::after {
    display: none !important;
  }

  // 确保表格底部没有多余的边框或背景
  ::v-deep .el-table::after {
    display: none !important;
  }

  // 修复固定列的底部显示
  ::v-deep .el-table__fixed-right {
    background: #fff !important;

    .el-table__fixed-footer-wrapper {
      background: #fff !important;
    }

    .el-table__fixed-body-wrapper {
      background: #fff !important;
    }
  }
}

// 额外修复：确保表格容器没有多余的背景
.el-form {
  background: transparent !important;
}

// 修复可能的滚动条区域背景
::v-deep .el-scrollbar {
  background: #fff !important;

  .el-scrollbar__wrap {
    background: #fff !important;
  }

  .el-scrollbar__view {
    background: #fff !important;
  }
}

// 直接修复导致问题的元素
::v-deep td.el-table_1_column_12.is-center.is-leaf,
::v-deep .el-table_1_column_12.is-center.is-leaf {
  background: #fff !important;
  background-color: #fff !important;
}

// 修复所有可能的表格列背景问题
::v-deep td[class*="el-table_1_column_"].is-center.is-leaf {
  background: #fff !important;
  background-color: #fff !important;
}

// 修复表格底部可能的空白区域
::v-deep .el-table__body-wrapper {
  .el-table__row:last-child {
    td {
      border-bottom: 1px solid #ebeef5 !important;
    }
  }
}

// 表格滚动样式优化
.main-table {
  width: 100% !important;

  ::v-deep .el-table__body-wrapper {
    overflow-y: auto !important;
    scrollbar-width: thin !important;
    scrollbar-color: #c1c1c1 #f1f1f1 !important;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 8px !important;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1 !important;
      border-radius: 4px !important;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1 !important;
      border-radius: 4px !important;

      &:hover {
        background: #a8a8a8 !important;
      }
    }
  }

  ::v-deep .el-table__header-wrapper {
    overflow: visible !important;
  }

  // 确保表格固定高度，内容可滚动
  ::v-deep .el-table__fixed-right {
    .el-table__fixed-body-wrapper {
      overflow-y: auto !important;
    }
  }
}

// 强制修复Element UI表格底部的灰色区域问题
::v-deep .el-table {
  // 移除表格可能的底部边框或背景
  .el-table__body,
  .el-table__footer {
    background: #fff !important;
  }

  // 修复所有表格单元格的背景
  td {
    background: #fff !important;
    background-color: #fff !important;

    &.is-center {
      background: #fff !important;
      background-color: #fff !important;
    }

    &.is-leaf {
      background: #fff !important;
      background-color: #fff !important;
    }
  }

  // 特别修复最后一列（操作列）
  td:last-child {
    background: #fff !important;
    background-color: #fff !important;
  }

  // 修复合计行
  .el-table__footer {
    td {
      background: #fff !important;
      background-color: #fff !important;
    }
  }
}

// 如果还有问题，使用更强的选择器
::v-deep [class*="el-table_1_column_"] {
  background: #fff !important;
  background-color: #fff !important;
}

// 查看按钮样式
.view-button {
  color: #409EFF !important;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;

  &:hover {
    background-color: rgba(64, 158, 255, 0.1);
    color: #409EFF !important;
  }
}

// 悬浮框中的标签样式
.popover-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  max-height: 200px;
  overflow-y: auto;

  .popover-tag {
    margin: 0;
    border-radius: 4px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
}

// 空数据显示
.empty-text {
  color: #c0c4cc;
  font-style: italic;
}
</style>

<style>
/* 全局样式：美化悬浮框 */
.el-popover {
  border-radius: 8px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid #e4e7ed !important;
}

.el-popover .el-popover__title {
  font-weight: 600 !important;
  color: #303133 !important;
}

/* 确保悬浮框在表格上方显示 */
.el-popover {
  z-index: 9999 !important;
}

/* 警示标识悬浮框特殊样式 */
.warn-content-popover {
  max-width: 400px !important;

  .popover-tags {
    display: flex;
    /* flex-direction: column; */
  }

  .popover-header {
    text-align: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e4e7ed;
    width: 100%;

    .popover-title {
      display: block;
      font-weight: 600;
      color: #303133;
      font-size: 14px;
      margin-bottom: 6px;
      line-height: 1.4;
      width: 100%;
    }

    .popover-tip {
      display: block;
      font-size: 12px;
      color: #909399;
      font-style: italic;
      line-height: 1.4;
      width: 100%;
    }
  }

  .tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    max-height: 200px;
    /* overflow-y: auto; */
    width: 100%;
    clear: both;

    .warn-tag-item {
      margin: 0;
      border-radius: 4px;
      transition: all 0.3s ease;
      position: relative;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

/* 省略号文本样式 */
.ellipsis-text {
  display: inline-block;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: middle;
  cursor: default;
}

/* 警示标识选择框样式 */
.tag-select {
  width: 200px;
  margin-right: 8px;
}

/* 表单提示样式 */
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

/* 骨架屏样式 */
.skeleton-container {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  margin: 20px 0;

  .skeleton-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e4e7ed;

    .skeleton-buttons {
      display: flex;
      gap: 12px;
    }
  }

  .skeleton-table {
    .skeleton-table-row {
      display: flex;
      gap: 12px;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }
    }
  }
}
</style>
