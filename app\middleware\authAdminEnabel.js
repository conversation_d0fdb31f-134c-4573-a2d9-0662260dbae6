// const _ = require('lodash');
module.exports = () => {
// module.exports = options => {
  return async function authAdminEnabel(ctx, next) {
    const url = ctx.request.url;

    const { isLimitOperation } = await ctx.service.systemConfig.authAdminEnabel();

    if (isLimitOperation) {
      if (url.indexOf('/manage') === 0) {
        ctx.status = 401;
        ctx.cookies.set('admin_' + ctx.app.config.auth_cookie_name, null, {
          path: '/',
          signed: true,
          httpOnly: false,
        });
        ctx.body = {
          data: null,
          message: '登录已过期，请刷新页面后重新登录',
        };
      } else {
        await next();
      }
    } else {
      await next();
    }
  };
};
