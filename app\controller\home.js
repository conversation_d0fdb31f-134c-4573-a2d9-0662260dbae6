const Controller = require('egg').Controller;
const xss = require('xss');
const shortid = require('shortid');
const _ = require('lodash');
const { authToken } = require('@utils');
const jwt = require('jsonwebtoken');
const fs = require('fs');
const path = require('path');
const { adminUserRule } = require('@validate');
const validator = require('validator');
const { siteFunc, validatorUtil, tools, ecdh } = require('@utils');
const { updateData, validPhoneAndIDNum } = require('@utils/tools.js');
const moment = require('moment');
const svgCaptcha = require('svg-captcha');
const axios = require('axios');
const qs = require('qs');
const { v4: uuidv4 } = require('uuid');

class HomeController extends Controller {
  // 页面的显示状态。0：显示表单，1：审核中，2：审核通过，3：审核失败，9：服务器错误
  // 可以多添加几个状态，等接口写完再添加
  // 可以多添加几个状态，等接口写完再添加
  // 使用cookie仅限于同个域名下，现在是不同域，直接发参数了。
  // let getTokenFromCookie = ctx.cookies.get('admin_' + ctx.app.config.auth_cookie_name);
  async fullArea(ctx) {
    try {
      const fields = ctx.request.body || {};
      console.log(fields);
      const targetItem = await ctx.service.findFirms.fullArea(fields);
      console.log(targetItem);
      ctx.helper.renderSuccess(ctx, {
        data: targetItem,
      });
    } catch (err) {
      console.log(err);
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  async findInFirms(ctx) {
    try {
      const payload = ctx.request.body;
      // const query = ctx.query.query ? JSON.parse(ctx.query.query) : {};
      // console.log('==================查询条件');
      // console.log(payload);
      const adminorgList = await ctx.service.findFirms.findInFirms(payload);
      ctx.helper.renderSuccess(ctx, {
        data: adminorgList,
      });
    } catch (err) {
      console.log(err);
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }
  async getApplyCorp() {
    // return;
    const { ctx, config } = this;
    try {
      console.log(
        '企业申请',
        ctx.req.connection.remoteAddress,
        ctx.session.adminUserInfo
      );
      const token = ctx.cookies.get('admin_' + ctx.app.config.auth_cookie_name);
      const pageData = {};
      const isAutoUpdate = config.isAutoUpdate,
        isVersionUpdate = config.isVersionUpdate;
      let params = '';
      if (isVersionUpdate) {
        params = `?ver=CHINA${config.version}`;
      } else if (isAutoUpdate) {
        const date = new Date();
        params = `?ver=C${date.getSeconds()}HI${Math.random()
          .toString(36)
          .substr(2)}N${date.getMilliseconds()}A`;
      }
      let vuePath = `/static/orgApply/js/app.js${params}`;
      pageData.pageState = 0;
      if (
        ctx.app.config.dev_modules &&
        ctx.app.config.dev_modules.indexOf('orgApply') >= 0
      ) {
        vuePath = 'http://' + ctx.hostname + ':8691/app.js';
      }
      if (_.isEmpty(token)) {
        // token不存在报错，回到登e录
        // console.log('这次请求的token参数为空');
        pageData.pageState = 9;
        ctx.redirect('/admin/login');
        return false;
      }
      if (!ctx.session.adminUserInfo) {
        // console.log('没有登录！');
        pageData.pageState = 9;
      }
      const checkToken = await authToken.checkToken(
        token,
        this.app.config.encrypt_key
      );

      if (!checkToken) {
        // console.log('无效的token，让用户去登录或是去CMS');
        pageData.pageState = 9;
      }
      pageData.user = await ctx.service.adminUser.item(ctx, {
        query: {
          _id: checkToken._id,
        },
      }, { authCheck: false });
      // pageData.user = ctx.session.adminUserInfo;

      // 用户存在，查询企业状态
      let organization = await ctx.service.adminorg.item(ctx, {
        query: {
          adminUserId: checkToken._id,
        },
        files:
          'cname code districtRegAdd regAdd districtWorkAdd workAdd corp industryCategory licensePic workAddress isactive message contract',
      }, { authCheck: false });

      if (organization && organization._id) {
        organization = tools.convertToEditJson(organization);
        const workAddress = organization.workAddress;
        if (workAddress && workAddress.length > 0) {
          const workAddressCount = workAddress.length;
          organization.districtWorkAdd =
            workAddress[workAddressCount - 1].districts;
          organization.workAdd = workAddress[workAddressCount - 1].address;
        }
        delete organization.workAddress; // 兼容之关键
        organization.industryCategory = organization.industryCategory
          ? organization.industryCategory
          : [];
        organization.showIndustryCategory =
          organization.industryCategory[0] || [];
        pageData.organization = organization;
        if (organization.isactive === '0') {
          console.log('%s - 已提交审核', organization.cname);
          pageData.pageState = 1;
        } else if (organization.isactive === '1') {
          console.log('%s - 已通过审核', organization.cname);
          pageData.pageState = 2;
          // 直接跳转

          const newToken = jwt.sign(
            {
              EnterpriseID: organization._id,
              _id: checkToken._id,
            },
            this.app.config.encrypt_key,
            {
              expiresIn: '30day',
            }
          );
          // 存入cookie
          ctx.cookies.set(
            'admin_' + this.app.config.auth_cookie_name,
            newToken,
            {
              path: '/',
              maxAge: 1000 * 60 * 60 * 24 * 30,
              signed: true,
              httpOnly: false,
            }
          );

          ctx.redirect('/admin/dashboard');
          return false;
        } else if (organization.isactive === '2') {
          console.log('%s - 审核未通过', organization.cname);
          pageData.pageState = 3;
        } else if (organization.isactive !== '3') {
          // 未知的数据错误，有人为修改数据库的isactive字段
          pageData.pageState = 8;
        }
      } else {
        pageData.pageState = 0;
      }
      console.log(pageData, 'pagedata111');
      await ctx.render('/corporation/reg.html', {
        status: 200,
        staticRootPath: config.static.prefix,
        siteSeo: this.app.config.siteSeo,
        pageData: JSON.stringify(pageData),
        vuePath,
      });
    } catch (err) {
      // 失败
      console.log('--error--:', err);
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  async logOutAction() {
    const ctx = this.ctx;
    // const { api_host, realm_name, post_logout_redirect_uri } = this.app.config.oidc.keyCloak;
    const { byToken } = ctx.session.adminUserInfo;
    console.log('退出登录操作ing', this.app.config.branch, byToken);
    // if (this.app.config.branch === 'by') {
    //   try {
    //     // const logoutUrl = `${api_host}/realms/${realm_name}/protocol/openid-connect/logout?post_logout_redirect_uri=${post_logout_redirect_uri}&id_token_hint=${byToken}`;
    //     const logoutUrl = `${api_host}/realms/${realm_name}/protocol/openid-connect/logout?redirect_uri=${post_logout_redirect_uri}`;
    //     const logOutRes = await axios.get(logoutUrl);
    //     ctx.auditLog(
    //       '退出登录keycloak返回结果',
    //       `${JSON.stringify(logOutRes.data)}`,
    //       'info'
    //     );
    //   } catch (error) {
    //     ctx.auditLog(
    //       '退出登录keycloak返回错误',
    //       `${JSON.stringify(error)}`,
    //       'error'
    //     );
    //   }
    // }
    ctx.auditLog('退出登录', '当前用户进行了退出登录操作。', 'info');
    ctx.service.operateLog.create('AdminUser', {
      optType: 'logout',
      supplementaryNotes: '用户退出登录',
    });
    const userid = ctx.session.adminUserInfo._id;
    await ctx.helper.clearUserPolicyCache(userid);
    ctx.session = null;
    ctx.cookies.set('admin_' + this.app.config.auth_cookie_name, null);

    const origin = this.ctx.request.header.host;
    let domain = '127.0.0.1';
    if (origin.includes('.cn')) {
      if (this.app.config.branch === 'xhl') {
        domain = 'xhlzyws.cn';
      } else if (this.app.config.branch === 'hz') {
        domain = 'hzzyws.cn';
      } else if (this.app.config.branch === 'fz') {
        domain = 'zyjk.fzcdc.org.cn';
      } else if (this.app.config.branch === 'hf') {
        domain = 'hfzyws.cn';
      } else if (this.app.config.branch === 'yc') {
        domain = 'yc.zyws.cn';
      } else if (this.app.config.branch === 'ldq') {
        domain = 'ldqzyws.cn';
      } else if (this.app.config.branch === 'by') {
        domain = 'zyjk.by';
      } else {
        domain = 'zyws.cn';
      }
    } else if (origin.includes('.net')) {
      domain = '.zyws.net';
    }
    ctx.cookies.set('admin_' + this.app.config.auth_cookie_name, null, {
      domain,
    });
    ctx.cookies.set('admin_doracmsapi', null);
    // ctx.redirect('/admin/login')
    ctx.helper.renderSuccess(ctx, {
      message: '退出登录成功',
      data: {
        branch: this.app.config.branch,
      },
    });
  }

  async getIndexPage() {
    const { ctx } = this;
    try {
      await ctx.render('index/index.html', {
        siteSeo: this.app.config.siteSeo,
        domainNames: this.app.config.domainNames,
        staticRootPath: this.app.config.static.prefix,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  async returnLogin() {
    const ctx = this.ctx;
    // 设置session信息
    ctx.session.showLoginPage = 'exist';
    ctx.helper.renderSuccess(ctx);
  }

  async getLoginPage() {
    const { ctx, config } = this;

    try {
      if (ctx.session.adminUserInfo) {
        console.log('getLoginPage, adminUserInfo:', ctx.session.adminUserInfo);
        if (
          ctx.session.showLoginPage &&
          ctx.session.showLoginPage === 'exist'
        ) {
          // 如果用户已登陆   userLogin为

          const configs = await ctx.helper.reqJsonData(
            'systemConfig/getConfig'
          );
          const { showImgCode } = configs || [];
          // const { userLogin } = ;
          // const companys = await ctx.model.Adminorg.find({
          //   _id: ctx.session.adminUserInfo.EnterpriseID,
          // });
          const companys = await ctx.service.db.find('Adminorg', {
            _id: ctx.session.adminUserInfo.EnterpriseID,
          });
          const cname = companys[0].cname;
          const name = ctx.session.adminUserInfo.name;
          console.log('companys=========', companys[0]);

          await ctx.render(`${config.branch}/login.html`, {
            siteSeo: this.app.config.siteSeo,
            staticRootPath: this.app.config.static.prefix,
            showImgCode,
            userLogin: '1',
            cname,
            name,
            ...(await this.getEnvConfig()), // 页脚信息
            ...(await this.getPublicKey()), // 公钥
          });
        } else {
          ctx.redirect('/admin/dashboard');
        }
      } else {
        const configs = await ctx.helper.reqJsonData('systemConfig/getConfig');
        const { showImgCode } = configs || [];
        console.log('showImgCode', showImgCode);
        await ctx.render(`${config.branch}/login.html`, {
          siteSeo: this.app.config.siteSeo,
          staticRootPath: this.app.config.static.prefix,
          showImgCode,
          userLogin: '2',
          ...(await this.getEnvConfig()), // 页脚信息
          ...(await this.getPublicKey()), // 公钥
        });
      }
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  async getTryPage() {
    const { ctx, config } = this;
    try {
      await ctx.render(`${config.branch}/trial.html`, {
        staticRootPath: this.app.config.static.prefix,
        siteSeo: this.app.config.siteSeo,
        // showImgCode,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  async getRegPage() {
    const { ctx } = this;
    try {
      if (ctx.session.adminUserInfo) {
        // 如果存在账户
        console.log('getRegPage, adminUserInfo:', ctx.session.adminUserInfo);
        ctx.redirect('/admin/dashboard');
      } else {
        const systemConfigs = await ctx.service.systemConfig.find({
          isPaging: '0',
        });
        const { showImgCode } = systemConfigs[0];
        // const configs = await ctx.helper.reqJsonData('systemConfig/getConfig');
        // const { showImgCode } = configs || [];
        await ctx.render('corporation/reg.html', {
          staticRootPath: this.app.config.static.prefix,
          siteSeo: this.app.config.siteSeo,
          showImgCode,
          ...(await this.getPublicKey()),
        });
      }
      console.log('渲染结束');
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  // /////////////////////////lihj
  async getServicePage() {
    const { ctx, config } = this;

    try {
      await ctx.render(`${config.branch}/service.html`, {
        staticRootPath: this.app.config.static.prefix,
        siteSeo: this.app.config.siteSeo,
        logoSrc: `/static/images/${config.branch}/logo.png`,
        ...(await this.getEnvConfig()), // 页脚信息
        // showImgCode,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  async getOfficialPage() {
    const { ctx, config } = this;
    try {
      await ctx.render(`${config.branch}/official.html`, {
        staticRootPath: this.app.config.static.prefix,
        siteSeo: this.app.config.siteSeo,
        logoSrc: `/static/images/${config.branch}/logo.png`,
        ...(await this.getEnvConfig()), // 页脚信息
        // showImgCode,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  async getConnectPage() {
    const { ctx, config } = this;

    try {
      await ctx.render(`${config.branch}/connect.html`, {
        staticRootPath: this.app.config.static.prefix,
        siteSeo: this.app.config.siteSeo,
        logoSrc: `/static/images/${config.branch}/logo.png`,
        ...(await this.getEnvConfig()), // 页脚信息
        // showImgCode,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  async getServicesPage() {
    const { ctx } = this;
    ctx.tempPage = 'services.html';
    ctx.pageType = 'services';
    await ctx.getPageData();
  }
  async getRegulationPage() {
    const { ctx } = this;
    ctx.tempPage = 'regulation.html';
    ctx.pageType = 'regulation';
    await ctx.getPageData();
  }
  async getContentListPage() {
    const { ctx } = this;
    ctx.tempPage = 'contentList.html';
    ctx.pageType = 'contentList';
    await ctx.getPageData();
  }
  async getConsultPage() {
    const { ctx } = this;
    ctx.tempPage = 'consult.html';
    ctx.pageType = 'consult';
    await ctx.getPageData();
  }
  async getDetailPage() {
    const { ctx } = this;
    ctx.tempPage = 'detail.html';
    ctx.pageType = 'detail';
    await ctx.getPageData();
  }

  async getHomePage() {
    const { ctx, config } = this;
    try {
      if (config.branch === 'hz') {
        ctx.redirect('/warning');
      } else if (config.branch === 'hohhot') {
        ctx.tempPage = 'index.html';
        ctx.pageType = 'index';
        await ctx.getPageData();
      } else {
        await ctx.render(`${config.branch}/home.html`, {
          staticRootPath: this.app.config.static.prefix,
          siteSeo: this.app.config.siteSeo,
          logoSrc: `/static/images/${config.branch}/logo.png`,
          ...(await this.getEnvConfig()), // 页脚信息
          navigation: this.app.config.navigation,
          // showImgCode,
        });
      }
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  async getHelpPage() {
    const { ctx, config } = this;
    try {
      await ctx.render(`${config.branch}/help.html`, {
        staticRootPath: this.app.config.static.prefix,
        siteSeo: this.app.config.siteSeo,
        logoSrc: `/static/images/${config.branch}/logo.png`,
        ...(await this.getEnvConfig()), // 页脚信息
        // showImgCode,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  async codeLoginAction() {
    const { ctx, config } = this;
    try {
      const fields = ctx.request.body || { code: '', messageCode: '' },
        countryCode = '86',
        code = fields.userName.trim(),
        // messageCode = fields.messageCode.trim(),
        initPassword = fields.password.trim();

      let errMsg = '',
        adminOrg = {},
        adminUser = {};

      if (_.isEmpty(code)) {
        errMsg = '请填写统一社会信用代码！';
        throw new Error(errMsg);
      }

      if (
        !/^([0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}|[0-9a-hj-npqrtuwxy]{2}\d{6}[0-9a-hj-npqrtuwxy]{10}|[1-9]\d{14}|\d{18})$/.test(
          code
        )
      ) {
        errMsg = '请检查统一社会信用代码格式！';
        throw new Error(errMsg);
      }

      // if (_.isEmpty(messageCode) && _.isEmpty(initPassword)) {
      //   errMsg = '请填写验证码或者初始密码！';
      //   throw new Error(errMsg);
      // }

      if (_.isEmpty(initPassword)) {
        errMsg = '请输入密码！';
        throw new Error(errMsg);
      }

      adminOrg = await ctx.service.adminorg.item(ctx, {
        query: { code },
        files: { _id: 1, adminUserId: 1, licensePic: 1 },
      });

      if (!adminOrg) {
        errMsg = '对不起！无此企业相关数据！注册企业后可通过此方式登录！';
        throw new Error(errMsg);
      }

      if (!adminOrg.adminUserId) {
        errMsg = '对不起！该企业没有绑定初始用户！';
        throw new Error(errMsg);
      }

      adminUser = await ctx.service.adminUser.item(ctx, {
        query: { _id: adminOrg.adminUserId },
        files: { _id: 1, phoneNum: 1, password: 1 },
      });

      if (!adminUser) {
        errMsg = '对不起！无此企业相关数据！注册企业后可通过此方式登录！';
        ctx.helper.clearCache(
          countryCode + adminUser.phoneNum,
          '_sendMessage_loginCode_'
        );
        throw new Error(errMsg);
      }
      // box解密密码
      let password = '';
      const decryptRes = await ctx.curl(
        `${this.config.iServiceHost}/crypto/decrypt`,
        {
          method: 'POST',
          dataType: 'json', // 返回的数据类型
          data: {
            ciphertext: fields.password,
            fontPublicKey: fields.publicKey,
          },
        }
      );
      if (decryptRes.status !== 200) {
        throw new Error('密码解密失败');
      }
      password = decryptRes.data.data.password;

      if (!_.isEmpty(password)) {
        // 解码校验
        const hashPassword = ctx.helper.hashSha256(
          password,
          config.salt_sha2_key
        );
        if (!(adminUser.password === hashPassword)) {
          // 密码不对
          throw new Error(ctx.__('validate_login_notSuccess_2'));
        }
      }

      const adminUserToken = jwt.sign(
        {
          _id: adminUser._id,
          EnterpriseID: adminOrg._id,
        },
        this.app.config.encrypt_key,
        {
          expiresIn: '30day',
        }
      );

      ctx.cookies.set(
        'admin_' + this.app.config.auth_cookie_name,
        adminUserToken,
        {
          path: '/',
          maxAge: 1000 * 60 * 60 * 24 * 30,
          signed: true,
          httpOnly: false,
        }
      ); // cookie 有效期30天

      ctx.helper.clearCache(
        countryCode + adminUser.phoneNum,
        '_sendMessage_loginCode_'
      );
      ctx.auditLog('登录操作', `未知用户正在通过营业执照 ${code} 执行登录。`);

      const loginPerson = adminUser.userName;
      // await ctx.model.Adminorg.updateOne(
      //   { _id: adminOrg._id },
      //   {
      //     $set: {
      //       'loginRecord.loginTime': new Date(),
      //       'loginRecord.loginPerson': loginPerson,
      //     },
      //   }
      // );
      await ctx.service.db.updateOne(
        'Adminorg',
        { _id: adminOrg._id },
        {
          $set: {
            'loginRecord.loginTime': new Date(),
            'loginRecord.loginPerson': loginPerson,
          },
        }
      );

      ctx.service.operateLog.create('AdminUser', {
        optType: 'login',
        supplementaryNotes: '用户登录',
        optUserId: adminUser._id,
      });
      if (!adminOrg.licensePic) {
        console.log('无营业执照照片');
        ctx.helper.renderSuccess(ctx, {
          data: {
            leadin: true,
          },
          message: ctx.__('validate_user_loginOk'),
        });
      } else {
        console.log('有营业执照照片');
        ctx.helper.renderSuccess(ctx, {
          data: {
            token: adminUserToken,
          },
        });
      }
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  async loginAction() {
    const { ctx, config } = this;
    try {
      const fields = ctx.request.body || {};
      const systemConfigs = await ctx.service.systemConfig.find({
        isPaging: '0',
      });
      // console.log(fields);
      const { showImgCode } = systemConfigs[0];

      // let errMsg = '';
      if (showImgCode) {
        // 获取验证码ID
        const captchaId = ctx.cookies.get('captchaId', { signed: true });
        if (!captchaId) {
          ctx.helper.renderCustom(ctx, {
            status: 400,
            message:
              '验证码已过期，请重新获取。',
          });
          return;
        }
        // 从Redis中获取验证码
        const captchaKey = `captcha:${captchaId}`;
        const validCode = await ctx.app.redis.get(captchaKey);
        // 验证码不存在或不匹配
        if (!validCode || fields.imageCode !== validCode) {
          console.log('验证码错误', fields.imageCode, validCode);
          ctx.helper.renderCustom(ctx, {
            status: 400,
            message:
              ctx.__('validate_inputCorrect', [ ctx.__('label_user_imageCode') ]),
          });
          return;
        }

        // 验证通过后，立即从Redis中删除验证码，确保一次性使用
        await ctx.app.redis.del(captchaKey);
      }

      // if (
      //   showImgCode &&
      //   (!fields.imageCode || fields.imageCode !== ctx.session.imageCode)
      // ) {
      //   errMsg = ctx.__('validate_inputCorrect', [
      //     ctx.__('label_user_imageCode'),
      //   ]);
      // }

      // if (errMsg) {
      //   throw new Error(errMsg);
      // }

      const formObj = {
        userName: fields.userName.trim(),
        password: fields.password.trim(),
      };
      // console.log(adminUserRule.login);
      ctx.validate(adminUserRule.login(ctx), formObj);

      let user = await ctx.service.adminUser.item(ctx, {
        query: {
          $or: [{ userName: formObj.userName }, { phoneNum: formObj.userName }],
        },
        populate: [
          {
            path: 'group',
            select: 'power _id enable name',
          },
        ],
        files:
          'enable password _id email userName logo loginAttempts loginAttemptsTimestamp passwordExpiresAt ',
      });

      // console.log('查询本次登录用户', user);
      // xxn add
      let trialUser = null;
      if (_.isEmpty(user) || !user.enable) {
        // 查询是否为试用账号
        trialUser = await ctx.service.trialAccount.findOne({
          userName: formObj.userName,
          enable: true,
          userId: { $exists: true },
        });
        if (trialUser) {
          // 有试用账号
          if (
            new Date(trialUser.effectiveDate).getTime() <= new Date().getTime()
          ) {
            if (!user) {
              // 没有正式账号
              ctx.helper.renderFail(ctx, {
                message:
                  '您的试用账号已到期，请点击注册申请正式账号或者联系客服。',
              });
              return;
            }
          } else {
            user = JSON.parse(JSON.stringify(trialUser));
          }
        }
      }

      if (!_.isEmpty(user)) {
        // 判断是否锁定 为锁定,否则根据配置是否可以自动解锁
        const limitLoginAttemps = config.limitLoginAttemps; // 最大尝试登录次数
        const loginAttemptsTimeRange = config.loginAttemptsTimeRange; // 登录尝试时间范围
        const lockedLoginTime = config.lockedLoginTime; // 登录锁定时间
        const ifAutoUnlock = config.ifAutoUnlock; // 登录失败后是否自动解锁
        let loginAttempts = Number(user.loginAttempts || 0);
        let loginAttemptsTimestamp = user.loginAttemptsTimestamp || [];
        // 次数超过限制并且在锁定时间范围内
        if (
          loginAttempts >= limitLoginAttemps &&
          loginAttemptsTimestamp.length > 0 &&
          new Date(
            loginAttemptsTimestamp[loginAttemptsTimestamp.length - 1]
          ).getTime() +
            lockedLoginTime >
            new Date().getTime()
        ) {
          // 将剩余解锁时间转换为x分钟x秒格式
          const lockedTime =
            new Date(
              loginAttemptsTimestamp[loginAttemptsTimestamp.length - 1]
            ).getTime() +
            lockedLoginTime -
            new Date().getTime();
          const lockedMinutes = Math.floor(lockedTime / 1000 / 60);
          const lockedSeconds = Math.floor((lockedTime / 1000) % 60);
          throw new Error(
            `账号已锁定，请${lockedMinutes}分钟${lockedSeconds}秒后再试，或手机验证码登录解锁`
          );
        } else if (loginAttempts > limitLoginAttemps && ifAutoUnlock) {
          // 已经锁过了,并且支持自动解锁，现在解锁 需要将登录次数和登录时间清空
          loginAttempts = 0;
          loginAttemptsTimestamp = [];
          await ctx.service.adminUser.update(ctx, user._id, {
            loginAttempts,
            loginAttemptsTimestamp,
          });
        } else if (loginAttempts > limitLoginAttemps && !ifAutoUnlock) {
          throw new Error('账号已锁定，请联系管理员解锁');
        }
        if (!user.enable) {
          throw new Error(ctx.__('validate_user_forbiden'));
        }
        // 解码校验
        // box解密密码
        let password = '';
        const decryptRes = await ctx.curl(
          `${this.config.iServiceHost}/crypto/decrypt`,
          {
            method: 'POST',
            dataType: 'json', // 返回的数据类型
            data: {
              ciphertext: fields.password,
              fontPublicKey: fields.publicKey,
            },
          }
        );
        if (decryptRes.status !== 200) {
          throw new Error('密码解密失败');
        }
        password = decryptRes.data.data.password;
        const hashPassword = ctx.helper.hashSha256(
          password,
          config.salt_sha2_key
        );
        if (!(user.password === hashPassword)) {
          console.log('密码错误', user._id);
          loginAttempts++;
          loginAttemptsTimestamp.push(new Date().getTime());
          await ctx.service.adminUser.update(ctx, user._id, {
            loginAttempts,
            loginAttemptsTimestamp,
          });
          // 从loginAttemptsTimestamp中筛选出在配置的连续登录时间范围内的登录时间次数
          const loginAttemptsTimestampInRange = loginAttemptsTimestamp.filter(
            item => {
              return (
                item >
                loginAttemptsTimestamp[loginAttemptsTimestamp.length - 1] -
                  loginAttemptsTimeRange
              );
            }
          );
          // 在配置的连续登录时间范围内连续登录是否超过限制次数
          if (loginAttemptsTimestampInRange.length <= limitLoginAttemps) {
            throw new Error(
              `密码错误，您还有${
                limitLoginAttemps - loginAttemptsTimestampInRange.length
              }次机会,${Math.floor(
                loginAttemptsTimeRange / 1000 / 60
              )}分钟内连续登录错误${limitLoginAttemps}次账号将被锁定${Math.floor(
                lockedLoginTime / 1000 / 60
              )}分钟`
            );
          }
          throw new Error(ctx.__('validate_login_notSuccess_2'));
        }
        // 判断密码是否过期-如果没有设置
        let passwordExpiresAt = user.passwordExpiresAt;
        if (!passwordExpiresAt) {
          passwordExpiresAt = new Date();
          // 更新字段进行有效期设置
          await ctx.service.adminUser.update(ctx, user._id, {
            passwordExpiresAt,
          });
        }
        // if ((passwordExpiresAt.getTime() + config.passwordExpiresIn) < new Date().getTime()) {
        //   throw new Error('密码已过期，请联系管理员重置密码');
        // }
        const adminUserId = trialUser ? trialUser.userId : user._id;
        const adminUserToken = jwt.sign(
          {
            _id: adminUserId,
          },
          this.app.config.encrypt_key,
          {
            expiresIn: '30day',
          }
        );
        // console.log('生成本次登录的用户token', adminUserToken);

        ctx.cookies.set(
          'admin_' + this.app.config.auth_cookie_name,
          adminUserToken,
          {
            path: '/',
            maxAge: 1000 * 60 * 60 * 24 * 30,
            signed: true,
            httpOnly: false,
          }
        ); // cookie 有效期30天
        ctx.auditLog(
          '登录操作',
          `未知用户正在通过用户名 ${formObj.userName} 执行登录。`
        );
        ctx.service.operateLog.create('AdminUser', {
          optType: 'login',
          supplementaryNotes: '用户登录',
          optUserId: adminUserId,
        });

        // const targetOrg = await ctx.model.Adminorg.findOne({
        const targetOrg = await ctx.service.db.findOne('Adminorg', {
          $or: [{ adminUserId }, { adminArray: { $all: [ adminUserId ] } }],
        });
        // 这里是登录成功之后都会走的地方 在这里进行登录时间的记录
        const loginPerson = formObj.userName;
        // await ctx.model.Adminorg.updateOne(
        //   { _id: targetOrg._id },
        //   {
        //     $set: {
        //       'loginRecord.loginTime': new Date(),
        //       'loginRecord.loginPerson': loginPerson,
        //     },
        //   }
        // );
        await ctx.service.db.updateOne(
          'Adminorg',
          { _id: targetOrg._id },
          {
            $set: {
              'loginRecord.loginTime': new Date(),
              'loginRecord.loginPerson': loginPerson,
            },
          }
        );
        if (!_.isEmpty(targetOrg)) {
          const adminUserToken = jwt.sign(
            {
              _id: adminUserId,
              EnterpriseID: targetOrg._id,
              isTrialUser: trialUser ? trialUser._id : false, // 试用账号
            },
            this.app.config.encrypt_key,
            {
              expiresIn: '30day',
            }
          );
          // console.log('生成本次登录的用户token', adminUserToken);

          ctx.cookies.set(
            'admin_' + this.app.config.auth_cookie_name,
            adminUserToken,
            {
              path: '/',
              maxAge: 1000 * 60 * 60 * 24 * 30,
              signed: true,
              httpOnly: false,
            }
          ); // cookie 有效期30天

          // console.log('企业：%s的管理员%s已登录', targetOrg.cname, ctx.session.adminUserInfo, ctx.cookies);
          ctx.helper.renderSuccess(ctx, {
            data: {
              token: adminUserToken,
            },
          });
        } else {
          console.log('管理员%s已登录', ctx.session.adminUserInfo);
          ctx.helper.renderSuccess(ctx, {
            data: {
              token: adminUserToken,
            },
          });

          // 没有找到企业，要跳转到企业申请界面
          // ctx.redirect('/enterprise/orgapply');
        }
      } else {
        console.log(ctx.__('validate_login_notSuccess'));
        ctx.helper.renderFail(ctx, {
          message: ctx.__('validate_login_notSuccess'),
        });
      }
    } catch (err) {
      // console.log('--err--', err)
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }
  // 验证码登录
  async smloginAction() {
    const { config, ctx } = this;
    try {
      const fields = ctx.request.body || {};
      const messageCode = fields.messageCode.trim();
      const imageCode = fields.imageCode || '';
      // console.log('============进来没有');
      // console.log(fields);
      let errMsg = '',
        loginType = fields.loginType || '1'; // 1:手机验证码登录 2:手机号密码登录 3:邮箱密码登录

      // TODO 临时兼容没有改动的APP端
      if (fields.phoneNum && fields.password) {
        loginType = 2;
      }

      if (fields.email && fields.password) {
        loginType = 3;
      }

      if (
        loginType !== '1' &&
        loginType !== '2' &&
        loginType !== '3' &&
        loginType !== '4'
      ) {
        throw new Error(ctx.__('validate_error_params'));
      }

      let queryUserObj = {
        $or: [
          {
            phoneNum: fields.phoneNum.trim(),
          },
          {
            phoneNum: '0' + fields.phoneNum.trim(),
          },
        ],
        // countryCode: fields.countryCode,
      };

      if (
        loginType !== '3' &&
        loginType !== '4' &&
        fields.phoneNum.indexOf('0') === '0'
      ) {
        queryUserObj = {
          $or: [
            {
              phoneNum: fields.phoneNum,
            },
            {
              phoneNum: fields.phoneNum.substr(1),
            },
          ],
          countryCode: fields.countryCode,
        };
      }

      const userObj = {};
      if (loginType === '1') {
        _.assign(userObj, queryUserObj);
      } else if (loginType === '2') {
        _.assign(userObj, queryUserObj);
      } else if (loginType === '3') {
        _.assign(userObj, {
          email: fields.email,
        });
      } else if (loginType === '4') {
        _.assign(userObj, {
          email: fields.email,
        });
        queryUserObj = {
          email: fields.email,
        };
      }
      console.log('--------------------------------------');
      console.log(queryUserObj);
      // 初级校验
      const userCount = await ctx.service.adminUser.count(queryUserObj);
      queryUserObj.enable = true;
      const userCountStrict = await ctx.service.adminUser.count(queryUserObj);
      let trialUser = null;
      const query = trialUser ? { _id: trialUser.userId } : userObj;
      const user = await ctx.service.adminUser.item(ctx, {
        query,
        files: this.getAuthUserFields('login'),
      });

      if (loginType === '1' || loginType === '2') {
        if (
          !fields.phoneNum ||
          !validatorUtil.checkPhoneNum(fields.phoneNum.toString())
        ) {
          errMsg = ctx.__('validate_inputCorrect', [
            ctx.__('label_user_phoneNum'),
          ]);
        }

        if (!fields.countryCode) {
          errMsg = ctx.__('validate_selectNull', [
            ctx.__('label_user_countryCode'),
          ]);
        }

        if (loginType === '2') {
          if (!validatorUtil.checkPwd(fields.password, 6, 12)) {
            errMsg = ctx.__('validate_rangelength', [
              ctx.__('label_user_password'),
              6,
              12,
            ]);
          }
        } else if (loginType === '1') {
          console.log('在验证码登录这里调用了getCache');
          if (_.isEmpty(user)) {
            errMsg = ctx.__('validate_user_empty');
          }
          // 判断是否锁定 为锁定,否则根据配置是否可以自动解锁
          const limitLoginAttemps = config.limitLoginAttemps; // 最大尝试登录次数
          const loginAttemptsTimeRange = config.loginAttemptsTimeRange; // 登录尝试时间范围
          const lockedLoginTime = config.lockedLoginTime; // 登录锁定时间
          const ifAutoUnlock = config.ifAutoUnlock; // 登录失败后是否自动解锁
          let loginAttempts = Number(user.smsLoginAttempts || 0);
          let loginAttemptsTimestamp = user.smsLoginAttemptsTimestamp || [];
          // 次数超过限制并且在锁定时间范围内
          if (
            loginAttempts >= limitLoginAttemps &&
            loginAttemptsTimestamp.length > 0 &&
            new Date(
              loginAttemptsTimestamp[loginAttemptsTimestamp.length - 1]
            ).getTime() +
              lockedLoginTime >
              new Date().getTime()
          ) {
            // 将剩余解锁时间转换为x分钟x秒格式
            const lockedTime =
              new Date(
                loginAttemptsTimestamp[loginAttemptsTimestamp.length - 1]
              ).getTime() +
              lockedLoginTime -
              new Date().getTime();
            const lockedMinutes = Math.floor(lockedTime / 1000 / 60);
            const lockedSeconds = Math.floor((lockedTime / 1000) % 60);
            throw new Error(
              `账号已锁定，请${lockedMinutes}分钟${lockedSeconds}秒后再试`
            );
          } else if (loginAttempts > limitLoginAttemps && ifAutoUnlock) {
            // 已经锁过了,并且支持自动解锁，现在解锁 需要将登录次数和登录时间清空
            loginAttempts = 0;
            loginAttemptsTimestamp = [];
            await ctx.service.adminUser.update(ctx, user._id, {
              smsLoginAttempts: loginAttempts,
              smsLoginAttemptsTimestamp: loginAttemptsTimestamp,
            });
          } else if (loginAttempts > limitLoginAttemps && !ifAutoUnlock) {
            throw new Error('账号已锁定，请联系管理员解锁');
          }
          const currentCode = await ctx.helper.getCache(
            config.session_secret +
              '_sendMessage_login_' +
              (fields.countryCode + fields.phoneNum.trim() + imageCode)
          );
          if (
            !messageCode ||
            !validator.isNumeric(messageCode.toString()) ||
            messageCode.length !== 6 ||
            currentCode !== messageCode
          ) {
            if (loginAttempts < limitLoginAttemps) {
              loginAttempts++;
              loginAttemptsTimestamp.push(new Date().getTime());
            } else {
              loginAttempts = 1;
              loginAttemptsTimestamp = [ new Date().getTime() ];
            }
            await ctx.service.adminUser.update(ctx, user._id, {
              smsLoginAttempts: loginAttempts,
              smsLoginAttemptsTimestamp: loginAttemptsTimestamp,
            });
            // 从loginAttemptsTimestamp中筛选出在配置的连续登录时间范围内的登录时间次数
            const loginAttemptsTimestampInRange = loginAttemptsTimestamp.filter(
              item => {
                return (
                  item >
                  loginAttemptsTimestamp[loginAttemptsTimestamp.length - 1] -
                    loginAttemptsTimeRange
                );
              }
            );
            // 在配置的连续登录时间范围内连续登录是否超过限制次数
            if (loginAttemptsTimestampInRange.length <= limitLoginAttemps) {
              throw new Error(
                `验证码错误，您还有${
                  limitLoginAttemps - loginAttemptsTimestampInRange.length
                }次机会,${Math.floor(
                  loginAttemptsTimeRange / 1000 / 60
                )}分钟内连续登录错误${limitLoginAttemps}次账号将被锁定${Math.floor(
                  lockedLoginTime / 1000 / 60
                )}分钟`
              );
            }
            throw new Error(ctx.__('validate_login_notSuccess_2'));
            // errMsg = ctx.__('validate_inputCorrect', [
            //   ctx.__('label_user_imageCode'),
            // ]);
          } else {
            loginAttempts = 0;
            loginAttemptsTimestamp = [];
            await ctx.service.adminUser.update(ctx, user._id, {
              loginAttempts,
              loginAttemptsTimestamp,
              smsLoginAttempts: loginAttempts,
              smsLoginAttemptsTimestamp: loginAttemptsTimestamp,
            });
          }
        }
      } else if (loginType === '3') {
        if (!validatorUtil.checkEmail(fields.email)) {
          errMsg = ctx.__('validate_inputCorrect', [
            ctx.__('label_user_email'),
          ]);
        }
        if (!validatorUtil.checkPwd(fields.password, 6, 12)) {
          errMsg = ctx.__('validate_rangelength', [
            ctx.__('label_user_password'),
            6,
            12,
          ]);
        }
      } else if (loginType === '4') {
        if (!validatorUtil.checkEmail(fields.email)) {
          errMsg = ctx.__('validate_inputCorrect', [
            ctx.__('label_user_email'),
          ]);
        }
        const currentCode = await ctx.helper.get(
          config.session_secret + '_sendMessage_login_' + fields.email
        );
        if (
          !messageCode ||
          !validator.isNumeric(messageCode.toString()) ||
          messageCode.length !== 6 ||
          currentCode !== messageCode
        ) {
          errMsg = ctx.__('validate_inputCorrect', [
            ctx.__('label_user_imageCode'),
          ]);
        }
      }

      if (errMsg) {
        return ctx.helper.renderCustom(ctx, {
          status: 400,
          message: errMsg,
        });
      }

      if (!userCountStrict) {
        if (config.branch === 'hz') {
          ctx.helper.renderFail(ctx, {
            message: '未查询到相关手机号，请使用收到提醒短信的手机号码登录',
          });
          return;
        }
        // 查询是否为试用账号 xxn add
        trialUser = await ctx.service.trialAccount.findOne({
          ...queryUserObj,
          enable: true,
          userId: { $exists: true },
        });
        if (trialUser) {
          // 有试用账号
          if (
            new Date(trialUser.effectiveDate).getTime() <= new Date().getTime()
          ) {
            if (userCount === 0) {
              // 没有正式账号
              ctx.helper.renderFail(ctx, {
                message:
                  '您的试用账号已到期，请点击注册申请正式账号或者联系客服。',
              });
              return;
            }
            trialUser = null; // 正式账号不能登录，试用账号已过期
          }
        }
      }
      if (
        userCount > 0 ||
        loginType === '2' ||
        loginType === '3' ||
        trialUser
      ) {
        // 校验登录用户合法性
        console.log('看这里的user');
        // console.log(user);
        if (!_.isEmpty(user)) {
          if (loginType === '2') {
            const pwd = ctx.helper.decrypt(
              user.password || '',
              config.encrypt_key
            );
            if (pwd !== fields.password) {
              throw new Error(ctx.__('validate_login_notSuccess_1'));
            }
          }
        }
        if (!user) {
          throw new Error(ctx.__('validate_login_notSuccess_1'));
        } else {
          if (!user.enable) {
            throw new Error(ctx.__('validate_user_forbiden'));
          }

          if (!user.loginActive) {
            await ctx.service.adminUser.update(ctx, user._id, {
              loginActive: true,
            });
          }
        }
        ctx.auditLog(
          '登录操作',
          `${trialUser ? '试用' : ''}用户正在通过手机号 ${
            fields.phoneNum
          } 执行登录。`
        );

        ctx.service.operateLog.create('AdminUser', {
          optType: 'login',
          supplementaryNotes: '用户登录',
          optUserId: user._id,
        });
        // 查询公司
        const targetOrg = await ctx.service.adminorg.item(ctx, {
          query: {
            $or: [
              {
                adminUserId: user._id,
              },
              {
                adminArray: { $all: [ user._id ] },
              },
            ],
          },
          populate: 'none',
        });
        if (!_.isEmpty(targetOrg)) {
          const adminUserToken = JSON.parse(JSON.stringify(user));
          console.log('在jwt这');
          // 针对 App 端同时创建 Token
          adminUserToken.token = jwt.sign(
            {
              EnterpriseID: targetOrg._id,
              _id: user._id,
              isTrialUser: trialUser ? trialUser._id : false, // 试用账号
            },
            config.encrypt_key,
            {
              expiresIn: '30day',
            }
          );
          console.log('在cookie这');
          // 将cookie存入缓存
          ctx.cookies.set(
            'admin_' + config.auth_cookie_name,
            adminUserToken.token,
            {
              path: '/',
              maxAge: 1000 * 60 * 60 * 24 * 30,
              signed: true,
              httpOnly: false,
            }
          ); // cookie 有效期30天
          const loginPerson = user.userName;
          // await ctx.model.Adminorg.updateOne(
          //   { _id: targetOrg._id },
          //   {
          //     $set: {
          //       'loginRecord.loginTime': new Date(),
          //       'loginRecord.loginPerson': loginPerson,
          //     },
          //   }
          // );
          await ctx.service.db.updateOne(
            'Adminorg',
            { _id: targetOrg._id },
            {
              $set: {
                'loginRecord.loginTime': new Date(),
                'loginRecord.loginPerson': loginPerson,
              },
            }
          );
          console.log(
            '企业：%s的管理员%s已登录',
            targetOrg.cname,
            ctx.session.adminUserInfo,
            ctx.cookies
          );

          // 重置验证码
          const endStr =
            loginType === '3'
              ? fields.email
              : fields.countryCode + fields.phoneNum;
          ctx.helper.clearCache(endStr, '_sendMessage_login_');
          // console.log('--111---',renderUser)
          if (!targetOrg.licensePic) {
            ctx.helper.renderSuccess(ctx, {
              data: {
                leadin: true,
              },
              message: ctx.__('validate_user_loginOk'),
            });
          } else {
            ctx.helper.renderSuccess(ctx, {
              data: adminUserToken,
              message: ctx.__('validate_user_loginOk'),
            });
          }
        } else {
          // 针对 App 端同时创建 Token
          const apptoken = jwt.sign(
            {
              _id: user._id,
            },
            config.encrypt_key,
            {
              expiresIn: '30day',
            }
          );
          console.log('在cookie这');
          // 将cookie存入缓存
          ctx.cookies.set('admin_' + config.auth_cookie_name, apptoken, {
            path: '/',
            maxAge: 1000 * 60 * 60 * 24 * 30,
            signed: true,
            httpOnly: false,
          }); // cookie 有效期30天

          const endStr =
            loginType === '3'
              ? fields.email
              : fields.countryCode + fields.phoneNum;
          ctx.helper.clearCache(endStr, '_sendMessage_login_');
          console.log('管理员%s已登录', ctx.session.adminUserInfo);
          ctx.helper.renderSuccess(ctx, {
            data: apptoken,
            message: ctx.__('validate_user_loginOk'),
          });
          return false;

          // 没有找到企业，要跳转到企业申请界面
          // ctx.redirect('/enterprise/orgapply');
        }
      } else {
        // 没有该用户数据，新建该用户
        const createUserObj = {
          group: config.groupID.adminGroupID,
          creativeRight: false,
          loginActive: true,
          enable: true,
        };

        if (loginType === '1') {
          createUserObj.phoneNum = fields.phoneNum.trim();
          createUserObj.countryCode = fields.countryCode.trim();
          createUserObj.userName = fields.phoneNum.trim();
        } else if (loginType === '4') {
          createUserObj.email = fields.email;
          createUserObj.userName = fields.email;
        }

        const currentUser = await ctx.service.adminUser.create(createUserObj);
        if (currentUser && currentUser._id) {
          this.ctx.service.user.bindUserToAdminUser(currentUser._id);
        }

        const newUser = await ctx.service.adminUser.item(ctx, {
          query: {
            _id: currentUser._id,
          },
          files: this.getAuthUserFields('login'),
        });

        const targetOrg = await ctx.service.adminorg.item(ctx, {
          query: {
            adminUserId: newUser._id,
          },
          populate: 'none',
        });

        ctx.auditLog(
          '注册操作',
          `用户通过手机号 ${createUserObj.phoneNum} 注册成功。`
        );

        if (!_.isEmpty(targetOrg)) {
          // 加密
          const adminUserToken = JSON.parse(JSON.stringify(newUser));
          adminUserToken.token = jwt.sign(
            {
              EnterpriseID: targetOrg._id,
              _id: adminUserToken._id,
            },
            config.encrypt_key,
            {
              expiresIn: '30day',
            }
          );
          // 存入cookie
          ctx.cookies.set(
            'admin_' + this.app.config.auth_cookie_name,
            adminUserToken.token,
            {
              path: '/',
              maxAge: 1000 * 60 * 60 * 24 * 30,
              signed: true,
              httpOnly: false,
            }
          );
          console.log(
            '企业：%s的管理员%s已登录',
            targetOrg.cname,
            ctx.session.adminUserInfo,
            ctx.cookies
          );

          // 重置验证码
          const endStr =
            loginType === '3'
              ? fields.email
              : fields.countryCode + fields.phoneNum;
          ctx.helper.clearCache(endStr, '_sendMessage_login_');
          ctx.helper.renderSuccess(ctx, {
            data: adminUserToken.token,
            message: ctx.__('validate_user_loginOk'),
          });
        } else {
          console.log('没有查询到企业相关信息跳转到企业信息完善页面');
          // 加密
          const adminUserToken = JSON.parse(JSON.stringify(newUser));
          adminUserToken.token = jwt.sign(
            {
              _id: adminUserToken._id,
            },
            config.encrypt_key,
            {
              expiresIn: '30day',
            }
          );
          // 存入cookie
          ctx.cookies.set(
            'admin_' + this.app.config.auth_cookie_name,
            adminUserToken.token,
            {
              path: '/',
              maxAge: 1000 * 60 * 60 * 24 * 30,
              signed: true,
              httpOnly: false,
            }
          );
          // console.log('企业：%s的管理员%s已登录', targetOrg, ctx.session.adminUserInfo, ctx.cookies);

          // 重置验证码
          const endStr =
            loginType === '3'
              ? fields.email
              : fields.countryCode + fields.phoneNum;
          console.log(endStr);
          ctx.helper.clearCache(endStr, '_sendMessage_login_');
          console.log('有走这里吗');
          ctx.helper.renderSuccess(ctx, {
            data: adminUserToken.token,
            message: ctx.__('validate_user_loginOk'),
          });

          return false;
        }
      }
      // end
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  async admin() {
    const { ctx } = this;
    try {
      // let datas = await ctx.helper.reqJsonData('manage/admin/index');
      // ctx.body = datas;
      // 测试用，临时
      if (ctx.query.isError) {
        throw new Error('Error');
      }
      // 检索代号：#0001  ctx.session.basePath 后台根路径
      ctx.redirect(`${ctx.session.basePath}/dashboard`);
    } catch (e) {
      ctx.helper.renderFail(ctx, {
        message: e,
      });
    }
  }

  async adminApi() {
    const { ctx } = this;
    try {
      // 测试用，临时
      if (ctx.query.isError) {
        throw new Error('Error');
      }
      const datas = await ctx.helper.reqJsonData('admin/index');
      ctx.body = datas;
    } catch (e) {
      ctx.helper.renderFail(ctx, {
        message: e,
      });
    }
  }
  // 发送验证码
  async sendVerificationCode() {
    const { config, ctx } = this;
    try {
      const fields = ctx.request.body || {};
      // 数据清理，确保只有一个 phoneNum 字段
      const phoneNums = Array.isArray(fields.phoneNum)
        ? fields.phoneNum
        : [ fields.phoneNum ];
      const uniquePhoneNum = [ ...new Set(phoneNums) ].filter(Boolean); // 去重并过滤空值

      if (uniquePhoneNum.length > 1) {
        throw new Error('请求中包含多个手机号，请只提供一个手机号');
      }

      let phoneNum = uniquePhoneNum[0];
      if (config.shouldVerifyPhoneExistence === '1') {
        // 查询adminuser表中是否有该手机号
        const user = await ctx.service.adminUser.item(ctx, {
          query: {
            phoneNum,
          },
          files: '_id',
        });
        if (!user) {
          ctx.helper.renderFail(ctx, {
            message: '该手机号未注册',
          });
          return;
        }
      }
      const code = fields.code;
      const countryCode = fields.countryCode;
      const messageType = fields.messageType;
      const imageCode = fields.imageCode || '';

      let cacheKey = '';
      const systemConfigs = await ctx.service.systemConfig.find({
        isPaging: '0',
      });
      const { showImgCode } = systemConfigs[0];

      let isLogin = true;
      // 判断是否要图形验证码
      if (!_.isEmpty(ctx.session.adminUserInfo)) {
        isLogin = false;
        // console.log('已登录用户');
      } else {
        isLogin = true;
        // console.log('未登录用户');
      }
      let errMsg = '';
      if (isLogin) {
        if (showImgCode) {
          // 获取验证码ID
          const captchaId = ctx.cookies.get('captchaId', { signed: true });
          if (!captchaId) {
            ctx.helper.renderFail(ctx, {
              message: ctx.__('validate_inputCorrect', [ ctx.__('label_user_imageCode') ]),
            });
            return;
          }

          // 从Redis中获取验证码
          const captchaKey = `captcha:${captchaId}`;
          const validCode = await this.app.redis.get(captchaKey);

          // 验证码不存在或不匹配
          if (!validCode || fields.imageCode !== validCode) {
            errMsg = ctx.__('validate_inputCorrect', [ ctx.__('label_user_imageCode') ]);
            ctx.helper.renderCustom(ctx, {
              message: errMsg,
              status: 400,
            });
            return;
          }

          // 验证通过后，立即从Redis中删除验证码，确保一次性使用
          await this.app.redis.del(captchaKey);
        }

        // if (
        //   showImgCode &&
        //   (!fields.imageCode || fields.imageCode !== ctx.session.imageCode)
        // ) {
        //   errMsg = ctx.__('validate_inputCorrect', [
        //     ctx.__('label_user_imageCode'),
        //   ]);
        //   ctx.helper.renderFail(ctx, {
        //     message: errMsg,
        //   });
        //   return;
        // }
      }
      if (!countryCode) {
        errMsg = ctx.__('validate_selectNull', [
          ctx.__('label_user_countryCode'),
        ]);
      }

      if (!messageType) {
        errMsg = ctx.__('validate_error_params');
      } else if (messageType === '1' || messageType === '0') {
        if (!phoneNum || !validator.isNumeric(phoneNum.toString())) {
          errMsg = ctx.__('validate_inputCorrect', [
            ctx.__('label_user_phoneNum'),
          ]);
        }
      } else if (messageType === '5') {
        if (
          !_.isEmpty(code) &&
          /^([0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}|[0-9a-hj-npqrtuwxy]{2}\d{6}[0-9a-hj-npqrtuwxy]{10}|[1-9]\d{14}|\d{18})$/.test(
            code
          )
        ) {
          const targetItem = await ctx.service.adminorg.item(ctx, {
            query: {
              code,
            },
            files: {
              adminUserId: 1,
            },
          });
          if (targetItem) {
            const adminUser = await ctx.service.adminUser.item(ctx, {
              query: { _id: targetItem.adminUserId },
              files: { phoneNum: 1 },
            });
            if (adminUser) {
              phoneNum = adminUser.phoneNum;
            } else {
              errMsg = '对不起！无此企业相关数据！注册企业后可通过此方式登录！';
            }
          } else {
            errMsg = '对不起！无此企业相关数据！注册企业后可通过此方式登录！';
          }
        } else {
          errMsg = ctx.__('validate_inputCorrect', [ ctx.__('label_user_code') ]);
        }
      }
      const clientIp =
        ctx.header['x-forwarded-for'] ||
        ctx.header['x-real-ip'] ||
        ctx.request.ip;
      const ipKey = `sendCodeIp:${clientIp}`;
      const phoneKey = `sendCodePhone:${phoneNum}`;
      const ipLastSendTime = Number(await ctx.app.redis.get(ipKey));
      const phoneLastSendTime = Number(await ctx.app.redis.get(phoneKey));
      const currentTime = Date.now();

      if (
        (ipLastSendTime && currentTime - ipLastSendTime < 59000) ||
        (phoneLastSendTime && currentTime - phoneLastSendTime < 59000)
      ) {
        errMsg = '发送验证码过于频繁，请稍后再试';
      }
      const ipSendCountKey = `sendCodeIpCount:${clientIp}`;
      const phoneSendCountKey = `sendCodePhoneCount:${phoneNum}`;
      const ipSendCount = Number((await ctx.app.redis.get(ipSendCountKey))) || 0;
      const phoneSendCount = Number((await ctx.app.redis.get(phoneSendCountKey))) || 0;

      if (ipSendCount >= 3 || phoneSendCount >= 3) {
        errMsg = '发送验证码次数过多，请10分钟后再试';
      }


      if (errMsg) {
        ctx.helper.renderCustom(ctx, {
          message: errMsg,
          status: 400,
        });
        return;
      }

      // 生成短信验证码
      const currentStr = siteFunc.randomString(6, '123456789');

      if (messageType === '0') {
        // 注册验证码
        cacheKey = '_sendMessage_reg_';
      } else if (messageType === '1') {
        // 登录获取验证码
        cacheKey = '_sendMessage_login_';
      } else if (messageType === '2') {
        // 忘记资金密码获取验证码
        cacheKey = '_sendMessage_reSetFunPassword_';
      } else if (messageType === '3') {
        // 忘记登录密码找回
        cacheKey = '_sendMessage_resetPassword_';
      } else if (messageType === '4') {
        // 身份认证
        cacheKey = '_sendMessage_identity_verification_';
      } else if (messageType === '5') {
        // 营业执照登录
        cacheKey = '_sendMessage_loginCode_';
      } else if (messageType === '6') {
        // 游客绑定邮箱或手机号
        cacheKey = '_sendMessage_tourist_bindAccount_';
      } else {
        throw new Error(ctx.__('validate_error_params'));
      }

      const endStr = countryCode + phoneNum + imageCode;
      const currentKey = config.session_secret + cacheKey + endStr;
      await ctx.app.redis.set(ipKey, currentTime, 'EX', 600);
      await ctx.app.redis.set(phoneKey, currentTime, 'EX', 600);
      await ctx.app.redis.set(ipSendCountKey, ipSendCount + 1, 'EX', 600);
      await ctx.app.redis.set(phoneSendCountKey, phoneSendCount + 1, 'EX', 600);
      console.log(currentStr, '---currentKey---', currentKey);
      // const sendMessageType = await ctx.helper.getCache(currentKey + '_limit');
      // if (sendMessageType) {
      //   ctx.helper.renderFail(ctx, {
      //     message: '发送太频繁，请稍后再试',
      //   });
      //   return;
      // }
      ctx.helper.setCache(currentKey, currentStr, 1000 * 60 * 15); // 验证码缓存15分钟

      // 发送短消息;
      const { data } = await this.ctx.curl(
        `${this.config.iServiceHost}/api/sendSMS`,
        {
          method: 'POST',
          dataType: 'json', // 返回的数据类型
          data: {
            templateCodeName: 'verification',
            TemplateParam: JSON.stringify({ code: currentStr.toString() }),
            PhoneNumbers: phoneNum,
          },
        }
      );
      // 限制 2 分钟内只能发送一次
      // ctx.helper.setCache(currentKey + '_limit', 'limit', 1000 * 60 * 2);

      if (!data || !data.data || data.data.Message !== 'OK') {
        this.ctx.auditLog(
          `短信发送失败 - ${phoneNum}`,
          JSON.stringify(data),
          'error'
        );
        return ctx.helper.renderFail(ctx, {
          data,
          message: '短信发送失败',
        });
      }
      // console.log('发送短消息: ', data);
      phoneNum = phoneNum.replace(
        /(\w{3})(\w*)(\w{4})$/g,
        function(all, u1, u2, u3) {
          return u1 + new Array(u2.length + 1).join('*') + u3;
        }
      );

      const message =
        messageType === '5'
          ? `验证码已发送到手机号：${phoneNum}，请注意查收！`
          : ctx.__('restful_api_response_success', [
            ctx.__('user_action_tips_sendMessage'),
          ]);

      ctx.helper.renderSuccess(ctx, {
        message,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }
  // 注册
  async regAction() {
    const { config, ctx } = this;
    try {
      const fields = ctx.request.body || {};
      let errMsg = '';
      const regType = fields.regType || '1'; // 1:手机号注册  2:邮箱注册

      if (regType !== '1' && regType !== '2') {
        throw new Error(ctx.__('validate_error_params'));
      }

      if (regType === '1') {
        console.log('进到手机验证了');
        if (
          !fields.phoneNum ||
          !validatorUtil.checkPhoneNum(fields.phoneNum.toString())
        ) {
          errMsg = ctx.__('validate_inputCorrect', [
            ctx.__('label_user_phoneNum'),
          ]);
        }

        if (!fields.countryCode) {
          errMsg = ctx.__('validate_selectNull', [
            ctx.__('label_user_countryCode'),
          ]);
        }
      } else if (regType === '2') {
        if (!validatorUtil.checkEmail(fields.email)) {
          errMsg = ctx.__('validate_inputCorrect', [
            ctx.__('label_user_email'),
          ]);
        }
      }

      const endStr =
        regType === '1'
          ? fields.countryCode + fields.phoneNum.trim()
          : fields.email;
      console.log(config.session_secret + '_sendMessage_reg_' + endStr);
      console.log('在注册这里调用了getCache');
      const currentCode = await ctx.helper.getCache(
        config.session_secret + '_sendMessage_reg_' + endStr
      );
      if (
        !validator.isNumeric(fields.messageCode.trim().toString()) ||
        fields.messageCode.trim().length !== 6 ||
        currentCode !== fields.messageCode.trim()
      ) {
        errMsg = ctx.__('validate_inputCorrect', [
          ctx.__('label_user_imageCode'),
        ]);
      }

      // if (fields.userName && !validator.isLength(fields.userName, 2, 12)) {
      //   errMsg = ctx.__('validate_rangelength', [ ctx.__('label_user_userName'), 2, 12 ]);
      // }

      // if (fields.userName && !validatorUtil.isRegularCharacter(fields.userName)) {
      //   errMsg = ctx.__('validate_error_field', [ ctx.__('label_user_userName') ]);
      // }

      // if (!validatorUtil.checkPwd(fields.password, 6, 12)) {
      //   errMsg = ctx.__('validate_rangelength', [ ctx.__('label_user_password'), 6, 12 ]);
      // }

      // box解密密码
      const decryptRes = await ctx.curl(
        `${this.config.iServiceHost}/crypto/decrypt`,
        {
          method: 'POST',
          dataType: 'json', // 返回的数据类型
          data: {
            ciphertext: fields.password,
            fontPublicKey: fields.publicKey,
          },
        }
      );
      if (decryptRes.status !== 200) {
        throw new Error('密码解密失败');
      }
      fields.password = decryptRes.data.data.password;
      // 加入规则判断
      const regx =
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#%^&*()_+{}\[\]:;<>,.?~\\/\-]).{8,12}$/;
      if (!regx.test(fields.password)) {
        ctx.body = {
          status: 400,
          msg: '密码必须包含大小写字母、数字、特殊字符，且长度为8-12位',
        };
        return;
      }
      const { name } = fields;
      // if (IDcard) {
      //   // 校验身份证
      //   const regx = /^[1-9]\d{5}(?:18|19|20)\d{2}(?:0[1-9]|10|11|12)(?:0[1-9]|[1-2]\d|30|31)\d{3}[\dXx]$/;
      //   if (!regx.test(IDcard)) {
      //     errMsg = ctx.__('validate_inputCorrect', [
      //       ctx.__('label_user_IDcard'),
      //     ]);
      //   }
      // } else {
      //   throw new Error(ctx.__('validate_inputCorrect', [
      //     ctx.__('label_user_IDcard'),
      //   ]));
      // }

      delete fields.publicKey;
      let userName = '';
      if (fields && fields.userName) {
        userName = fields.userName.trim();
      } else if (fields && fields.phoneNum) {
        userName = fields.phoneNum.trim();
      }

      if (errMsg) {
        throw new Error(errMsg);
      }

      const userObj = {
        userName,
        countryCode: fields.countryCode.trim(),
        logo: fields.logo,
        phoneNum: fields.phoneNum.trim(),
        name,
        // IDcard,
        // email: fields.email,
        group: '0',
        creativeRight: false,
        // password: ctx.helper.encrypt(fields.password, app.config.encrypt_key),
        password: ctx.helper.hashSha256(
          fields.password.trim(),
          config.salt_sha2_key
        ),
        loginActive: false,
        enable: true,
      };

      let queryUserObj = {};
      if (regType === '1') {
        queryUserObj = {
          $or: [
            {
              phoneNum: fields.phoneNum.trim(),
            },
            {
              phoneNum: '0' + fields.phoneNum.trim(),
            },
          ],
        };

        if (fields.phoneNum.indexOf('0') === '0') {
          queryUserObj = {
            $or: [
              {
                phoneNum: fields.phoneNum,
              },
              {
                phoneNum: fields.phoneNum.substr(1),
              },
            ],
          };
        }
      } else if (regType === '2') {
        queryUserObj = {
          email: fields.email,
        };
        userObj.userName = userObj.userName || fields.email;
      }
      const adminUser = await ctx.service.adminUser.item(ctx, {
        query: queryUserObj,
      });
      if (!_.isEmpty(adminUser)) {
        // throw new Error(ctx.__('validate_hadUse_userNameOrEmail'));
        ctx.helper.clearCache(endStr, '_sendMessage_reg_');
        throw new Error(ctx.__('validate_Phone'));
      } else {
        const adminuserobj = {
          userName: userObj.userName || userObj.phoneNum,
          countryCode: userObj.countryCode,
          logo: userObj.logo,
          phoneNum: userObj.phoneNum,
          name: userObj.name,
          // IDcard: userObj.IDcard,
          email: userObj.email,
          group: config.groupID.adminGroupID,
          password: fields.password,
          enable: true,
        };
        const adminuser = await ctx.service.adminUser.create(adminuserobj);
        console.log('打印adminUser创建后的返回值', adminuser);
        if (adminuser && adminuser._id) {
          this.ctx.service.user.bindUserToAdminUser(adminuser._id);
        }

        // const endUser = await ctx.service.user.create(userObj);
        // console.log(endUser);
        // ctx.session.user = await ctx.service.user.item(ctx, {
        //   query: {
        //     _id: endUser._id,
        //   },
        //   files: this.getAuthUserFields('session'),
        // });
        console.log(this.getAuthUserFields('session'));
        // console.log('==========');
        // console.log(ctx.session.user);
        // 重置验证码
        ctx.helper.clearCache(endStr, '_sendMessage_reg_');
        ctx.auditLog(
          '注册操作',
          `用户通过用户名 ${adminuserobj.phoneNum} 密码注册成功。`
        );
        const adminUserToken = jwt.sign(
          {
            _id: adminuser._id,
            isTrialUser: false, // 试用账号
          },
          this.app.config.encrypt_key,
          {
            expiresIn: '30day',
          }
        );
        // console.log('生成本次登录的用户token', adminUserToken);

        ctx.cookies.set(
          'admin_' + this.app.config.auth_cookie_name,
          adminUserToken,
          {
            path: '/',
            maxAge: 1000 * 60 * 60 * 24 * 30,
            signed: true,
            httpOnly: false,
          }
        ); // cookie 有效期30天

        ctx.helper.renderSuccess(ctx, {
          data: {
            token: adminUserToken,
          },
          message: ctx.__('validate_user_regOk'),
        });
      }
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
        status: 500,
      });
    }
  }
  // 验证申请试用账号时是否存在正式账号
  async checkRegTry() {
    const { ctx } = this;
    const fields = ctx.request.body || {};
    const phoneNum = fields.phoneNum ? fields.phoneNum.trim() : '';
    const adminUserCount = await ctx.service.adminUser.findOne({
      $or: [{ phoneNum }, { phoneNum: '0' + fields.phoneNum.trim() }],
    });
    if (adminUserCount && adminUserCount._id) {
      const adminUserId = adminUserCount._id;
      // const adminOrg = await ctx.model.Adminorg.findOne(
      const adminOrg = await ctx.service.db.findOne(
        'Adminorg',
        { $or: [{ adminUserId }, { adminArray: adminUserId }] },
        { isactive: 1 }
      );
      if (adminOrg && adminOrg.isactive === '4') {
        ctx.helper.renderFail(
          ctx,
          {
            message:
              '该号码已经是体验账号，请登录后使用，需要延长体验时长，请联系客服。',
          },
          400
        );
      } else {
        ctx.helper.renderFail(
          ctx,
          {
            message:
              '该号码已经是正式账号，请勿重复申请试用账号, 如有疑问，请联系客服。',
          },
          400
        );
      }
    } else {
      ctx.helper.renderSuccess(ctx, {
        message: '该手机号未注册正式账号',
      });
    }
  }
  // 注册试用账号 xxn
  async regTryAction() {
    const { config, ctx } = this;
    try {
      const fields = ctx.request.body || {}; // messageCode, phoneNum, countryCode, password, name, channel, Enterprise
      const phoneNum = fields.phoneNum ? fields.phoneNum.trim() : '';
      const countryCode = fields.countryCode.trim() || '86';
      const password = fields.password ? fields.password.trim() : '';
      let errMsg = '';
      // 1、手机验证
      if (!phoneNum || !validatorUtil.checkPhoneNum(phoneNum.toString())) {
        errMsg = ctx.__('validate_inputCorrect', [
          ctx.__('label_user_phoneNum'),
        ]);
      }
      // 2、验证码验证
      const endStr = countryCode + phoneNum;
      // console.log(1111111111, config.session_secret + '_sendMessage_reg_' + endStr);
      const currentCode = await ctx.helper.getCache(
        config.session_secret + '_sendMessage_reg_' + endStr
      );
      if (
        !validator.isNumeric(fields.messageCode.trim().toString()) ||
        fields.messageCode.trim().length !== 6 ||
        currentCode !== fields.messageCode.trim()
      ) {
        errMsg = ctx.__('validate_inputCorrect', [
          ctx.__('label_user_imageCode'),
        ]);
      }

      if (errMsg) throw new Error(errMsg);

      // 3、查询手机账号是否已存在
      const trialUser = await ctx.service.trialAccount.findOne({
        $or: [{ phoneNum }, { phoneNum: '0' + fields.phoneNum.trim() }],
      });
      if (!_.isEmpty(trialUser)) {
        // 4、如果已经存在就延长试用期
        ctx.helper.clearCache(endStr, '_sendMessage_reg_');
        throw new Error(ctx.__('validate_Phone'));
      } else {
        // 5、查询是否存在正式账号
        const adminUserCount = await ctx.service.adminUser.findOne({
          $or: [{ phoneNum }, { phoneNum: '0' + fields.phoneNum.trim() }],
        });
        if (adminUserCount && adminUserCount._id) {
          ctx.helper.clearCache(endStr, '_sendMessage_reg_');
          const adminUserId = adminUserCount._id;
          // const adminOrg = await ctx.model.Adminorg.findOne(
          const adminOrg = await ctx.service.db.findOne(
            'Adminorg',
            { $or: [{ adminUserId }, { adminArray: adminUserId }] },
            { isactive: 1 }
          );
          if (adminOrg && adminOrg.isactive === '4') {
            ctx.helper.renderFail(
              ctx,
              {
                message:
                  '该号码已经是体验账号，请登录后使用，需要延长体验时长，请联系客服。',
              },
              400
            );
          } else {
            ctx.helper.renderFail(
              ctx,
              {
                message:
                  '该号码已经是正式账号，请勿重复申请试用账号, 如有疑问，请联系客服。',
              },
              400
            );
          }
          return;
        }
        // 6、没有就创建试用账号
        const adminuserobj = {
          name: fields.name ? fields.name.trim() : '',
          countryCode,
          phoneNum,
          userName: phoneNum,
          channel: +fields.channel || 1,
          Enterprise: fields.Enterprise ? fields.Enterprise.trim() : '',
        };
        if (password) adminuserobj.password = password;
        const user = await ctx.service.trialAccount.create(adminuserobj);
        // 重置验证码
        ctx.helper.clearCache(endStr, '_sendMessage_reg_');

        ctx.auditLog(
          '试用注册操作',
          `用户通过手机号- ${adminuserobj.phoneNum} 注册成功。`
        );
        ctx.helper.renderSuccess(ctx, {
          message: ctx.__('validate_user_regOk'),
          data: user,
        });
      }
    } catch (err) {
      ctx.helper.renderFail(
        ctx,
        {
          message: err,
        },
        400
      );
    }
  }

  getAuthUserFields(type = '') {
    let fieldStr =
      'id userName category group logo date enable state name IDcard email password';
    if (type === 'login') {
      fieldStr =
        'id userName name password category group logo date enable state phoneNum countryCode email comments IDcard position loginActive birth password smsLoginAttempts smsLoginAttemptsTimestamp';
    } else if (type === 'base') {
      fieldStr =
        'id userName name password category group logo date enable state phoneNum countryCode email watchers followers comments IDcard favorites favoriteCommunityContent despises comments profession experience industry introduction birth creativeRight gender';
    } else if (type === 'session') {
      fieldStr =
        'id userName name password category group logo date enable state phoneNum countryCode watchers followers praiseContents praiseMessages praiseCommunityContent watchSpecials watchCommunity watchTags favorites favoriteCommunityContent despises despiseMessage despiseCommunityContent IDcard position gender vip email comments';
    }
    return fieldStr;
  }

  async aliSMSApi() {
    const { ctx } = this;
    const fields = ctx.request.body || {};
    const { apiName, params, requestOption } = fields;

    try {
      const aliback = await siteFunc.aliSMS(apiName, params, requestOption);
      ctx.body = aliback;
    } catch (e) {
      ctx.body = e;
    }
  }
  async checkUserName() {
    const { ctx } = this;
    try {
      const userName = ctx.query.userName || '';
      let isHas = false;
      if (userName !== '') {
        const adminUserInfo = await ctx.service.adminUser.item(ctx, {
          query: { userName },
          files: '_id userName -group',
        });
        if (adminUserInfo) {
          const id = ctx.session.adminUserInfo
            ? ctx.session.adminUserInfo._id
            : '';
          isHas = id !== adminUserInfo._id;
        }
        ctx.helper.renderSuccess(ctx, {
          data: { isHas },
        });
      } else {
        ctx.helper.renderSuccess(ctx, {
          data: { isHas },
        });
      }
    } catch (err) {
      ctx.auditLog(
        '错误',
        `修改用户信息检查用户名错误：${err.stack} 。`,
        'error'
      );
      ctx.helper.renderFail(ctx, { data: { isHas: false } });
    }
  }
  // 更新用户信息
  async updateUserInfo() {
    const { app, ctx } = this;
    // 读手机号码字段。如果存在手机号，要去重
    try {
      const adminUserId = ctx.session.adminUserInfo._id;
      // xxn添加：查询当前当前用户是否有phoneNum和userId
      // let needBindUserIdFlag = false;
      // const curAdminUser = await ctx.model.AdminUser.findOne({
      const curAdminUser = await ctx.service.db.findOne('AdminUser', {
        _id: adminUserId,
      });
      // if (!curAdminUser.userId && !curAdminUser.phoneNum) needBindUserIdFlag = true;
      // xxn end
      const fields = ctx.request.body,
        type = fields.type || '',
        IDcard = fields.IDcard || '',
        messageCode = fields.code || '',
        countryCode = fields.countryCode || '86',
        phoneNum = fields.phoneNum || '',
        password = fields.password || '',
        checkPass = fields.checkPass || '';
      // if (!curAdminUser.userId && !curAdminUser.phoneNum) needBindUserIdFlag = true;
      // xxn end
      const info = await validPhoneAndIDNum(ctx, 'adminUser', {
        _id: adminUserId, // 当前操作表的id 更新一定要传 创建不传
        phoneNum: fields.phoneNum || '', // 需要修改得电话号码 必填
        oldPhoneNum: curAdminUser.phoneNum || '', // 修改前得电话号码 创建的时候不传
        IDNum: fields.IDcard || '', // 身份证号 当有关身份证得操作时传
        oldIDNum: curAdminUser.IDcard || '', // 修改前的身份证 当有关身份证得操作时传
      });
      if (info) {
        ctx.helper.renderFail(ctx, {
          message: `${info.split(':')[1]}，或联系24小时客服咨询`,
        });
        return;
      }
      this.checkUserFormData(ctx, fields);
      const userObj = {};
      const sessionUser = ctx.session.adminUserInfo;
      let errMsg = '';

      if (type === '1') {
        // if (IDcard === '' || !validatorUtil.checkIDCard(IDcard)) {
        //   errMsg = '请填写正确的身份证号';
        // }
        // if (phoneNum === '' || !validatorUtil.checkPhoneNum((phoneNum).toString())) {
        //   errMsg = ctx.__('validate_inputCorrect', [ ctx.__('label_user_phoneNum') ]);
        // }
        if (countryCode === '') {
          errMsg = ctx.__('validate_selectNull', [
            ctx.__('label_user_countryCode'),
          ]);
        }
        const currentCode = await ctx.helper.getCache(
          app.config.session_secret +
            '_sendMessage_resetPassword_' +
            (fields.countryCode + fields.phoneNum.trim())
        );
        if (
          messageCode === '' ||
          !validator.isNumeric(messageCode.toString()) ||
          messageCode.length !== 6 ||
          currentCode !== messageCode
        ) {
          errMsg = ctx.__('validate_inputCorrect', [
            ctx.__('label_user_imageCode'),
          ]);
        }

        if (errMsg) {
          ctx.helper.clearCache(
            countryCode + phoneNum,
            '_sendMessage_resetPassword_'
          );
          return ctx.helper.renderCustom(ctx, {
            status: 400,
            message: errMsg,
          });
        }

        if (password !== '' && password !== checkPass) {
          ctx.helper.clearCache(
            countryCode + phoneNum,
            '_sendMessage_resetPassword_'
          );
          return ctx.helper.renderCustom(ctx, {
            status: 400,
            message: errMsg,
          });
        }
      }

      if (fields.enable !== 'undefined' && fields.enable !== undefined) {
        userObj.enable = fields.enable;
      }

      // 查询是不是已存在该手机号
      // let isExist = siteFunc.checkExistPhoneNumber(fields.phoneNum);
      if (type !== '2') {
        const curUsers = await ctx.service.adminUser.find(
          { isPaging: '0' },
          {
            query: {
              $or: [
                {
                  phoneNum: fields.phoneNum,
                },
                // {
                //   userName: fields.phoneNum,
                // },
              ],
            },
            files: '_id userName phoneNum password',
          }
        );
        if (curUsers.length > 0 && !(phoneNum === sessionUser.phoneNum)) {
          const adminUserId = curUsers[0]._id;
          const adminOrgCount = await ctx.service.adminorg.count({
            $or: [{ adminUserId }, { adminArray: { $all: [ adminUserId ] } }],
          });
          if (adminOrgCount < 1 && curUsers.userName === curUsers.phoneNum) {
            // 删除用户
            await ctx.service.adminUser.removes(ctx, curUsers[0]._id);
          } else {
            ctx.helper.clearCache(
              countryCode + phoneNum,
              '_sendMessage_resetPassword_'
            );
            return ctx.helper.renderCustom(ctx, {
              status: 400,
              message: '该手机号码已存在！请更换新手机号码！',
            });
          }
        }

        userObj.phoneNum = phoneNum;
        if (
          curUsers.length > 0 &&
          curUsers[0].password &&
          curUsers[0].password !== ''
        ) {
          userObj.password = curUsers[0].password;
        }
      }

      if (fields.userName) {
        userObj.userName = fields.userName;
      }
      if (fields.email) {
        userObj.email = fields.email;
      }
      if (fields.name) {
        userObj.name = fields.name;
      }
      if (IDcard) {
        userObj.IDcard = IDcard;
      }
      if (fields.expireDate) {
        userObj.expireDate = fields.expireDate;
      }
      if (fields.gender) {
        userObj.gender = fields.gender;
      }

      if (fields.logo) {
        userObj.logo = fields.logo;
      }

      if (fields.confirm) {
        userObj.confirm = fields.confirm;
      }
      if (fields.group) {
        userObj.group = fields.group;
      }
      if (fields.category) {
        userObj.category = fields.category;
      }
      if (fields.comments) {
        userObj.comments = xss(fields.comments);
      }
      if (fields.introduction) {
        userObj.introduction = xss(fields.introduction);
      }
      if (fields.company) {
        userObj.company = fields.company;
      }
      if (fields.province) {
        userObj.province = fields.province;
      }
      if (fields.city) {
        userObj.city = fields.city;
      }
      if (fields.birth) {
        // 生日日期不能大于当前时间
        if (new Date(fields.birth).getTime() > new Date().getTime()) {
          return ctx.helper.renderCustom(ctx, {
            status: 500,
            message: ctx.__('validate_error_params'),
          });
        }
        userObj.birth = fields.birth;
      }
      if (fields.industry) {
        userObj.industry = xss(fields.industry);
      }
      if (fields.profession) {
        userObj.profession = xss(fields.profession);
      }
      if (fields.experience) {
        userObj.experience = xss(fields.experience);
      }
      if (password !== '') {
        // 正则判断
        const regPassword =
          /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#%^&*()_+{}\[\]:;<>,.?~\\/\-]).{8,12}$/;
        if (!regPassword.test(password)) {
          errMsg = ctx.__('密码格式不符合规范');
          ctx.helper.renderFail(ctx, {
            message: errMsg,
          });
          return;
        }
        userObj.password = password;
        userObj.passwordExpiresAt = new Date();
      }
      const targetUserId = sessionUser ? sessionUser._id : '';
      await ctx.service.adminUser.update(ctx, targetUserId, userObj);
      // await tools.updateData(ctx, 'adminUser', {
      //   _id: adminUser._id,
      //   type: '2',
      //   IDNum: fields.IDcard ? fields.IDcard : (curAdminUser.IDcard || ''),
      // });
      // 可能无用
      userObj.id = targetUserId;
      const renderUser = JSON.parse(JSON.stringify(userObj));

      renderUser.token = jwt.sign(
        {
          userId: targetUserId,
        },
        app.config.encrypt_key,
        {
          expiresIn: '30day',
        }
      );

      ctx.cookies.set('api_' + app.config.auth_cookie_name, renderUser.token, {
        path: '/',
        maxAge: app.config.userMaxAge,
        signed: true,
        httpOnly: false,
      });

      // 下面代码没用并报错，我先注释掉了。

      // const url1 = fields.URL1;
      // const url2 = fields.URL2;
      // this.deleteFileReal(url1.filename, url1.localPath);
      // this.deleteFileReal(url2.filename, url2.localPath);

      ctx.helper.clearCache(
        countryCode + phoneNum,
        '_sendMessage_resetPassword_'
      );

      // 给adminUser绑定userId
      // if (needBindUserIdFlag && userObj.phoneNum && userObj.phoneNum !== '') {
      //   ctx.service.user.bindUserToAdminUser(); // 不传参数adminUserId, 默认就是当前用户
      // }
      // 调用同步三表方法 htt++++
      updateData(ctx, 'adminUser', {
        _id: adminUserId,
        type: '2',
        IDNum: fields.IDcard || curAdminUser.IDcard,
      });
      // if (needBindUserIdFlag && userObj.phoneNum && userObj.phoneNum !== '') {
      //   ctx.service.user.bindUserToAdminUser(); // 不传参数adminUserId, 默认就是当前用户
      // }
      // 成功返回数据
      ctx.helper.renderSuccess(ctx, {
        data: {},
        message: ctx.__('sys_layer_option_success'),
      });
    } catch (err) {
      let errMessage = '';
      const keyPattern = err.keyPattern;
      if (
        err.code === 11000 &&
        Object.keys(keyPattern).indexOf('phoneNum') !== -1
      ) {
        errMessage = '手机号码重复';
      }
      console.log({ message: errMessage });
      ctx.helper.renderFail(ctx, { message: errMessage || err.message });
    }
  }
  // 删除静态图片
  async deleteFileReal(fileName, configfilePath) {
    let deleteImgErr = '';
    fs.unlink(path.resolve(configfilePath, fileName), err => {
      if (err) {
        console.log(err);
        deleteImgErr = '系统错误';
        return deleteImgErr;
      }
      console.log('删除成功');
    });
  }

  // 获取用户信息
  async getUserInfoBySession() {
    const { ctx, app } = this;
    try {
      let userId = ctx.session.adminUserInfo
        ? ctx.session.adminUserInfo._id
        : '';
      if (!userId) {
        const getTokenFromCookie = ctx.cookies.get('admin_' + app.config.auth_cookie_name);
        const checkToken = await authToken.checkToken(getTokenFromCookie, app.config.encrypt_key);
        console.log('checkToken', checkToken);
        if (checkToken && checkToken._id) {
          userId = checkToken._id;
        }
      }
      let targetUser = {};
      if (userId !== '') {
        targetUser = await ctx.service.adminUser.item(ctx, {
          query: {
            _id: userId,
          },
          populate: [
            {
              path: 'group',
              select: 'power _id enable name',
            },
            {
              path: 'newAddEnterpriseID',
              select:
                'isactive cname corp regAdd code licensePic districtRegAdd message',
            },
            {
              path: 'userId',
              select: 'employeeId',
            },
          ],
          files: '-password',
        });
        if (targetUser.userId) {
          const employee = await ctx.service.employee.employeeItem(ctx, {
            query: { _id: targetUser.userId.employeeId },
            files: '-_id IDNum phoneNum',
          });
          if (employee) {
            if (!targetUser.IDcard && employee.IDNum) {
              targetUser.IDcard = employee.IDNum;
            }
            if (!targetUser.phoneNum && employee.phoneNum) {
              targetUser.phoneNum = employee.phoneNum;
            }
          }
        }
      }
      targetUser = JSON.parse(JSON.stringify(targetUser));
      ctx.helper.renderSuccess(ctx, {
        data: {
          ...targetUser,
          isTrialUser: ctx.session.adminUserInfo.isTrialUser,
        },
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }
  setTreeData(arr) {
    // 删除所有的children,以防止多次调用
    arr.forEach(function(item) {
      delete item.children;
    });
    const map = {}; // 构建map
    arr.forEach(i => {
      map[i.id] = i; // 构建以id为键 当前数据为值
    });
    const treeData = [];
    arr.forEach(child => {
      const mapItem = map[child.parentid]; // 判断当前数据的parentId是否存在map中
      if (mapItem) {
        // 存在则表示当前数据不是最顶层的数据
        // 注意： 这里的map中的数据是引用了arr的它的指向还是arr,当mapItem改变时arr也会改变，踩坑点
        (mapItem.children || (mapItem.children = [])).push(child); // 这里判断mapItem中是否存在child
      } else {
        // 不存在则是顶层数据
        treeData.push(child);
      }
    });
    return treeData;
  }
  // 获取所有所属子公司
  async getSubCompanies() {
    const { ctx } = this;
    try {
      const userId = ctx.session.adminUserInfo._id;
      const targetUser = await ctx.service.adminUser.item2(ctx, {
        query: {
          adminUserId: userId,
        },
      });
      ctx.helper.renderSuccess(ctx, {
        data: targetUser,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  // 切换公司
  async switchCompanies() {
    const { app, ctx } = this;
    ctx.session.adminUserInfo.EnterpriseID =
      ctx.request.body._id || ctx.session.adminUserInfo.EnterpriseID;
    const EnterpriseID = ctx.session.adminUserInfo.EnterpriseID;
    const _id = ctx.session.adminUserInfo._id;
    const user = await ctx.service.adminUser.item(ctx, {
      query: {
        _id,
      },
      files: this.getAuthUserFields('login'),
    });
    const adminUserToken = tools.convertToEditJson(user);

    const loginPerson = ctx.session.adminUserInfo.userName;
    // await ctx.model.Adminorg.updateOne(
    //   { _id: EnterpriseID },
    //   {
    //     $set: {
    //       'loginRecord.loginTime': new Date(),
    //       'loginRecord.loginPerson': loginPerson,
    //     },
    //   }
    // );
    await ctx.service.db.updateOne(
      'Adminorg',
      { _id: EnterpriseID },
      {
        $set: {
          'loginRecord.loginTime': new Date(),
          'loginRecord.loginPerson': loginPerson,
        },
      }
    );

    adminUserToken.token = jwt.sign(
      {
        EnterpriseID,
        _id: adminUserToken._id,
      },
      app.config.encrypt_key,
      {
        expiresIn: '30day',
      }
    );
    // 存入cookie
    ctx.cookies.set(
      'admin_' + this.app.config.auth_cookie_name,
      adminUserToken.token,
      {
        path: '/',
        maxAge: 1000 * 60 * 60 * 24 * 30,
        signed: true,
        httpOnly: false,
      }
    );
  }

  checkUserFormData(ctx, fields) {
    let errMsg = '';
    // console.log('----')
    if (fields._id && !this.checkCurrentId(fields._id)) {
      errMsg = ctx.__('validate_error_params');
    }
    if (fields.profession && !validator.isNumeric(fields.profession)) {
      errMsg = ctx.__('validate_error_field', [ ctx.__('label_profession') ]);
    }
    if (fields.industry && !validator.isNumeric(fields.industry)) {
      errMsg = ctx.__('validate_error_field', [ ctx.__('label_introduction') ]);
    }
    if (fields.experience && !validator.isNumeric(fields.experience)) {
      errMsg = ctx.__('validate_error_field', [ ctx.__('label_experience') ]);
    }
    if (fields.userName && !validatorUtil.isRegularCharacter(fields.userName)) {
      errMsg = ctx.__('validate_error_field', [ ctx.__('label_user_userName') ]);
    }
    if (fields.userName && !validator.isLength(fields.userName, 2, 30)) {
      errMsg = ctx.__('validate_rangelength', [
        ctx.__('label_user_userName'),
        2,
        12,
      ]);
    }
    if (fields.name && !validatorUtil.isRegularCharacter(fields.name)) {
      errMsg = ctx.__('validate_error_field', [ ctx.__('label_name') ]);
    }
    if (fields.name && !validator.isLength(fields.name, 2, 20)) {
      errMsg = ctx.__('validate_rangelength', [ ctx.__('label_name'), 2, 20 ]);
    }

    if (fields.gender && fields.gender !== '0' && fields.gender !== '1') {
      errMsg = ctx.__('validate_inputCorrect', [ ctx.__('lc_gender') ]);
    }
    if (fields.email && !validatorUtil.checkEmail(fields.email)) {
      errMsg = ctx.__('validate_inputCorrect', [ ctx.__('label_user_email') ]);
    }

    if (
      fields.introduction &&
      !validatorUtil.isRegularCharacter(fields.introduction)
    ) {
      errMsg = ctx.__('validate_error_field', [ ctx.__('label_introduction') ]);
    }
    if (
      fields.introduction &&
      !validator.isLength(fields.introduction, 2, 100)
    ) {
      errMsg = ctx.__('validate_rangelength', [
        ctx.__('label_introduction'),
        2,
        100,
      ]);
    }
    if (fields.comments && !validatorUtil.isRegularCharacter(fields.comments)) {
      errMsg = ctx.__('validate_error_field', [ ctx.__('label_comments') ]);
    }
    if (fields.comments && !validator.isLength(fields.comments, 2, 100)) {
      errMsg = ctx.__('validate_rangelength', [
        ctx.__('label_comments'),
        2,
        100,
      ]);
    }
    if (errMsg) {
      throw new Error(errMsg);
    }
  }

  checkCurrentId(ids) {
    if (!ids) return false;
    let idState = true;
    const idsArr = ids.split(',');
    if (typeof idsArr === 'object' && idsArr.length > 0) {
      for (let i = 0; i < idsArr.length; i++) {
        if (!shortid.isValid(idsArr[i])) {
          idState = false;
          break;
        }
      }
    } else {
      idState = false;
    }
    return idState;
  }

  // htt update 删除无用代码，修改获取数据方式
  async getAssessmentInfo() {
    const { ctx } = this;
    try {
      const data = await ctx.service.preventAssess.getAssessmentInfo(
        ctx.query.year
      );
      const EnterpriseID = ctx.session.adminUserInfo
        ? ctx.session.adminUserInfo.EnterpriseID
        : '';
      const filePath = `${this.config.enterprise_http_path}/${EnterpriseID}`;
      // data.tableFile = filePath + '/' + data.tableFilePath;
      data.tableFile = await ctx.helper.concatenatePath({
        path: filePath + '/' + data.tableFilePath,
      });
      // data.reportFile = filePath + '/' + data.reportFilePath;
      data.reportFile = await ctx.helper.concatenatePath({
        path: filePath + '/' + data.reportFilePath,
      });
      // data.exposeFile = filePath + '/' + data.exposeFilePath;
      data.exposeFile = await ctx.helper.concatenatePath({
        path: filePath + '/' + data.exposeFilePath,
      });
      ctx.helper.renderSuccess(ctx, {
        data,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  // htt update 重写
  async createAssessmentReport() {
    const { ctx } = this;
    try {
      let fields = ctx.request.body || {};
      const lastHasRole = fields.hasRole;
      // console.log(fields.hasRole, typeof fields.hasRole, '后端参数数据');
      const EnterpriseID = ctx.session.adminUserInfo
        ? ctx.session.adminUserInfo.EnterpriseID
        : '';
      if (!fields) {
        ctx.helper.renderFail(ctx);
        return false;
      }
      // 获取基础数据
      const baseData = await ctx.service.preventAssess.getAssessmentInfo(
        fields.year
      );
      if (Number(fields.allEmployeesCount) < baseData.contactHazardCount) {
        fields.allEmployeesCount = baseData.contactHazardCount;
      }
      const formObj = {
        fillerName: fields.fillerName, // 填表人
        fillerPhoneNumber: fields.fillerPhoneNumber, // 联系电话
        problem: fields.problem,
        rectification: fields.rectification,
        situation: fields.situation,
        // 先注释掉
        hasRole: fields.hasRole, // 职业卫生管理机构
        majorManegerCount: fields.majorManegerCount, // 专职
        partTimeManegerCount: fields.partTimeManegerCount, // 兼职
        healthCheckCount: fields.healthCheckCount, // 人数（上岗 在岗  离岗）
        allEmployeesCount: Number(fields.allEmployeesCount), // 劳动者总人数，
        contactHazardCount: fields.contactHazardCount, // 接触职业病危害总人数
        ODzaigangCount: fields.ODzaigangCount, // 职业病累计人数 - 目前在岗
        cumulativeTotalCount: fields.cumulativeTotalCount, // 职业病累计人数 - 历年累计
      };
      // 获取防治自查数据
      const preventAssessData = await ctx.service.preventAssess.find({
        year: fields.year,
      });
      const unConformitys = []; // 不符合
      let infoValue = '';
      const reasonableDeficiencys = []; // 合理缺项
      if (preventAssessData && preventAssessData.formData) {
        preventAssessData.formData.forEach((item, j) => {
          preventAssessData.formData[j].item =
            preventAssessData.formData[j]._id;
          item.items.forEach(item2 => {
            item2.method = item2.method || '';
            item2.content = item2.content || '';
            const jibenfuhe = item2.selects.filter(
              item3 => item3.label === '基本符合'
            );
            const heliquexiang = item2.selects.filter(
              item3 => item3.label === '合理缺项'
            );
            if (!jibenfuhe[0]) item2.jibenfuhe = '-';
            if (!heliquexiang[0]) item2.heliquexiang = '-';
            if (item2.selected === '合理缺项') {
              infoValue = item2.selects.filter(
                item3 => item3.label === '合理缺项'
              );
              if (infoValue[0]) {
                infoValue = infoValue[0].value;
              } else {
                infoValue = '';
              }
              reasonableDeficiencys.push({
                name: item2.name,
                info: infoValue || '',
              });
            }
            if (item2.selected === '不符合') {
              unConformitys.push({
                name: item2.name,
                info: item2.inconformityText || '',
              });
            }
            if (item2.selected === '基本符合') {
              unConformitys.push({
                name: item2.name,
                info: item2.inconformityText || '',
              });
            }
          });
        });
      }
      preventAssessData.reasonableDeficiencys = reasonableDeficiencys;
      preventAssessData.unConformitys = unConformitys;
      preventAssessData.cname = baseData.cname;
      preventAssessData.count =
        (preventAssessData.formData && preventAssessData.formData.length) || 0;
      if (preventAssessData.allMark === undefined) {
        preventAssessData.allMark = '';
      }
      if (!preventAssessData.realityMark === undefined) {
        preventAssessData.realityMark = '';
      }
      Object.keys(fields).forEach(item => {
        if (fields[item]) delete baseData[item];
      });
      _.assign(fields, baseData);
      // 注册地址
      fields.districtRegAdd = fields.districtRegAdd
        ? fields.districtRegAdd.join('') + (fields.regAdd || '')
        : '';
      // 工作场所地址
      if (fields.workAddress) {
        fields.workAddress = fields.workAddress.map(item => {
          if (item.districts) {
            item = item.districts.join('') + (item.address || '');
          }
          return item;
        });
      } else {
        fields.workAddress = '';
      }
      _.assign(formObj, fields);
      // formObj.isReported = 1; // 为什么要把这个注释掉呢????????
      // 危害因素
      let harmFactors = [];
      fields = JSON.parse(JSON.stringify(fields));
      if (fields.HazardFactors && fields.HazardFactors.length) {
        fields.HazardFactors.forEach(item => {
          harmFactors = [];
          item.harmFactors.forEach(item2 => {
            if (item2.label !== '总计') {
              harmFactors.push(item2.label);
            }
          });
          delete item.harmFactors;
          item.harmFactors = harmFactors.join('、');
        });
      }
      const pattern = /<(?:[^"'>]|"[^"]*"|'[^']*')*>/g; // 用来去除标签
      const IMGSrcReg = /src=[\'\"]?([^\'\"]*)[\'\"]?/gi; // 用来匹配src的正则
      const IMGSrc = fields.situation && fields.situation.match(IMGSrcReg); // 获取IMG src的值
      // 图片可能有多张，遍历它们并返回本地路径
      const imgdirName = IMGSrc
        ? IMGSrc.map(item => {
          const replaceStr =
              'src="/static' +
              this.config.upload_http_path +
              '/' +
              EnterpriseID +
              '/';
          return { situationImgName: item.replace(replaceStr, '') };
        })
        : null;
      fields.situation =
        (fields.situation &&
          fields.situation.replace(pattern, '').replace('&nbsp;', '')) ||
        ''; // 使用正则去除标签，只留下文字
      fields.imgdirName = imgdirName;
      // fields.hasRole = fields.hasRole ? '有' : '无';
      // console.log(lastHasRole, 'lastHasRole000000');
      fields.hasRole = lastHasRole ? '有' : '无';
      fields.fillDate = moment(fields.fillDate).format('YYYY-MM-DD');
      fields.fDY = moment(fields.fillDate).format('YYYY');
      fields.fDM = moment(fields.fillDate).format('MM');
      fields.fDD = moment(fields.fillDate).format('DD');
      // 用人单位职业病危害风险评估表数据
      // const checkDate = await ctx.model.CheckAssessment.findOne({
      //   EnterpriseID,
      //   year: new Date(fields.year),
      // });
      // let data = [];
      let harmStatistics = baseData.harmStatistics;
      // let noSeriousHarm = {};
      // let seriousHarm = {};
      if (harmStatistics && harmStatistics[0]) {
        harmStatistics = harmStatistics.filter(item => item.sort === 'all')[0]
          .count;
        // seriousHarm = harmStatistics.filter(
        //   item => item.label === '严重危害因素'
        // );
        // seriousHarm = seriousHarm[0] || { harmFactors: [] };
        // noSeriousHarm = harmStatistics.filter(
        //   item => item.label === '一般危害因素'
        // );
        // noSeriousHarm = noSeriousHarm[0] || { harmFactors: [] };
      } else {
        // seriousHarm = { harmFactors: [] };
        // noSeriousHarm = { harmFactors: [] };
      }
      const riskInfo = await this.getRiskAssessmentInfo2({
        EnterpriseID,
        year: fields.year,
      });
      // console.log(riskInfo, 'riskInfo');
      // if (checkDate) {
      //   if (checkDate.chemistryFactors) {
      //     data = await this.getCheckDataInfo(
      //       noSeriousHarm,
      //       seriousHarm,
      //       checkDate.chemistryFactors.formData,
      //       '',
      //       data,
      //       '',
      //       'chemistryFactors'
      //     );
      //   }
      //   if (checkDate.dustFactors) {
      //     data = await this.getCheckDataInfo(
      //       noSeriousHarm,
      //       seriousHarm,
      //       checkDate.dustFactors.formData,
      //       '',
      //       data,
      //       '',
      //       'dustFactors'
      //     );
      //   }
      //   if (checkDate.noiseFactors) {
      //     data = await this.getCheckDataInfo(
      //       noSeriousHarm,
      //       seriousHarm,
      //       checkDate.noiseFactors.formData,
      //       '噪声',
      //       data,
      //       'checkData',
      //       'noiseFactors'
      //     );
      //   }
      //   if (checkDate.handBorneVibrationFactors) {
      //     data = await this.getCheckDataInfo(
      //       noSeriousHarm,
      //       seriousHarm,
      //       checkDate.handBorneVibrationFactors.formData,
      //       '手传振动',
      //       data,
      //       'ahw',
      //       'handBorneVibrationFactors'
      //     );
      //   }
      //   if (checkDate.heatFactors) {
      //     data = await this.getCheckDataInfo(
      //       noSeriousHarm,
      //       seriousHarm,
      //       checkDate.heatFactors.formData,
      //       '高温',
      //       data,
      //       'averageValue',
      //       'heatFactors'
      //     );
      //   }
      //   if (checkDate.highFrequencyEleFactors) {
      //     data = await this.getCheckDataInfo(
      //       noSeriousHarm,
      //       seriousHarm,
      //       checkDate.highFrequencyEleFactors.formData,
      //       '高频电磁场',
      //       data,
      //       'ahw',
      //       'highFrequencyEleFactors'
      //     );
      //   }
      //   if (checkDate.laserFactors) {
      //     data = await this.getCheckDataInfo(
      //       noSeriousHarm,
      //       seriousHarm,
      //       checkDate.laserFactors.formData,
      //       '激光辐射',
      //       data,
      //       'irradiance',
      //       'laserFactors'
      //     );
      //   }
      //   if (checkDate.microwaveFactors) {
      //     data = await this.getCheckDataInfo(
      //       noSeriousHarm,
      //       seriousHarm,
      //       checkDate.microwaveFactors.formData,
      //       '微波辐射',
      //       data,
      //       'average',
      //       'microwaveFactors'
      //     );
      //   }
      //   if (checkDate.powerFrequencyElectric) {
      //     data = await this.getCheckDataInfo(
      //       noSeriousHarm,
      //       seriousHarm,
      //       checkDate.powerFrequencyElectric.formData,
      //       '工频电场',
      //       data,
      //       'electricIntensity',
      //       'powerFrequencyElectric'
      //     );
      //   }
      //   if (checkDate.SiO2Factors) {
      //     data = await this.getCheckDataInfo(
      //       noSeriousHarm,
      //       seriousHarm,
      //       checkDate.SiO2Factors.formData,
      //       '游离二氧化硅',
      //       data,
      //       '',
      //       'SiO2Factors'
      //     );
      //   }
      //   if (checkDate.ultraHighRadiationFactors) {
      //     data = await this.getCheckDataInfo(
      //       noSeriousHarm,
      //       seriousHarm,
      //       checkDate.ultraHighRadiationFactors.formData,
      //       '超高频辐射',
      //       data,
      //       'electricAverage',
      //       'ultraHighRadiationFactors'
      //     );
      //   }
      //   if (checkDate.ultravioletFactors) {
      //     data = await this.getCheckDataInfo(
      //       noSeriousHarm,
      //       seriousHarm,
      //       checkDate.ultravioletFactors.formData,
      //       '紫外辐射',
      //       data,
      //       'irradiance',
      //       'ultravioletFactors'
      //     );
      //   }
      //   if (checkDate.ionizatioSourceFactors) {
      //     data = await this.getCheckDataInfo(
      //       noSeriousHarm,
      //       seriousHarm,
      //       checkDate.ionizatioSourceFactors.formData,
      //       '电离辐射',
      //       data,
      //       'checkResultValue',
      //       'ionizatioSourceFactors'
      //     );
      //   }
      //   if (checkDate.ionizatioRadialFactors) {
      //     data = await this.getCheckDataInfo(
      //       noSeriousHarm,
      //       seriousHarm,
      //       checkDate.ionizatioRadialFactors.formData,
      //       '电离辐射',
      //       data,
      //       'checkResultValue',
      //       'ionizatioRadialFactors'
      //     );
      //   }
      // }
      const exposeData = {
        noSeriousHarmNoExceedEmployees: fields.noSeriousHarmNoExceedEmployees,
        noSeriousHarmExceedEmployees: fields.noSeriousHarmExceedEmployees,
        seriousHarmNoExceedEmployees: fields.seriousHarmNoExceedEmployees,
        seriousHarmExceedEmployees: fields.seriousHarmExceedEmployees,
      }; // 暴露风险数据
      if (fields.noSeriousHarmNoExceedEmployees > 0) {
        exposeData.noSeriousHarmNoExceedLevel = '低风险';
      } else {
        exposeData.noSeriousHarmNoExceedLevel = '';
      }
      if (fields.noSeriousHarmExceedEmployees >= 50) {
        exposeData.noSeriousHarmExceedLevel = '高风险';
      } else if (fields.noSeriousHarmExceedEmployees >= 10) {
        exposeData.noSeriousHarmExceedLevel = '中风险';
      } else if (fields.noSeriousHarmExceedEmployees > 0) {
        exposeData.noSeriousHarmExceedLevel = '低风险';
      } else {
        exposeData.noSeriousHarmExceedLevel = '';
      }
      if (fields.seriousHarmNoExceedEmployees >= 50) {
        exposeData.seriousHarmNoExceedLevel = '高风险';
      } else if (fields.seriousHarmNoExceedEmployees > 0) {
        exposeData.seriousHarmNoExceedLevel = '中风险';
      } else {
        exposeData.seriousHarmNoExceedLevel = '';
      }

      if (fields.seriousHarmExceedEmployees >= 10) {
        exposeData.seriousHarmExceedLevel = '高风险';
      } else if (fields.seriousHarmExceedEmployees > 0) {
        exposeData.seriousHarmExceedLevel = '中风险';
      } else {
        exposeData.seriousHarmExceedLevel = '';
      }
      const assessExposeLevel = [ '低风险', '中风险', '高风险', '' ];
      const exposeFileBack = await ctx.helper.fillWord(
        ctx,
        '用人单位职业病危害暴露情况调查和风险评估表',
        {
          data: riskInfo.assessExposeData,
          ...exposeData,
          assessExposeLevel: assessExposeLevel[fields.assessExposeLevel],
        }
      );
      formObj.hasRole = lastHasRole;
      formObj.exposeFilePath = exposeFileBack.staticName;
      // 生成报告后清除上一次生成的报告文件
      if (fields.exposeFilePath) {
        await ctx.service.record.deleteFile(
          fields.exposeFilePath,
          `${this.config.enterprise_path}/${EnterpriseID}`
        );
      }
      let isShanxi = false;
      if (
        fields.workAddress &&
        fields.workAddress.join().indexOf('山西') > -1
      ) {
        isShanxi = true;
      } else if (!fields.workAddress && fields.cname.indexOf('山西') === -1) {
        isShanxi = true;
      }
      let reportBack = {};
      console.log(isShanxi);
      const newFields = this.changeStringFields(fields);
      reportBack = await ctx.helper.fillWord(
        ctx,
        '山西用人单位职业病危害综合风险评估报告',
        newFields
      );
      // if (isShanxi) {
      //   reportBack = await ctx.helper.fillWord(
      //     ctx,
      //     '山西用人单位职业病危害综合风险评估报告',
      //     fields
      //   );
      // } else {
      //   reportBack = await ctx.helper.fillWord(
      //     ctx,
      //     '用人单位职业病危害综合风险评估报告',
      //     fields
      //   );
      // }
      formObj.reportFilePath = reportBack.staticName;
      // 生成报告后清除上一次生成的报告文件
      if (fields.reportFilePath) {
        await ctx.service.record.deleteFile(
          fields.reportFilePath,
          `${this.config.enterprise_path}/${EnterpriseID}`
        );
      }
      const tableBack = await ctx.helper.fillWord(
        ctx,
        '用人单位职业卫生管理自查表',
        preventAssessData
      );
      // 生成报告后清除上一次生成的报告文件
      if (fields.tableFilePath) {
        await ctx.service.record.deleteFile(
          fields.tableFilePath,
          `${this.config.enterprise_path}/${EnterpriseID}`
        );
      }

      formObj.tableFilePath = tableBack.staticName;

      let recordBack = {};
      recordBack = await ctx.service.db.findOneAndUpdate(
        'RiskAssessmentReport',
        { _id: fields._id },
        { $set: formObj }
      );
      if (formObj.allEmployeesCount) {
        await ctx.service.db.updateOne(
          'PreventAssess',
          { _id: preventAssessData._id },
          { $set: { headCount: formObj.allEmployeesCount } }
        );
      }
      await ctx.service.preventAssess.updateAssessmentInfo(
        {
          year: fields.year,
        },
        preventAssessData._id || ''
      );
      ctx.helper.renderSuccess(ctx, {
        message: '报告提交成功！',
        data: {
          recordBack,
          reportBack,
          tableBack,
          exposeFileBack,
        },
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }
  changeStringFields(fields) {
    if (!fields.allEmployeesCount) {
      fields.allEmployeesCount = fields.allEmployeesCount + '';
    }
    if (fields.healthCheckCount && !fields.healthCheckCount.zaiGang) {
      fields.healthCheckCount.zaiGang = '0';
    }
    if (fields.healthCheckCount && !fields.healthCheckCount.shangGang) {
      fields.healthCheckCount.shangGang = '0';
    }
    if (
      fields.healthCheckCount &&
      !fields.healthCheckCount.shangGangInspectionRequired
    ) {
      fields.healthCheckCount.shangGangInspectionRequired = '0';
    }
    if (fields.healthCheckCount && !fields.healthCheckCount.liGang) {
      fields.healthCheckCount.liGang = '0';
    }
    if (
      fields.healthCheckCount &&
      !fields.healthCheckCount.zaiGangInspectionRequired
    ) {
      fields.healthCheckCount.zaiGangInspectionRequired = '0';
    }
    if (
      fields.healthCheckCount &&
      !fields.healthCheckCount.liGangInspectionRequired
    ) {
      fields.healthCheckCount.liGangInspectionRequired = '0';
    }
    if (!fields.noSeriousHarmExceedEmployees) {
      fields.noSeriousHarmExceedEmployees = '0';
    }
    if (!fields.noSeriousHarmExceedEmployees) {
      fields.noSeriousHarmExceedEmployees = '0';
    }
    if (!fields.noSeriousHarmNoExceedEmployees) {
      fields.noSeriousHarmNoExceedEmployees = '0';
    }
    if (!fields.seriousHarmExceedEmployees) {
      fields.seriousHarmExceedEmployees = '0';
    }
    if (!fields.seriousHarmNoExceedEmployees) {
      fields.seriousHarmNoExceedEmployees = '0';
    }
    if (!fields.ODzaigangCount) {
      fields.ODzaigangCount = '0';
    }
    if (!fields.cumulativeTotalCoun) {
      fields.cumulativeTotalCoun = '0';
    }
    if (!fields.contactHazardCount) {
      fields.contactHazardCount = '0';
    }
    if (!fields.majorManegerCount) {
      fields.majorManegerCount = '0';
    }
    if (!fields.partTimeManegerCount) {
      fields.partTimeManegerCount = '0';
    }
    if (!fields.cumulativeTotalCount) {
      fields.cumulativeTotalCount = '0';
    }
    return fields;
  }
  // async getTouchHarmEmployeeInfo(checkProject, noSeriousHarm, seriousHarm) {
  //   let type = '',
  //     touchEmployees = [];
  //   type = await this.ctx.model.OccupationalexposureLimits.findOne({ chineseName: checkProject });
  //   if (type) {
  //     if (type.seriousHarm === '1') {
  //       type = '严重';
  //       touchEmployees = seriousHarm.harmFactors.filter(item => item.label === checkProject);
  //     } else {
  //       type = '一般';
  //       touchEmployees = noSeriousHarm.harmFactors.filter(item => item.label === checkProject);
  //     }
  //     return { type, touchEmployees: touchEmployees[0] ? touchEmployees[0].value : 0 };
  //   }
  //   return null;
  // }

  // 获取接害人数详情信息 htt++++

  async getRiskAssessmentTableData(params) {
    const data = { data: [], applyResults: [] };
    const EnterpriseID = this.ctx.session.adminUserInfo
      ? this.ctx.session.adminUserInfo.EnterpriseID
      : '';
    const { ctx } = this;
    // const adminorgRes = await ctx.model.Adminorg.findOne(
    const adminorgRes = await ctx.service.db.findOne(
      'Adminorg',
      { _id: EnterpriseID },
      { level: 1 }
    );
    // console.log('企业行业分类等级', adminorgRes);
    const adminorgLevel = adminorgRes.level;
    const projectNumberArr = [];
    let startYear;
    let endYear;
    if (adminorgLevel === '严重') {
      startYear = new Date(params.year).getFullYear() + '';
      endYear = +startYear + 1 + '';
    } else {
      const starttempyear = new Date(params.year).getFullYear() - 2 + '';
      const endstartyear = new Date(params.year).getFullYear() + 1 + '';
      startYear = new Date(starttempyear).getFullYear() + '';
      endYear = new Date(endstartyear).getFullYear() + '';
    }
    console.log('startYear:', startYear, 'endYear:', endYear);
    // const enterpriseIds = await this.ctx.service.employee.findSubCompany(EnterpriseID, 'branch');
    const enterpriseIds = await ctx.helper.getScopeData('enterprise_ids');
    const enterpriseData = [ EnterpriseID, ...enterpriseIds ];
    // 最开始的需求是不管行业分类只抓取最近一年的数据
    const pipeline = [
      {
        $match: {
          EnterpriseID: { $in: enterpriseData },
          reportTime: { $gte: new Date(startYear), $lte: new Date(endYear) },
        },
      },
      {
        $lookup: {
          from: 'checkAssessment',
          localField: '_id',
          foreignField: 'jobHealthId',
          as: 'checkAssessment',
        },
      },
      { $unwind: '$checkAssessment' },
      {
        $sort: { 'checkAssessment.year': -1 },
      },
    ];
    const checkAssInformation = await ctx.service.db.aggregate(
      'JobHealth',
      pipeline
    );

    for (let y = 0; y < checkAssInformation.length; y++) {
      const item = checkAssInformation[y].projectNumber;
      projectNumberArr.push(item);
    }
    const checkDate = checkAssInformation.map(item => item.checkAssessment);
    if (checkDate.length) {
      const categorys = [
        {
          category: 'chemistryFactors',
          harmFactorName: '',
          checkData: '',
          bigCategory: 'chemistryFactors',
        },
        {
          category: 'dustFactors',
          harmFactorName: '',
          checkData: '',
          bigCategory: 'dustFactors',
        },
        {
          category: 'biologicalFactors',
          harmFactorName: '',
          checkData: '',
          bigCategory: 'biologicalFactors',
        },
        {
          category: 'noiseFactors',
          harmFactorName: '噪声',
          checkData: 'checkData',
          bigCategory: 'physicsFactors',
        },
        {
          category: 'handBorneVibrationFactors',
          harmFactorName: '手传振动',
          checkData: 'fourHoursAccelerated',
          bigCategory: 'physicsFactors',
        },
        {
          category: 'heatFactors',
          harmFactorName: '高温',
          checkData: 'averageValue',
          bigCategory: 'physicsFactors',
        },
        {
          category: 'highFrequencyEleFactors',
          harmFactorName: '高频电磁场',
          checkData: [
            { label: '电场强度', value: 'electricIntensityData' },
            { label: '磁场强度', value: 'magneticIntensityData' },
          ],
          bigCategory: 'physicsFactors',
        },
        {
          category: 'laserFactors',
          harmFactorName: '激光辐射',
          checkData: 'irradiance',
          bigCategory: 'physicsFactors',
        },
        {
          category: 'microwaveFactors',
          harmFactorName: '微波辐射',
          checkData: 'average',
          bigCategory: 'physicsFactors',
        },
        {
          category: 'powerFrequencyElectric',
          harmFactorName: '工频电场',
          checkData: 'electricIntensity',
          bigCategory: 'physicsFactors',
        },
        {
          category: 'ultraHighRadiationFactors',
          harmFactorName: '超高频辐射',
          checkData: 'electricAverage',
          bigCategory: 'physicsFactors',
        },
        {
          category: 'ionizatioSourceFactors',
          harmFactorName: '电离辐射-含源装置',
          checkData: 'checkResultValue',
          bigCategory: 'radiationFactors',
        },
        {
          category: 'ionizatioRadialFactors',
          harmFactorName: '电离辐射-射线装置',
          checkData: 'checkResultValue',
          bigCategory: 'radiationFactors',
        },
      ];
      let item = {};
      for (let i = 0; i < categorys.length; i++) {
        item = categorys[i];
        for (let j = 0; j < checkDate.length; j++) {
          const item2 = checkDate[j];
          if (item2[item.category] && item2[item.category].formData.length) {
            await this.getCheckDataInfo(
              EnterpriseID,
              item2[item.category].formData,
              item.harmFactorName,
              data,
              item.checkData,
              item.bigCategory
            );
          }
        }
      }
    }
    data.projectNumberArr = projectNumberArr;
    // console.log(data, 'tttttttttttttt');
    return data;
  }

  async getCheckDataInfo(
    EnterpriseID,
    formData,
    harmFactorName,
    data,
    checkData,
    harmFactorType
  ) {
    const checkResultTxts = [ '符合', '合格' ];
    const { ctx } = this;
    const isApplyTable = ctx.query.isApplyTable;
    let touchEmployees = '';
    let workspace = '';
    let station = '';
    let checkProject = '';
    let item2 = {};
    let item = {};
    let actualHarmFactorName = ''; // 去除总呼的危害因素名称
    let resultItem = {};
    // const applyResults = [];
    let CTWA = '',
      CSTE = '',
      CME = '',
      CPE = '';
    for (let i = 0; i < formData.length; i++) {
      item = formData[i];
      if (JSON.stringify(item) !== '{}') {
        resultItem = {
          车间名称: workspace,
          '工种/岗位名称': station,
          定员: 0,
          '检测点/检测对象名称': workspace + station,
          职业病危害因素名称: '',
          CTWA: '',
          CSTE: '',
          CME: '',
          CPE: '',
          噪声等效声级: '',
          '其他因素浓度/强度': '',
          '折减因子（RF)': '',
          暴露水平: item.checkResult
            ? checkResultTxts.indexOf(item.checkResult) !== -1
              ? '不超标'
              : '超标'
            : '',
        };
        let TWAFixed = 0;
        const pcTWA = item.TWALimit || item.touchLimitTWA || '';
        if (pcTWA) {
          TWAFixed = ((pcTWA + '').replace(/[^0-9.]/gi, '') &&
            (pcTWA + '').replace(/[^0-9.]/gi, '').split('.'))[1]
            ? (pcTWA + '').replace(/[^0-9.]/gi, '').split('.')[1].length
            : 1;
        }
        let PE = '';
        if (
          harmFactorType === 'chemistryFactors' ||
          harmFactorType === 'biologicalFactors'
        ) {
          workspace = item.workspace || '';
          station = item.station || '';
          checkProject = item.checkProject;
          actualHarmFactorName = checkProject;
          if (harmFactorType === 'biologicalFactors') {
            CTWA = item.TWA || '';
            CME = item.MAC || '';
            CSTE = item.STEL || '';
            CPE = item.PE || '';
          } else {
            PE = item.checkResults[0] ? item.checkResults[0].PE || '' : '';
            CTWA = item.checkResults[0] ? item.checkResults[0].TWA || '' : '';
            CME = item.checkResults[0] ? item.checkResults[0].MAC || '' : '';
            CSTE = item.checkResults[0] ? item.checkResults[0].STEL || '' : '';
            CPE = item.checkResults[0] ? item.checkResults[0].PE || '' : '';
            resultItem['折减因子（RF)'] = item.checkResults[0]
              ? item.checkResults[0].RF || ''
              : '';
          }
          item2 = {
            workspace: item.workspace || '',
            station: item.station || '',
            checkProject: item.checkProject,
            touchEmployees,
            TWA: CTWA,
            MAC: CME,
            STEL: CSTE,
            checkData: '',
            equalLevel: '',
            checkResult: item.checkResult,
          };
          resultItem.CTWA = ctx.service.commonService.getNumber2(CTWA);
          resultItem.CSTE = ctx.service.commonService.getNumber2(CSTE);
          resultItem.CME = ctx.service.commonService.getNumber2(CME);
          resultItem.CPE = ctx.service.commonService.getNumber2(CPE);
        } else if (harmFactorType === 'dustFactors') {
          // console.log('粉尘????????');
          CTWA = item.checkResults[0] ? item.checkResults[0].TWA || '' : '';
          PE = item.checkResults[0] ? item.checkResults[0].PE || '' : '';
          CPE = item.checkResults[0] ? item.checkResults[0].PE || '' : '';
          workspace = item.workspace || '';
          station = item.station || '';
          checkProject = item.checkProject;
          actualHarmFactorName = checkProject.replace(
            /\(总尘\)|\(呼尘\)|（总尘）|（呼尘）/,
            ''
          );
          item2 = {
            workspace: item.workspace || '',
            station: item.station || '',
            checkProject,
            touchEmployees,
            TWA: CTWA,
            MAC: '',
            STEL: '',
            checkData: '',
            equalLevel: '',
            checkResult: item.checkResult || '',
          };
          resultItem.CTWA = ctx.service.commonService.getNumber2(CTWA);
          resultItem.CPE = ctx.service.commonService.getNumber2(CPE);
          resultItem['折减因子（RF)'] = item.checkResults[0]
            ? item.checkResults[0].RF || ''
            : '';
        } else if (
          harmFactorType === 'ionizatioSourceFactors' ||
          harmFactorType === 'ionizatioRadialFactors'
        ) {
          checkProject = item[harmFactorName];
          resultItem['其他因素浓度/强度'] =
            item.checkResults[0].checkResultValue;
          item2 = {
            workspace: item.workspace || '',
            station: item.station || '',
            checkProject: item[harmFactorName],
            touchEmployees,
            TWA: '',
            MAC: '',
            STEL: '',
            checkData: resultItem['其他因素浓度/强度'],
            equalLevel: '',
            checkResult: item.checkResult,
          };
          actualHarmFactorName = checkProject;
        } else {
          checkProject = harmFactorName;
          actualHarmFactorName = checkProject;
          station = item.station || item.workType || '';
          workspace = item.workspace || '';
          item2 = {
            workspace,
            station,
            checkProject,
            touchEmployees,
            MAC: '',
            STEL: '',
            TWA: '',
            divisionData: '',
            checkResult: item.checkResult || '',
            equalLevel: checkProject === '噪声' ? item.equalLevel : '',
          };
          if (checkProject === '噪声') {
            item2.equalLevel = item.equalLevel;
            resultItem.噪声等效声级 = ctx.service.commonService.getNumber2(
              item.equalLevel
            );
          } else {
            item2.equalLevel = '';
            if (typeof checkData === 'object') {
              const checkDatas = [];
              for (let j = 0; j < checkData.length; j++) {
                checkDatas.push(
                  checkData[j].label +
                    '：' +
                    Number(
                      ctx.service.commonService.getNumber2(
                        item[checkData[j].value]
                      )
                    )
                );
              }
              resultItem['其他因素浓度/强度'] = checkDatas.join('/');
            } else {
              resultItem['其他因素浓度/强度'] =
                ctx.service.commonService.getNumber2(item[checkData] || '');
            }
          }
          item2.checkData = resultItem['其他因素浓度/强度'];
        }
        if (isApplyTable && actualHarmFactorName.indexOf('其他粉尘') !== -1) {
          actualHarmFactorName = '其他粉尘';
        }
        if (!isApplyTable) {
          if (
            pcTWA &&
            Number(pcTWA.replace(/[^0-9.]/gi, '')) &&
            Number(PE.replace(/[^0-9.]/gi, ''))
          ) {
            item2.divisionData = (
              Number(PE.replace(/[^0-9.]/gi, '')) /
              Number(pcTWA.replace(/[^0-9.]/gi, ''))
            ).toFixed(TWAFixed);
            item2.realDivisionData = item2.divisionData;
            if (Number(item2.divisionData) < 0.1) {
              item2.divisionData = '<0.1'; // 最终结果小于0.1 那么结果直接为<0.1
            }
          }
        }
        if (checkProject) {
          const station2 = station.replace(/岗位$|岗$/, '');
          const workspace2 = workspace.replace(/车间$/, '');
          const harmFactorInfo =
            // await ctx.model.OccupationalexposureLimits.findOne(
            await ctx.service.db.findOne(
              'OccupationalexposureLimits',
              { chineseName: actualHarmFactorName },
              { showName: 1, seriousHarm: 1, standardName2_1FullName: 1 }
            );
          const pipeline = [
            { $match: { EnterpriseID } },
            { $unwind: '$children' },
            {
              $addFields: {
                stations: {
                  $cond: [
                    { $eq: [ '$category', 'mill' ] },
                    '$children.children',
                    [ '$children' ],
                  ],
                },
              },
            },
            { $unwind: '$stations' },
            {
              $project: {
                customizeHarm: {
                  $regexFindAll: {
                    input: '$stations.customizeHarm',
                    regex: /[^,|，|、|\/]+/,
                  },
                },
                harmFactors: '$stations.harmFactors',
                stationName: '$stations.name',
                workspaceName: {
                  $cond: [
                    { $eq: [ '$category', 'mill' ] },
                    '$children.name',
                    '$name',
                  ],
                },
                peopleNumber: '$stations.customizePeopleNumber',
              },
            },
            {
              $addFields: {
                customizeHarm: {
                  $map: {
                    input: '$customizeHarm',
                    as: 'item',
                    in: '$$item.match',
                  },
                },
              },
            },
            {
              $match: {
                $and: [
                  {
                    $or: [
                      { stationName: station2 },
                      { stationName: station2 + '岗位' },
                      { stationName: station2 + '岗' },
                      { stationName: station2.replace(/\s+/g, '') },
                      { stationName: (station2 + '岗位').replace(/\s+/g, '') },
                      { stationName: (station2 + '岗').replace(/\s+/g, '') },
                      { stationName: station2.replace(/(\d+)$/g, '') },
                      {
                        stationName: (station2 + '岗位').replace(/(\d+)$/g, ''),
                      },
                      { stationName: (station2 + '岗').replace(/(\d+)$/g, '') },
                      {
                        stationName: station2
                          .replace(/(\d+)$/g, '')
                          .replace(/\s+/g, ''),
                      },
                      {
                        stationName: (station2 + '岗位')
                          .replace(/(\d+)$/g, '')
                          .replace(/\s+/g, ''),
                      },
                      {
                        stationName: (station2 + '岗')
                          .replace(/(\d+)$/g, '')
                          .replace(/\s+/g, ''),
                      },
                    ],
                  },
                  {
                    $or: [
                      { workspaceName: workspace2 },
                      { workspaceName: workspace2 + '车间' },
                      {
                        workspaceName: workspace2.replace(/\s+/g, '') + '车间',
                      },
                      { workspaceName: workspace2.replace(/\s+/g, '') },
                      {
                        workspaceName:
                          workspace2
                            .replace(/(\d+)$/g, '')
                            .replace(/\s+/g, '') + '车间',
                      },
                      {
                        workspaceName: workspace2
                          .replace(/(\d+)$/g, '')
                          .replace(/\s+/g, ''),
                      },
                      { workspaceName: workspace2.replace(/(\d+)$/g, '') },
                      {
                        workspaceName:
                          workspace2.replace(/(\d+)$/g, '') + '车间',
                      },
                    ],
                  },
                ],
              },
            },
          ];
          const stationInfo = await ctx.service.db.aggregate(
            'MillConstruction',
            pipeline
          );
          touchEmployees = stationInfo[0] ? stationInfo[0].peopleNumber : 0;
          item2.isSerious =
            harmFactorInfo && harmFactorInfo.seriousHarm === '1'
              ? '严重'
              : '一般';
          item2.touchEmployees = touchEmployees || 0;
          item2.category = harmFactorType;
          if (
            isApplyTable &&
            harmFactorInfo &&
            harmFactorInfo.standardName2_1FullName
          ) {
            station = station.replace(/(\d+)$/g, '').replace(/\s+/g, '');
            resultItem.车间名称 = workspace;
            resultItem['工种/岗位名称'] = station;
            resultItem['检测点/检测对象名称'] = workspace + station;
            resultItem.职业病危害因素名称 =
              harmFactorInfo.standardName2_1FullName;
            resultItem.定员 = item2.touchEmployees;
            data.applyResults.push(resultItem);
          } else {
            data.data.push(item2);
          }
        }
      }
    }
    // return { data, applyResults };
  }

  async getRiskAssessmentInfo2(params) {
    const EnterpriseID = this.ctx.session.adminUserInfo
      ? this.ctx.session.adminUserInfo.EnterpriseID
      : '';
    let projectNumber = [];
    const data = await this.getRiskAssessmentTableData(params); // 危害因素数据 jobhealth表关联checkassentment表的危害因素结果
    projectNumber = data.projectNumberArr;
    // 用人单位职业病危害风险评估表数据
    const exposeData = {
      noSeriousHarmNoExceedEmployees: 0, // 未发生严重职业病危害且未超过接害人数
      noSeriousHarmExceedEmployees: 0, // 未发生严重职业病危害但超过接害人数
      seriousHarmNoExceedEmployees: 0, // 发生严重职业病危害但未超过接害人数
      seriousHarmExceedEmployees: 0, // 发生严重职业病危害且超过接害人数
    }; // 暴露风险数据

    let harmFactorTouchInfo = {
      dustFactors: {
        category: '粉尘类',
        alisa: 'dustFactors',
        harmFactors: new Set(),
        touchEmployeesCount: 0,
      },
      chemistryFactors: {
        category: '化学物质类',
        alisa: 'chemistryFactors',
        harmFactors: new Set(),
        touchEmployeesCount: 0,
      },
      physicsFactors: {
        category: '物理因素类',
        alisa: 'physicsFactors',
        harmFactors: new Set(),
        touchEmployeesCount: 0,
      },
      biologicalFactors: {
        category: '生物因素类',
        alisa: 'biologicalFactors',
        harmFactors: new Set(),
        touchEmployeesCount: 0,
      },
      radiationFactors: {
        category: '放射性物质类',
        alisa: 'radiationFactors',
        harmFactors: new Set(),
        touchEmployeesCount: 0,
      },
    };
    const checkResultTxts = [ '符合', '合格' ];
    // 点位+是否严重+判定结果
    const checkPointResultInfo = {};
    let checkPointResult = '';
    // 点位+危害因素类别
    let harmFactorTouchPoint = '';
    const harmFactorTouchPointInfo = {};
    // 点位
    const checkPointInfo = {};
    let checkPoint = '';
    let contactHazardCount = 0; // 接害总人数
    data.data.forEach(item => {
      item.workspace = item.workspace.replace(/(\d+)$/g, '');
      item.station = item.station.replace(/(\d+)$/g, '');
      harmFactorTouchPoint = item.workspace + item.station + item.category;
      checkPointResult =
        item.workspace + item.station + item.isSerious + item.checkResult;
      checkPoint = item.workspace + item.station;
      // console.log('工作场所',checkPointInfo[checkPoint])
      if (!checkPointInfo[checkPoint]) {
        contactHazardCount += Number(item.touchEmployees) || 0;
        checkPointInfo[checkPoint] = true;
      }
      if (item.isSerious === '严重') {
        if (checkResultTxts.indexOf(item.checkResult) !== -1) {
          if (!checkPointResultInfo[checkPointResult]) {
            exposeData.seriousHarmNoExceedEmployees +=
              Number(item.touchEmployees) || 0;
            checkPointResultInfo[checkPointResult] = true;
          }
        } else {
          if (!checkPointResultInfo[checkPointResult]) {
            exposeData.seriousHarmExceedEmployees +=
              Number(item.touchEmployees) || 0;
            checkPointResultInfo[checkPointResult] = true;
          }
        }
      } else {
        if (checkResultTxts.indexOf(item.checkResult) !== -1) {
          if (!checkPointResultInfo[checkPointResult]) {
            exposeData.noSeriousHarmNoExceedEmployees +=
              Number(item.touchEmployees) || 0;
            checkPointResultInfo[checkPointResult] = true;
          }
        } else {
          if (!checkPointResultInfo[checkPointResult]) {
            exposeData.noSeriousHarmExceedEmployees +=
              Number(item.touchEmployees) || 0;
            checkPointResultInfo[checkPointResult] = true;
          }
        }
      }
      // 计算危害因素分类接害人数
      harmFactorTouchInfo[item.category].harmFactors.add(item.checkProject);
      if (!harmFactorTouchPointInfo[harmFactorTouchPoint]) {
        harmFactorTouchInfo[item.category].touchEmployeesCount +=
          item.touchEmployees || 0;
        harmFactorTouchPointInfo[harmFactorTouchPoint] = true;
      }
    });
    if (exposeData.noSeriousHarmNoExceedEmployees > 0) {
      exposeData.noSeriousHarmNoExceedLevel = '低风险';
    } else {
      exposeData.noSeriousHarmNoExceedLevel = '';
    }
    if (exposeData.noSeriousHarmExceedEmployees >= 50) {
      exposeData.noSeriousHarmExceedLevel = '高风险';
    } else if (exposeData.noSeriousHarmExceedEmployees > 0) {
      exposeData.noSeriousHarmExceedLevel = '中风险';
    } else {
      exposeData.noSeriousHarmExceedLevel = '';
    }
    if (exposeData.seriousHarmNoExceedEmployees >= 50) {
      exposeData.seriousHarmNoExceedLevel = '高风险';
    } else if (exposeData.seriousHarmNoExceedEmployees > 0) {
      exposeData.seriousHarmNoExceedLevel = '中风险';
    } else {
      exposeData.seriousHarmNoExceedLevel = '';
    }
    if (exposeData.seriousHarmExceedEmployees > 0) {
      exposeData.seriousHarmExceedLevel = '高风险';
    } else {
      exposeData.seriousHarmExceedLevel = '';
    }
    let assessExposeLevel = 3; // 暴露风险等级
    if (
      exposeData.seriousHarmExceedEmployees > 0 ||
      exposeData.seriousHarmNoExceedEmployees >= 50 ||
      exposeData.noSeriousHarmExceedEmployees >= 50
    ) {
      assessExposeLevel = 2;
    } else if (
      (exposeData.seriousHarmNoExceedEmployees > 0 &&
        exposeData.seriousHarmNoExceedEmployees < 50) ||
      (exposeData.noSeriousHarmExceedEmployees > 0 &&
        exposeData.noSeriousHarmExceedEmployees < 50)
    ) {
      assessExposeLevel = 1;
    } else {
      assessExposeLevel = 0;
    }
    // const preventAssess = await this.ctx.model.PreventAssess.findOne({
    const preventAssess = await this.ctx.service.db.findOne('PreventAssess', {
      EnterpriseID,
      year: new Date(params.year).toISOString(),
    });
    let selfAssessment = 3;
    let assessmentResult = 3; // 综合风险等级
    if (preventAssess) {
      selfAssessment = preventAssess.level; // 健康管理等级
      // console.log('preventAssess.level:', preventAssess.level);
      switch (selfAssessment) {
        case 'A':
          if (assessExposeLevel === 0) {
            // 低风险
            assessmentResult = 2;
          }
          if (assessExposeLevel === 1) {
            // 中风险
            assessmentResult = 2;
          }
          if (assessExposeLevel === 2) {
            // 高风险
            assessmentResult = 1;
          }
          break;
        case 'B':
          if (assessExposeLevel === 0) {
            assessmentResult = 2;
          }
          if (assessExposeLevel === 1) {
            assessmentResult = 1;
          }
          if (assessExposeLevel === 2) {
            assessmentResult = 0;
          }
          break;
        case 'C':
          if (assessExposeLevel === 0) {
            assessmentResult = 1;
          }
          if (assessExposeLevel === 1) {
            assessmentResult = 0;
          }
          if (assessExposeLevel === 2) {
            assessmentResult = 0;
          }
          break;
        default:
          selfAssessment = 0;
          assessmentResult = 0;
          break;
      }
      // 更新了风险评估表中的等级
      // await this.ctx.model.RiskAssessmentReport.updateOne(
      await this.ctx.service.db.updateOne(
        'RiskAssessmentReport',
        { preventAssessId: preventAssess._id },
        {
          $set: {
            assessmentResult,
            assessExposeLevel,
            assessManageLevel: selfAssessment,
            noSeriousHarmNoExceedEmployees:
              exposeData.noSeriousHarmNoExceedEmployees,
            noSeriousHarmExceedEmployees:
              exposeData.noSeriousHarmExceedEmployees,
            seriousHarmNoExceedEmployees:
              exposeData.seriousHarmNoExceedEmployees,
            seriousHarmExceedEmployees: exposeData.seriousHarmExceedEmployees,
          },
        }
      );
    }

    // const adminorgRes = await this.ctx.model.Adminorg.findOne(
    const adminorgRes = await this.ctx.service.db.findOne(
      'Adminorg',
      { _id: EnterpriseID },
      { level: 1 }
    );
    const adminorgLevel = adminorgRes.level;
    harmFactorTouchInfo = Object.values(harmFactorTouchInfo);
    harmFactorTouchInfo = harmFactorTouchInfo.map(item => {
      item.harmFactors = Array.from(item.harmFactors);
      return item;
    });
    return {
      contactHazardCount,
      harmFactorTouchInfo,
      assessExposeData: data.data,
      ...exposeData,
      assessExposeLevel,
      assessmentResult,
      adminorgLevel,
      projectNumber,
    };
  }
  // async getCheckDataInfo(
  //   noSeriousHarm,
  //   seriousHarm,
  //   formData,
  //   harmFactorName,
  //   data,
  //   checkData,
  //   harmFactorType
  // ) {
  //   const { ctx } = this;
  //   const EnterpriseID = ctx.session.adminUserInfo
  //     ? ctx.session.adminUserInfo.EnterpriseID
  //     : '';
  //   let item = {};
  //   let touchEmployees = '';
  //   let workspace = '';
  //   let station = '';
  //   let checkProject = '';
  //   let item2 = {};
  //   for (let i = 0; i < formData.length; i++) {
  //     item = formData[i];
  //     if (harmFactorType === 'chemistryFactors') {
  //       workspace = item.workspace || '';
  //       station = item.station || '';
  //       checkProject = item.checkProject;
  //       item2 = {
  //         workspace: item.workspace || '',
  //         station: item.station || '',
  //         checkProject: item.checkProject,
  //         touchEmployees,
  //         TWA: item.checkResults[0] ? item.checkResults[0].TWA || '' : '',
  //         MAC: item.checkResults[0] ? item.checkResults[0].MAC || '' : '',
  //         STEL: item.checkResults[0] ? item.checkResults[0].STEL || '' : '',
  //         checkData: '',
  //         equalLevel: '',
  //         checkResult: item.checkResult,
  //         divisionData:
  //           item.touchLimitTWA &&
  //           Number(item.touchLimitTWA.replace(/[^0-9.]/gi, ''))
  //             ? Number(item.touchLimitPE.replace(/[^0-9.]/gi, ''))
  //             : '',
  //       }; // PE/PC-TWA的分母Number(item.touchLimitTWA.replace(/[^0-9.]/gi, ''));
  //     } else if (harmFactorType === 'dustFactors') {
  //       workspace = item.workspace || '';
  //       station = item.station || '';
  //       checkProject = item.checkProject;
  //       item2 = {
  //         workspace: item.workspace || '',
  //         station: item.station || '',
  //         checkProject: item.checkProject,
  //         touchEmployees,
  //         TWA: item.checkResults[0] ? item.checkResults[0].TWA || '' : '',
  //         MAC: '',
  //         STEL: '',
  //         checkData: '',
  //         equalLevel: '',
  //         checkResult: item.checkResult || '',
  //         divisionData:
  //           item.TWALimit && item.TWALimit.replace(/[^0-9.]/gi, '')
  //             ? Number(item.PELimit.replace(/[^0-9.]/gi, '')) /
  //               Number(item.TWALimit.replace(/[^0-9.]/gi, ''))
  //             : '',
  //       };
  //     } else {
  //       checkProject = harmFactorName;
  //       let splitArr = [];
  //       if (item.checkAddress) {
  //         splitArr = item.checkAddress.split('车间');
  //         workspace = splitArr[0];
  //         station = splitArr[1] || item.workType || '';
  //       } else {
  //         station = item.station || item.workType || '';
  //         workspace = item.workspace || '';
  //       }
  //       item2 = {
  //         workspace,
  //         station,
  //         checkProject: harmFactorName,
  //         touchEmployees,
  //         MAC: '',
  //         STEL: '',
  //         TWA: '',
  //         divisionData: '',
  //         checkData: item[checkData] || '',
  //         checkResult: item.checkResult || '',
  //         equalLevel: harmFactorType === 'noiseFactors' ? item.equalLevel : '',
  //       };
  //     }
  //     if (checkProject) {
  //       const stationInfo = await ctx.model.MillConstruction.aggregate([
  //         { $match: { EnterpriseID } },
  //         { $unwind: '$children' },
  //         {
  //           $addFields: {
  //             stations: {
  //               $cond: [
  //                 { $eq: ['$category', 'mill'] },
  //                 '$children.children',
  //                 ['$children'],
  //               ],
  //             },
  //           },
  //         },
  //         { $unwind: '$stations' },
  //         {
  //           $project: {
  //             harmFactors: '$stations.harmFactors',
  //             stationName: '$stations.name',
  //             workspaceName: {
  //               $cond: [
  //                 { $eq: ['$category', 'mill'] },
  //                 '$children.name',
  //                 '$name',
  //               ],
  //             },
  //             peopleNumber: { $size: '$stations.children' },
  //           },
  //         },
  //         {
  //           $match: {
  //             stationName: station,
  //             workspaceName: workspace,
  //             harmFactors: {
  //               $elemMatch: { $elemMatch: { $eq: checkProject } },
  //             },
  //           },
  //         },
  //       ]);
  //       touchEmployees = stationInfo[0] ? stationInfo[0].peopleNumber : 0;
  //       const harmFactorInfo =
  //         await ctx.model.OccupationalexposureLimits.findOne({
  //           showName: checkProject,
  //         });
  //       if (harmFactorInfo) {
  //         item2.type = harmFactorInfo.seriousHarm === '1' ? '严重' : '一般';
  //       }
  //       item2.touchEmployees = touchEmployees;
  //     }
  //     data.push(item2);
  //   }
  //   return data;
  // }

  async updateAssessmentReport() {
    const { ctx } = this;
    ctx.helper.renderSuccess(ctx, {
      data: { msg: '企业职卫评估表已更新！' },
    });
  }

  async deleteAssessmentReport() {
    const { ctx } = this;
    try {
      const params = ctx.request.body;
      const assessIds = _.map(params, '_id');
      // let reportIds = _.map(params, 'report._id');
      // reportIds = _.pull(reportIds, undefined);
      const tableFilePath = _.map(params, 'report.tableFilePath');
      const reportFilePath = _.map(params, 'report.reportFilePath');
      let filePaths = [].concat(tableFilePath, reportFilePath);
      filePaths = _.filter(filePaths, null);
      const EnterpriseID = ctx.session.adminUserInfo
        ? ctx.session.adminUserInfo.EnterpriseID
        : '';
      // 删文件
      for (let i = 0; i < filePaths.length; i++) {
        await ctx.service.record.deleteFile(
          filePaths[i],
          `${this.config.enterprise_path}/${EnterpriseID}`
        );
      }
      // 删自查表记录
      const assessDelRes = await ctx.service.db.deleteMany('PreventAssess', {
        _id: { $in: assessIds },
      });
      // 删报告记录
      const riskAssessDelRes = await ctx.service.db.deleteMany(
        'RiskAssessmentReport',
        { preventAssessId: { $in: assessIds } }
      );
      console.log('已删除', assessDelRes, riskAssessDelRes);
      ctx.helper.renderSuccess(ctx, {
        data: { msg: '企业职卫评分表数据已删除！' },
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  async dingSubscript() {
    try {
      const { ctx } = this;

      const query = ctx.query;
      const body = ctx.request.body;

      // const adminorg = await ctx.model.Adminorg.findOne({ _id: query._id });
      const adminorg = await ctx.service.db.findOne('Adminorg', {
        _id: query._id,
      });
      if (adminorg === null) {
        return;
      }
      const { token, aes_key, AppKey, AppSecret } = adminorg.dingInfo;

      // 参考：钉钉开发文档-业务事件回调
      const DingTalkEncryptor = require('dingtalk-encrypt');
      const encryptor = new DingTalkEncryptor(token, aes_key, AppKey);
      // 解密钉钉回调数据
      const plainText = await encryptor.getDecryptMsg(
        query.msg_signature,
        query.timestamp,
        query.nonce,
        body.encrypt
      );
      const res = await encryptor.getEncryptedMap(
        'success',
        query.timestamp,
        query.nonce
      );

      // ctx.body = JSON.stringify(res);

      const obj = JSON.parse(plainText);

      const result = await ctx.curl(
        `https://oapi.dingtalk.com/gettoken?appkey=${AppKey}&appsecret=${AppSecret}`,
        {
          method: 'GET',
          rejectUnauthorized: true,
          dataType: 'json',
        }
      );
      const access_token = result.data.access_token;
      // console.log(access_token, 'token=======');
      const UserIds = obj.UserId ? await this.sliceArr(obj.UserId, 50) : '';
      const DeptId = obj.DeptId ? obj.DeptId : '';
      // 回调事件类型，根据事件类型和业务数据处理相应业务
      const eventType = obj.EventType;
      // console.log(eventType,'来的是什么事件')
      // 4. 根据EventType分类处理
      if (eventType === 'check_url') {
        console.log('验证成功====');
      } else if (eventType === 'user_add_org') {
        await this.user_add_org(access_token, UserIds, query._id);
        console.log('通讯录用户增加');
      } else if (eventType === 'user_modify_org') {
        await this.user_add_org(
          access_token,
          UserIds,
          query._id,
          'user_modify_org'
        );
        console.log('通讯录用户修改');
      } else if (eventType === 'user_leave_org') {
        await this.user_leave_org(access_token, UserIds);
        console.log('通讯录用户离职');
      } else if (eventType === 'org_dept_modify') {
        await this.org_dept_modify(access_token, DeptId);
        console.log('通讯录企业部门修改');
      } else if (eventType === 'org_dept_remove') {
        await this.org_dept_remove(DeptId);
        console.log('通讯录企业部门删除');
      }
      ctx.body = JSON.stringify(res);
    } catch (error) {
      console.log(error);
    }
  }

  async org_dept_modify(access_token, dingId) {
    const dept = await this.ctx.curl(
      `https://oapi.dingtalk.com/department/get?access_token=${access_token}&id=${dingId}`,
      {
        method: 'GET',
        rejectUnauthorized: true,
        dataType: 'json',
      }
    );
    console.log(dept.data, '===');
    const name = dept.data.name;
    console.log(dingId);
    console.log(name);
    // const res = await this.ctx.model.Dingtree.updateOne({ dingId }, { $set: { name } });
    const res = await this.ctx.service.db.updateOne(
      'Dingtree',
      { dingId },
      { $set: { name } }
    );
    console.log(res);
  }
  async org_dept_remove(dingId) {
    // const res = await this.ctx.model.Dingtree.deleteMany({ dingId });
    const res = await this.ctx.service.db.deleteMany('Dingtree', { dingId });

    console.log(res);
  }
  /**
   * 人员表离岗存在人员转正 不存在新增人员
   * @param {*string} access_token 字符串
   * @param {*array} UserIds 数组
   * @param {*} CorpId id
   * @param {*} type 类型
   */
  async user_add_org(access_token, UserIds, CorpId, type) {
    const dept = await this.ctx.curl(
      `https://oapi.dingtalk.com/department/list?access_token=${access_token}`,
      {
        method: 'GET',
        rejectUnauthorized: true,
        dataType: 'json',
      }
    );
    const user = await this.check_user_detail(access_token, UserIds);
    const arr = user.data.result;
    // console.log(arr);
    const final_result = [];
    for (let index = 0; index < arr.length; index++) {
      const obj = {};
      const field_list = arr[index].field_list;
      for (let index = 0; index < field_list.length; index++) {
        const item = field_list[index];
        if (item.field_code === 'sys00-name') {
          obj.name = item.label;
        }
        if (item.field_code === 'sys02-certNo') {
          obj.IDNum = item.label;
        }
        if (item.field_code === 'sys00-mobile') {
          obj.phoneNum =
            item.label.indexOf('-') !== -1 ? item.label.split('-')[1] : '';
        }
        if (item.field_code === 'sys03-highestEdu') {
          obj.education = item.label;
        }
        if (item.field_code === 'sys00-confirmJoinTime') {
          obj.workStart = item.label;
        }
        if (item.field_code === 'sys02-marriage') {
          obj.marriage = item.label;
        }
        if (item.field_code === 'sys00-email') {
          obj.email = item.label;
        }
        if (item.field_code === 'sys00-deptIds') {
          const data = await this.ctx.service.employee.concatdepart(
            item.label,
            dept.data.department
          );
          obj.departs = data.departs;
          const arr = data.departIds.split(',');
          for (let i = 0; i < arr.length; i++) {
            if (!arr[i]) {
              arr.splice(i, 1);
            } else {
              arr[i] = arr[i].split(/-|_|\//gi);
            }
          }
          obj.departIds = arr;
        }
      }
      obj.dingId = arr[index].userid;
      final_result.push(obj);
    }
    console.log(final_result, '最终结果', type);

    const dingId = user.data.result[0].userid;
    // const res = await this.ctx.model.Employee.findOne({ dingId });
    const res = await this.ctx.service.db.findOne('Employee', { dingId });
    if (res === null || type === 'user_modify_org') {
      console.log('新增人员 || 修改');
      await this.ctx.service.employee.clearup(final_result, CorpId);
    } else {
      console.log('转在岗');
      // await this.ctx.model.Employee.update({ dingId }, { $set: { status: 1 } });
      await this.ctx.service.db.updateMany(
        'Employee',
        { dingId },
        { $set: { status: 1 } }
      );
    }
  }
  async user_leave_org(access_token, UserIds) {
    const res = await this.check_user_detail(access_token, UserIds);
    const dingId = res.data.result[0].userid;
    console.log(dingId, '要删除的id');
    // const result = await this.ctx.model.Employee.update(
    const result = await this.ctx.service.db.updateMany(
      'Employee',
      { dingId },
      { $set: { status: 0 } }
    );
    console.log(result, '删除结果');
  }
  /**
   * 查看钉钉用户详情
   * @param {*string} access_token 字符串
   * @param {*array} userid_list 用户id
   */
  async check_user_detail(access_token, userid_list) {
    const { ctx } = this;
    return await ctx.curl(
      `https://oapi.dingtalk.com/topapi/smartwork/hrm/employee/list?access_token=${access_token}&userid_list=${userid_list}`,
      {
        method: 'post',
        rejectUnauthorized: true,
        dataType: 'json',
      }
    );
  }
  /**
   * 数组分批次
   * @param {*array} array 原数组
   * @param {*number} subGroupLength  想要分批的数量
   */
  async sliceArr(array, subGroupLength) {
    // 将数据分为等份
    console.log(array);
    console.log(array.length, '分批的数组总长');
    let index = 0;
    const newArray = [];
    while (index < array.length) {
      newArray.push(array.slice(index, (index += subGroupLength)));
    }
    return newArray;
  }
  // 获取当前单位的系统信息
  async mySystemMessage() {
    const { ctx, config } = this;
    const { isRead, pageInfo } = ctx.request.body;
    const _id = ctx.session.adminUserInfo.EnterpriseID;
    if (!_id) {
      ctx.helper.renderFail(ctx, {
        message: '请先登录',
      });
      return;
    }
    const ids = {
      $in: [ _id, ctx.session.adminUserInfo._id ],
    };
    const query = {
      state: 1,
      sendWay: { $in: [ 'systemMessage', 'both' ] },
    };
    if (isRead) {
      query.reader = { $elemMatch: { isRead: +isRead, readerID: ids } };
    } else {
      query.reader = { $elemMatch: { readerID: ids } };
    }
    const pipeline = [
      {
        $match: query, // 这里的query是筛选条件
      },
      {
        $lookup: {
          from: 'superusers', // B表的名称
          localField: 'authorID', // A表中的字段
          foreignField: '_id', // B表中的字段
          as: 'cnameFromSuperusers', // 结果存储的字段名
        },
      },
      {
        $unwind: {
          path: '$cnameFromSuperusers',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $addFields: {
          cname: {
            $ifNull: [ '$cnameFromSuperusers.cname', null ], // 如果
          },
        },
      },
      {
        $lookup: {
          from: 'adminorgs', // C表的名称
          let: { aAuthorID: '$authorID', oldName: '$cname' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: [ '$_id', '$$aAuthorID' ] },
                    { $eq: [ null, '$$oldName' ] }, // 只有当B表中没有找到时才查找C表
                  ],
                },
              },
            },
            {
              $project: {
                cname: {
                  $ifNull: [
                    '$shortName', // 如果shortName不存在，
                    '$cname', // 则尝试使用cnameFromAdminorgs.cname
                  ],
                },
              },
            },
          ],
          as: 'cnameFromAdminorgs',
        },
      },
      {
        $unwind: {
          path: '$cnameFromAdminorgs',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $addFields: {
          'authorID.cname': {
            $ifNull: [ '$cname', '$cnameFromAdminorgs.cname' ], // 如果B表中有cname，则使用B表的cname，否则使用C表的cname
          },
        },
      },
      {
        $project: {
          authorGroup: 1,
          files: 1,
          title: 1,
          message: 1,
          authorID: 1,
          informer: 1,
          date: 1,
        },
      },
      {
        $sort: {
          date: -1, // 按date字段降序排序
        },
      },
      {
        $skip: (pageInfo.current - 1) * pageInfo.pageSize, // 跳过前面的记录，实现分页
      },
      {
        $limit: pageInfo.pageSize, // 限制返回的记录数，实现分页
      },
    ];
    const data = await ctx.service.db.aggregate(
      'MessageNotification',
      pipeline
    );
    pageInfo.total = await ctx.service.db.find(
      'MessageNotification',
      query,
      {},
      { count: true }
    ); // 总数
    // 处理返回的数据 文件 时间格式等
    const upload_http_path = config.upload_http_path;
    const list = data.map(ele => {
      const newEle = JSON.parse(JSON.stringify(ele));
      if (ele.files) {
        newEle.files = ele.files.map(async fileName => {
          return {
            url: await ctx.helper.concatenatePath({
              path: `${upload_http_path}/messageNotification/${ele._id}/${fileName}`,
            }),
            name: fileName,
          };
        });
      }
      newEle.date = moment(ele.date).format('YYYY-MM-DD HH:mm');
      return newEle;
    });
    ctx.helper.renderSuccess(ctx, {
      data: {
        pageInfo,
        docs: list,
      },
      message: '数据获取成功',
    });
  }
  // 获取页脚信息
  async getEnvConfig() {
    const { ctx, config } = this;
    try {
      const envConfig = await ctx.curl(`${config.iServiceHost}/api/envConfig`, {
        dataType: 'json',
        data: { host: ctx.host },
      });
      return {
        recordNumber: envConfig.data.data.recordNumber || '',
        footerContent: envConfig.data.data.footerContent || [],
      };
    } catch (err) {
      return {
        recordNumber: '',
        footerContent: [],
      };
    }
  }
  // 获取加密公钥
  async getPublicKey() {
    const { ctx } = this;
    const publicKeyRes = await ctx.curl(
      `${this.config.iServiceHost}/crypto/getPublicKey`,
      {
        method: 'GET',
        dataType: 'json', // 返回的数据类型
        data: {},
      }
    );
    if (publicKeyRes.status !== 200) {
      throw new Error('获取公钥失败');
    }
    return { publicKey: publicKeyRes.data.data.publicKey };
  }
  // 杭州入口页
  async hzEnter() {
    const { ctx } = this;
    try {
      const configs = await ctx.helper.reqJsonData('systemConfig/getConfig');
      const { showImgCode } = configs || [];
      // 获取页脚信息
      const envConfig = await this.getEnvConfig();
      const domainNames = this.app.config.domainNames;
      // domainNames.super = domainNames.super + '/admin/login';
      await ctx.render('hz/enter.html', {
        siteSeo: this.app.config.siteSeo,
        staticRootPath: this.app.config.static.prefix,
        showImgCode,
        ...envConfig,
        domainNames: {
          ...domainNames,
          super: 'https://jg.hzzyws.cn/admin/login',
        },
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 北元集团登录
  async byEnter() {
    const { ctx } = this;
    const { api_host, client_id, redirect_uri, realm_name } =
      this.app.config.oidc.keyCloak;
    // 环境变量去掉
    try {
      const toUrl = `${api_host}/realms/${realm_name}/protocol/openid-connect/auth?response_type=code&client_id=${client_id}&redirect_uri=${redirect_uri}&scope=openid`;
      console.log('🚀进入byenter:', toUrl);
      // 转发到返回跳转到http://localhost:4000/realms/dt/protocol/openid-connect/auth?response_type=code%20&client_id=OHCloud&redirect_uri=https://www.keycloak.org/app/ 进行keycloak登录验证
      ctx.redirect(toUrl);
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  async whEnter() {
    const { ctx } = this;
    const { api_host, client_id, redirect_uri } = this.app.config.oidc.keyCloak;
    // 环境变量去掉
    try {
      //       https://iamqas.whchem.com/esc-sso/oidc/authorize?client_id=e64f46daeed18742bb38
      // &response_type=code&redirect_uri=http%3A%2F%2Fohs-qy.cloudqas.whchem.com%2Fwhcallback&oauth_timestamp=1733818853574
      const toUrl = `${api_host}/esc-sso/oidc/authorize?response_type=code&client_id=${client_id}&redirect_uri=${redirect_uri}&oauth_timestamp=${new Date().getTime()}`;
      console.log('🚀进入whenter:', toUrl);
      // 转发到返回跳转到http://localhost:4000/realms/dt/protocol/openid-connect/auth?response_type=code%20&client_id=OHCloud&redirect_uri=https://www.keycloak.org/app/ 进行keycloak登录验证
      ctx.redirect(toUrl);
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  async byCallback() {
    const { ctx, app } = this;
    try {
      // 接受keycloak返回的code
      const { api_host, client_id, client_secret, redirect_uri, realm_name } =
        this.config.oidc.keyCloak;
      const { oidcAutoCreate } = this.config;
      const code = ctx.query.code;
      console.log('code', code);
      // 通过code获取token
      const data = qs.stringify({
        grant_type: 'authorization_code',
        client_id,
        client_secret,
        redirect_uri,
        code,
      });
      const config = {
        method: 'post',
        url: `${api_host}/realms/${realm_name}/protocol/openid-connect/token`,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        data,
      };

      const tokenRes = await axios(config);
      // 解析返回结果
      const { access_token } = qs.parse(tokenRes.data);
      console.log(
        // '🚀 ~ file: home.js:4461 ~ HomeController ~ byCallback ~ access_token:',
        access_token
      );
      const authorization = `Bearer ${access_token}`;
      // console.log('🚀 ~  authorization:', authorization);
      // 通过token获取用户信息
      const userInfo = await axios.get(
        `${api_host}/realms/${realm_name}/protocol/openid-connect/userinfo`,
        {
          headers: {
            Authorization: authorization,
          },
        }
      );
      const { userName } = app.config.byjtAdmin;
      if (userInfo.data && userInfo.data.user_name === userName) {
        await this.toOpt(userInfo.data);
        ctx.auditLog(
          '从企业端自动跳转到运营端',
          `北元集团管理员upn: ${userName}`
        );
        return;
      }
      // 根据user_name对应unitCode
      // 先在adminuser表中查找
      // let adminuserInfo = await ctx.model.AdminUser.findOne({
      let adminuserInfo = await ctx.service.db.findOne('AdminUser', {
        unitCode: userInfo.data.user_name,
      }, {}, { authCheck: false });
      if (!adminuserInfo) {
        if (!oidcAutoCreate) {
          throw new Error('暂无权限登录');
        }
        // const employeeInfo = await ctx.model.Employee.findOne({
        const employeeInfo = await ctx.service.db.findOne('Employee', {
          unitCode: userInfo.data.user_name,
        });
        if (employeeInfo) {
          adminuserInfo = await ctx.model.AdminUser.create({
            _id: employeeInfo._id,
            userName: employeeInfo.unitCode,
            name: employeeInfo.name,
            phoneNum: employeeInfo.phoneNum,
            unitCode: employeeInfo.unitCode,
            EnterpriseID: employeeInfo.EnterpriseID,
            IDcard: employeeInfo.IDNum,
            newAddEnterpriseID: employeeInfo.EnterpriseID,
            employees: [ employeeInfo._id ],
          });

          // await ctx.model.Adminorg.updateOne(
          await ctx.service.db.updateOne(
            'Adminorg',
            { _id: employeeInfo.EnterpriseID },
            {
              $addToSet: {
                adminArray: adminuserInfo._id,
              },
            }
          );
        } else {
          throw new Error('暂无权限登录');
        }
      }

      const adminUserToken = jwt.sign(
        {
          _id: adminuserInfo._id,
          EnterpriseID: adminuserInfo.newAddEnterpriseID,
          byToken: tokenRes.data.id_token,
        },
        this.app.config.encrypt_key,
        {
          expiresIn: '30day',
        }
      );
      // 存入cookie
      console.log(
        '🚀 ~ file: home.js:4487 ~ HomeController ~ byCallback ~ adminUserToken:',
        adminUserToken
      );
      ctx.cookies.set(
        'admin_' + this.app.config.auth_cookie_name,
        adminUserToken,
        {
          path: '/',
          maxAge: 1000 * 60 * 60 * 24 * 30,
          signed: true,
          httpOnly: false,
        }
      );

      ctx.redirect('/admin/dashboard');
      // ctx.body = JSON.stringify(userInfo.data);
    } catch (error) {
      await ctx.render('manage/error.html', {
        status: 401,
        message: '无权限访问',
      });
      ctx.auditLog('oidc认证登录错误', `${error}`, 'error');
    }
  }

  async whCallback() {
    const { ctx } = this;
    try {
      console.log('ssssssssssss');
      // 接受keycloak返回的code
      const { api_host, client_id, client_secret, redirect_uri } =
        this.config.oidc.keyCloak;
      const { byOidcAutoCreate } = this.config;
      const code = ctx.query.code;
      const pages = ctx.query.pages;
      console.log('pages', pages);
      console.log('code', code);
      // 通过code获取token
      const data = qs.stringify({
        grant_type: 'authorization_code',
        client_id,
        client_secret,
        redirect_uri,
        code,
      });
      const config = {
        method: 'post',
        //         https://地址:端口/ esc-sso/oidc/accessToken?grant_type=authorization_code
        // &oauth_timestamp=[oauth_timestamp]&client_id=[clientId]&client_secret=[clientSecret]
        // &code=[code]&redirect_uri=[appRedirectUrl]
        // url: `${api_host}/realms/${realm_name}/protocol/openid-connect/token`,
        url: `${api_host}/esc-sso/oidc/accessToken?grant_type=authorization_code&oauth_timestamp=${new Date().getTime()}&client_id=${client_id}&client_secret=${client_secret}&code=${code}&redirect_uri=${redirect_uri}`,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        data,
      };
      console.log('wh post code config', config);
      const tokenRes = await axios(config);
      // 解析返回结果
      const { access_token, id_token } = qs.parse(tokenRes.data);
      console.log(
        // '🚀 ~ file: home.js:4461 ~ HomeController ~ byCallback ~ access_token:',
        access_token,
        id_token
      );
      const authorization = `Bearer ${access_token}`;
      // console.log('🚀 ~  authorization:', authorization);
      // 通过token获取用户信息
      const userInfo = await axios.get(
        // https://地址:端口/ esc-sso/oidc/profile?access_token=[token]
        `${api_host}/esc-sso/oidc/profile?access_token=${access_token}`,
        {
          headers: {
            Authorization: authorization,
          },
        }
      );
      console.log('wh userInfo', userInfo.data);
      // 根据user_name对应unitCode
      // 先在adminuser表中查找
      // let adminuserInfo = await ctx.model.AdminUser.findOne({
      let adminuserInfo = await ctx.service.db.findOne('AdminUser', {
        _id: userInfo.data.account_no,
      }, {}, { authCheck: false });
      if (!adminuserInfo) {
        if (!byOidcAutoCreate) {
          throw new Error('暂无权限登录');
        }
        // const employeeInfo = await ctx.model.Employee.findOne({
        const employeeInfo = await ctx.service.db.findOne('Employee', {
          _id: userInfo.data.account_no,
        });
        if (employeeInfo) {
          adminuserInfo = await ctx.model.AdminUser.create({
            _id: employeeInfo._id,
            userName: employeeInfo._id,
            name: employeeInfo.name,
            phoneNum: employeeInfo.phoneNum,
            unitCode: employeeInfo.unitCode,
            EnterpriseID: employeeInfo.EnterpriseID,
            IDcard: employeeInfo.IDNum,
            newAddEnterpriseID: employeeInfo.EnterpriseID,
            employees: [ employeeInfo._id ],
          });

          // await ctx.model.Adminorg.updateOne(
          await ctx.service.db.updateOne(
            'Adminorg',
            { _id: employeeInfo.EnterpriseID },
            {
              $addToSet: {
                adminArray: adminuserInfo._id,
              },
            }
          );
        } else {
          throw new Error('暂无权限登录');
        }
      }

      const adminUserToken = jwt.sign(
        {
          _id: adminuserInfo._id,
          EnterpriseID: adminuserInfo.newAddEnterpriseID,
          byToken: tokenRes.data.id_token,
        },
        this.app.config.encrypt_key,
        {
          expiresIn: '30day',
        }
      );
      // 存入cookie
      console.log(
        '🚀 ~ file: home.js:4487 ~ HomeController ~ byCallback ~ adminUserToken:',
        adminUserToken
      );
      ctx.cookies.set(
        'admin_' + this.app.config.auth_cookie_name,
        adminUserToken,
        {
          path: '/',
          maxAge: 1000 * 60 * 60 * 24 * 30,
          signed: true,
          httpOnly: false,
        }
      );
      ctx.redirect(`/${pages}`);
      // ctx.redirect('/admin/dashboard');
      // ctx.body = JSON.stringify(userInfo.data);
    } catch (error) {
      await ctx.render('manage/error.html', {
        status: 401,
        message: '无权限访问',
      });
      ctx.auditLog('oidc认证登录错误', `${error}`, 'error');
    }
  }

  // 北元集团root用户自动跳转到运营端
  async toOpt(byjtAdminInfo) {
    const { ctx, config } = this;
    try {
      const { privateKey } = config.ecdhOptions;
      const { publicKey, sharedKey } = config.optEcdhOptions;
      const _sharedKey =
        sharedKey || ecdh.generateSharedKey(privateKey, publicKey);
      const content = JSON.stringify({
        userName: byjtAdminInfo.user_name,
        given_name: byjtAdminInfo.given_name,
        family_name: byjtAdminInfo.family_name,
        timestamp: new Date().getTime(),
      });
      const token = ecdh.encrypt(content, _sharedKey);
      const operate = config.domainNames.operate;
      ctx.redirect(`${operate}/api/ssoLogin?token=${token}`);
    } catch (err) {
      ctx.auditLog(
        '从企业端自动跳转到运营端失败',
        `北元集团管理员upn: ${byjtAdminInfo.user_name}`,
        'error'
      );
    }
  }
  // 获取杭州登录页面
  async hzLoginPage() {
    const { ctx } = this;
    try {
      if (ctx.session.adminUserInfo) {
        console.log('getLoginPage, adminUserInfo:', ctx.session.adminUserInfo);
        if (
          ctx.session.showLoginPage &&
          ctx.session.showLoginPage === 'exist'
        ) {
          // 如果用户已登陆   userLogin为

          const configs = await ctx.helper.reqJsonData(
            'systemConfig/getConfig'
          );
          const { showImgCode } = configs.data || [];
          console.log('是否显示验证码', showImgCode);
          // const { userLogin } = ;
          const companys = await ctx.service.db.find('Adminorg', {
            _id: ctx.session.adminUserInfo.EnterpriseID,
          });
          const cname = companys[0].cname;
          const name = ctx.session.adminUserInfo.name;
          console.log('companys=========', companys[0]);

          await ctx.render('hz/login.html', {
            siteSeo: this.app.config.siteSeo,
            staticRootPath: this.app.config.static.prefix,
            showImgCode,
            userLogin: '1',
            cname,
            name,
            ...(await this.getEnvConfig()), // 页脚信息
            ...(await this.getPublicKey()), // 公钥
          });
        } else {
          ctx.redirect('/admin/dashboard');
        }
      } else {
        const configs = await ctx.helper.reqJsonData('systemConfig/getConfig');
        const { showImgCode } = configs.data || [];
        await ctx.render('hz/login.html', {
          siteSeo: this.app.config.siteSeo,
          staticRootPath: this.app.config.static.prefix,
          showImgCode,
          userLogin: '2',
          ...(await this.getEnvConfig()), // 页脚信息
          ...(await this.getPublicKey()), // 公钥
        });
      }
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  // 山西焦煤入口页
  async sxccEnter() {
    const { ctx } = this;
    try {
      const configs = await ctx.helper.reqJsonData('systemConfig/getConfig');
      const { showImgCode } = configs || [];
      // 获取页脚信息
      const envConfig = await this.getEnvConfig();
      await ctx.render('sxcc/enter.html', {
        siteSeo: this.app.config.siteSeo,
        staticRootPath: this.app.config.static.prefix,
        showImgCode,
        ...envConfig,
        domainNames: this.app.config.domainNames,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  // 福州入口页
  async fzEnter() {
    const { ctx } = this;
    try {
      const configs = await ctx.helper.reqJsonData('systemConfig/getConfig');
      const { showImgCode } = configs || [];
      // 获取页脚信息
      const envConfig = await this.getEnvConfig();
      await ctx.render('fz/enter.html', {
        siteSeo: this.app.config.siteSeo,
        staticRootPath: this.app.config.static.prefix,
        showImgCode,
        ...envConfig,
        domainNames: this.app.config.domainNames,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  // 福建入口页
  async fjEnter() {
    const { ctx } = this;
    try {
      const configs = await ctx.helper.reqJsonData('systemConfig/getConfig');
      const { showImgCode } = configs || [];
      // 获取页脚信息
      const envConfig = await this.getEnvConfig();
      await ctx.render('fj/enter.html', {
        siteSeo: this.app.config.siteSeo,
        staticRootPath: this.app.config.static.prefix,
        showImgCode,
        ...envConfig,
        domainNames: this.app.config.domainNames,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  // demo入口页
  async demoEnter() {
    const { ctx } = this;
    try {
      const configs = await ctx.helper.reqJsonData('systemConfig/getConfig');
      const { showImgCode } = configs || [];
      // 获取页脚信息
      const envConfig = await this.getEnvConfig();
      await ctx.render('demo/enter.html', {
        siteSeo: this.app.config.siteSeo,
        staticRootPath: this.app.config.static.prefix,
        showImgCode,
        ...envConfig,
        domainNames: this.app.config.domainNames,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  // 福州入口页
  async xhlEnter() {
    const { ctx } = this;
    try {
      const configs = await ctx.helper.reqJsonData('systemConfig/getConfig');
      const { showImgCode } = configs || [];
      // 获取页脚信息
      const envConfig = await this.getEnvConfig();
      await ctx.render('xhl/enter.html', {
        siteSeo: this.app.config.siteSeo,
        staticRootPath: this.app.config.static.prefix,
        showImgCode,
        ...envConfig,
        domainNames: this.app.config.domainNames,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  // 莲都区入口页
  async ldqEnter() {
    const { ctx } = this;
    try {
      const configs = await ctx.helper.reqJsonData('systemConfig/getConfig');
      const { showImgCode } = configs || [];
      // 获取页脚信息
      const envConfig = await this.getEnvConfig();
      await ctx.render('ldq/enter.html', {
        siteSeo: this.app.config.siteSeo,
        staticRootPath: this.app.config.static.prefix,
        showImgCode,
        ...envConfig,
        domainNames: this.app.config.domainNames,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  async getPersonInfo() {
    const { ctx } = this;
    // const personInfo = ctx.session.adminUserInfo.phoneNum;
    const personInfo = ctx.session.adminUserInfo.userName;
    ctx.helper.renderSuccess(ctx, {
      data: personInfo,
      message: '数据获取成功',
    });
  }
  // 获取验证码
  async getImgCode(ctx) {
    const { app } = this;
    // 生成验证码

    const captcha = svgCaptcha.createMathExpr({
      size: 4, // 随机字符串的数量
      fontSize: 40,
      ignoreChars: 'Ooli', // 过滤掉一些字符，如0o1i
      width: 100,
      height: 40,
      noise: 2,
      color: false,
      background: '#ffffff',
      mathMin: 0,
      mathMax: 9,
      mathOperator: '+-',
    });
    // 生成唯一的验证码ID
    const captchaId = uuidv4();

    // 验证码存储在Redis中，设置过期时间为5分钟
    const captchaKey = `captcha:${captchaId}`;
    await app.redis.set(captchaKey, captcha.text, 'EX', 300);

    // 将验证码ID设置到Cookie中，用于后续验证
    ctx.cookies.set('captchaId', captchaId, {
      httpOnly: true,
      maxAge: 300 * 1000,
      signed: true,
    });

    // 保持向后兼容
    ctx.session.imageCode = captcha.text;

    ctx.response.type = 'image/svg+xml'; // 返回的类型
    ctx.body = captcha.data; // 返回一张图片
  }
  async getSurveyPage() {
    const { ctx } = this;
    try {
      // 获取传参
      const query = ctx.params;
      const { id } = query;
      const list = await ctx.curl(
        `${this.config.iServiceHost}/questionnaire/getQuestionnaireDetailById`,
        {
          method: 'POST',
          dataType: 'json', // 返回的数据类型
          data: { id },
        }
      );
      if (list.data.data.publishStatus === 0) {
        await ctx.render('index/noSurvey.html');
        // throw new Error('获取问卷详情失败');
      } else {
        await ctx.render('index/survey.html', {
          siteSeo: this.app.config.siteSeo,
          server_path: this.app.config.server_path,
          staticRootPath: this.app.config.static.prefix,
          data: JSON.stringify(list.data.data),
        });
      }
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 校验验证码
  async checkPhoneCode(ctx) {
    const { messageCode, countryCode, phoneNum } = ctx.request.body;
    let errMsg = '';
    try {
      const currentCode = await ctx.helper.getCache(
        ctx.app.config.session_secret +
          '_sendMessage_resetPassword_' +
          (countryCode + phoneNum.trim())
      );
      if (
        messageCode === '' ||
        !validator.isNumeric(messageCode.toString()) ||
        messageCode.length !== 6 ||
        currentCode !== messageCode
      ) {
        errMsg = ctx.__('validate_inputCorrect', [
          ctx.__('label_user_imageCode'),
        ]);
      }

      if (errMsg) {
        ctx.helper.clearCache(
          countryCode + phoneNum,
          '_sendMessage_resetPassword_'
        );
        return ctx.helper.renderCustom(ctx, {
          status: 400,
          message: errMsg,
        });
      }
      ctx.helper.renderSuccess(ctx, {
        message: '验证成功',
      });
    } catch (error) {
      ctx.helper.renderCustom(ctx, {
        status: 500,
        message: error,
      });
    }
  }

  async articleDetail() {
    const { ctx } = this;
    try {
      const query = ctx.request.query;
      await ctx.model.Content.updateOne(query, { $inc: { clickNum: 1 } });
      const data = await ctx.model.Content.findOne(query)
        .populate('uAuthor', 'name userName logo');
      ctx.helper.renderSuccess(ctx, { data, message: '数据获取成功' });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 点击问卷调查，获取和校验问卷系统的用户信息，并获取单点登录的token
  async checkWjUser() {
    const { ctx, config } = this;
    try {
      const adminUserInfo = ctx.session.adminUserInfo;
      const org = await ctx.model.Adminorg.findOne({ _id: adminUserInfo.EnterpriseID }, { cname: 1, code: 1 });
      // 使用原生查询跳过 Mongoose 的脱敏插件
      const employeeCollection = ctx.model.Employee.collection;
      const employee = await employeeCollection.findOne(
        { _id: adminUserInfo._id },
        { projection: { gender: 1, phoneNum: 1 } }
      );
      const params = {
        id: adminUserInfo._id,
        name: adminUserInfo.name,
        phone: employee ? employee.phoneNum : '',
        gender: employee ? employee.gender : '',
        auth_account: adminUserInfo.userName,
        org_id: org._id,
        org_name: org.cname,
        org_code: org.code,
        org_short_name: org.cname,
      };
      const { data } = await ctx.curl(
        `${config.iService2Host}/wj`,
        {
          method: 'POST',
          dataType: 'json',
          data: params,
        }
      );
      if (data.code !== 200) {
        ctx.auditLog('问卷系统用户验证', `用户验证失败：${data.message}`, 'error');
        return ctx.helper.renderFail(ctx, {
          message: data.message || '用户验证失败',
          data: data.data,
        });
      }
      ctx.helper.renderSuccess(ctx, {
        data: data.data,
        message: data.message || '用户验证成功' });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error.message,
      });
    }
  }

  getWhGISbaseLayerUrl() {
    const { ctx, config } = this;
    console.log('app.config.whGISbaseLayerUrl', config.whGISbaseLayerUrl);
    ctx.helper.renderSuccess(ctx, {
      data: config.whGISbaseLayerUrl || '',
    });
  }
}

module.exports = HomeController;
