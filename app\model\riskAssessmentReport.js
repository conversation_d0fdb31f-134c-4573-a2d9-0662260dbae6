/**
 * Created by <PERSON><PERSON> on 2020/11/30.
 * 风险评估数据
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const ctx = app.createAnonymousContext();
  const permissionPlugin = require('../utils/permission.js');

  const RiskAssessmentReportSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    jobHealthId: String, // 项目id
    preventAssessId: String, // 防治自查id
    EnterpriseID: {
      type: String,
    },

    year: {
      type: String,
    },

    /**
     * 填表人信息：
     *  如果是机构端上报的 那填表人是机构的账号否则是企业的账号
     */
    adminUserId: {
      type: String,
    },
    fillerName: {
      type: String,
    }, // 填表人
    fillerPhoneNumber: {
      type: String,
    }, // 联系电话

    fillDate: {
      type: Date,
      default: Date.now,
    }, // 填表日期,也就是上报日期，年份从这个字段来取
    problem: String,
    rectification: String,
    situation: String,

    seriousHarmExceedEmployees: Number, // 接触严重危害因素超标人数
    seriousHarmExceedLevel: String, // 级别

    seriousHarmNoExceedEmployees: Number, // 接触严重危害因素不超标人数
    seriousHarmNoExceedLevel: String, // 级别

    noSeriousHarmExceedEmployees: Number, // 接触一般危害因素超标人数
    noSeriousHarmExceedLevel: String, // 级别

    noSeriousHarmNoExceedEmployees: Number, // 接触一般危害因素不超标人数
    noSeriousHarmNoExceedLevel: String, // 级别

    majorManegerCount: Number, // 管理人员专职人数
    partTimeManegerCount: Number, // 管理热源兼职人数
    hasRole: String, // 是否
    HazardFactors: [ // 按危害因素分组各危害因素接害情况
      { employee: Array, // 接害员工id数组
        value: Number, // 接害人数
        label: String, // 危害因素类别 化学有害因素、粉尘、物理、生物、放射
        harmFactors: [ // 具体危害因素接害情况
          { employee: Array, // 接害员工id数组
            label: String, // 危害因素名称
            value: Number, // 接害人数
          }],
      },
    ],
    contactHazardCount: Number, // 接触职业病危害总人数
    ODzaigangCount: Number, // 职业病累计人数 - 目前在岗
    cumulativeTotalCount: Number, // 职业病累计人数 - 历年累计
    allEmployeesCount: Number, // 人员总数
    healthCheckCount: {
      shangGang: Number, // 上岗前体检人数
      shangGangInspectionRequired: Number, // 上岗前应检体检人数

      zaiGang: Number, // 在岗体检
      zaiGangInspectionRequired: Number, // 在岗应检

      liGang: Number, // 离岗体检
      liGangInspectionRequired: Number, // 离岗应检体检

    }, // 健康监护数据{ _id: null, shangGang: 3, zaiGang: 0, liGang: 0 },
    corpPhoneNumber: String, // 联系电话
    parentCompany: String, // 上属企业
    cname: String, // 企业名称
    code: String, // 信用代码
    corp: String, // 法人
    districtRegAdd: String, // 注册地址
    regAdd: String, // 详细注册地址
    workAddress: Array, // 工作场所地址
    regType: String, // 注册类型
    companyScale: String, // 企业规模
    industryCategory: Array, // 行业分类

    assessManageLevel: {
      type: String,
      default: 'C',
    }, // 职业健康管理状况等级，0A、1B、2C ++++++++++++++
    assessExposeLevel: {
      type: Number,
      default: 0,
    }, // 职业病危害暴露风险等级，0低、1中、2高 ++++++++++++
    harmFactorTouchInfo: [{
      category: String, // 危害因素分类
      alisa: String, // 别名
      harmFactors: Array, // 危害因素集合
      touchEmployeesCount: Number, // 接触人数
    }],
    assessExposeData: {
      type: [ new Schema({
        category: String, // 危害因素类型
        workspace: String, // 车间
        station: String, // 岗位
        touchEmployees: Number, // 定员，接触人数
        checkProject: String, // 危害因素/检测项目
        TWA: String,
        STEL: String,
        MAC: String,
        divisionData: String, // PE/PC-TWA
        realDivisionData: String, // PE/PC-TWA 计算出来的真实值
        equalLevel: String, // 噪声等效声级
        checkData: String, // 其他因素浓度/强度
        checkResult: String, // 检测结果/接触水平 符合、不符合
        isSerious: String, // 职业病危害因素性质 严重、一般
      }) ],
      default: undefined,
    },
    assessmentResult: {
      type: Number,
      // default: 0,
    }, // 职业病危害综合风险等级，0甲类、1乙类、2丙类 +++++++

    isReported: { type: Number, default: 0 }, // 0未上报，1已上报
    status: {
      type: Number,
      default: 0,
    }, // 0未审核，1审核通过，2打回
    repulseReason: {
      type: String, // 打回原因
      default: '',
    },
    reportFilePath: String, // 分类分级报告word名称
    tableFilePath: String, // 防治自查word名称
    exposeFilePath: String, // 用人单位职业病危害暴露情况调查和风险评估word名称
    source: {
      default: '', // 数据来源 service | enterprise
      type: String,
    },
    riskassessmentDate: {
      type: Date,
    },
  });

  RiskAssessmentReportSchema.index({ EnterpriseID: 1 });
  RiskAssessmentReportSchema.index({ year: 1 });
  RiskAssessmentReportSchema.index({ adminUserId: 1 });
  RiskAssessmentReportSchema.index({ fillerName: 1 });
  RiskAssessmentReportSchema.index({ fillerPhoneNumber: 1 });

  RiskAssessmentReportSchema.plugin(permissionPlugin, {
    ctx,
    fieldMapping: {
      enterpriseId: 'EnterpriseID',
    },
  });
  return mongoose.model('RiskAssessmentReport', RiskAssessmentReportSchema, 'riskAssessmentReport');

};
