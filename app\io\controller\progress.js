const { updateData } = require('@utils/tools.js');
let onlineMonitoringTimer = null;
module.exports = app => {
  class Controller extends app.Controller {
    // 在线监测 xxn add
    async onlineMonitoring(ctx) {
      const nsp = ctx.socket.nsp;
      const id = ctx.socket.id; // 获取到连接到此命名空间的客户机ID
      try {
        // 获取企业的所有设备
        const deviceList = await ctx.service.devices.list();
        nsp.sockets[id].emit('deviceList', deviceList);
        if (deviceList.length === 0) return;
        // 获取设备的最新数据
        const deviceIDs = deviceList.map(ele => ele.deviceID);
        const client = nsp.sockets[id];

        const newData = await ctx.service.devicesData.newData(deviceIDs);
        client.emit('newData', newData);

        if (onlineMonitoringTimer) clearInterval(onlineMonitoringTimer);
        onlineMonitoringTimer = setInterval(async () => {
          const newData = await ctx.service.devicesData.newData(deviceIDs);
          client.emit('newData', newData);
        }, 2000);

      } catch (error) {
        if (onlineMonitoringTimer) clearInterval(onlineMonitoringTimer);
        ctx.auditLog('在线监测错误', JSON.stringify(error), 'error');
        nsp.sockets[id].emit('err', { errMsg: '服务器出错' + JSON.stringify(error) });
      }
    }
    // 关闭在线监测定时器
    async closeOnlineMonitoring() {
      if (onlineMonitoringTimer) clearInterval(onlineMonitoringTimer);
    }
    async employees(ctx) {
      // console.log(999, ctx.args)
      console.log(999, ctx.socket.id);
      const nsp = ctx.socket.nsp; // 获取到
      const id = ctx.socket.id; // 获取到连接到此命名空间的客户机ID
      if (nsp.sockets[id]) {
        nsp.sockets[id].emit('id', id); // 然后将 ID 发送给前端 / 当然也可以通过{ EnterpriseID: id } 存到 redis中
      }
      ctx.body = '发送成功';
    }

    async addMills(ctx) {
      const nsp = ctx.socket.nsp; // 获取到
      const id = ctx.socket.id; // 获取到连接到此命名空间的客户机ID
      try {
        const res = await ctx.service.mill.addMill(JSON.parse(ctx.args[0]));
        for (let i = 0; i < res; i++) {
          if (nsp.sockets[id]) {
            nsp.sockets[id].emit('progress', i);
            if (i === res - 1) {
              nsp.sockets[id].emit('addMillsEnd', res);
            }
          }
        }
      } catch (error) {
        // console.log(error);
        nsp.sockets[id].emit('addMillsEnd', { errMsg: '服务器出错' });

      }
    }

    async downloadAllRecords(ctx) {
      const nsp = ctx.socket.nsp; // 获取到
      const id = ctx.socket.id; // 获取到连接到此命名空间的客户机ID
      try {
        console.log(ctx.args[0]);
        const year = new Date(new Date(JSON.parse(ctx.args[0]).year).getTime() + 28800000).getFullYear().toString();
        const group = ctx.session.adminUserInfo;
        const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
        const enterpriseIds = await ctx.service.employee.findSubCompany(EnterpriseID, 'branch');
        console.log('🚀 ~ file: progress.js:77 ~ Controller ~ downloadAllRecords ~ enterpriseIds:', enterpriseIds);
        // const recordMake = await ctx.model.AdminGroup.findOne({ name: '企业端档案托管用户' });
        const recordMake = await ctx.service.db.findOne('AdminGroup', { name: '企业端档案托管用户' });
        // 企业端档案托管用户的一些判断逻辑，不知道要不要啊，北元没走到
        if (recordMake && (group.group !== recordMake._id)) {
          // 要有年份的筛选，所以之前的字段格式不对
          // await ctx.model.Adminorg.updateOne({ _id: EnterpriseID }, { $inc: { downRecordCounts: 1 } });
          // const res = await ctx.model.Adminorg.findOne({ _id: EnterpriseID }, { downRecordCounts: 1 });
          const res = await ctx.service.db.findOne('Adminorg', { _id: EnterpriseID }, { downRecordCounts: 1 });
          let count = 0;
          if (res.downRecordCounts && res.downRecordCounts.length && res.downRecordCounts[res.downRecordCounts.length - 1].count) {
            count = res.downRecordCounts[res.downRecordCounts.length - 1].count;
          }
          await ctx.service.db.updateOne('Adminorg', { _id: EnterpriseID }, { $push: {
            downRecordCounts: {
              count: count + 1,
              date: new Date(),
            },
          } });
        }
        if (recordMake && (group.group === recordMake._id)) { // linht 托管账号自动生成部分档案数据
          ctx.auditLog('托管账号自动生成档案', `公司id${EnterpriseID} 。`, 'info');
          const info = await ctx.service.autoGenerateFiles.autoGenerateFiles(EnterpriseID, year);
          ctx.helper.renderFail(ctx, {
            message: info,
          });
        }
        // 档案标志1 调用生成档案service
        // #region 生成档案
        const res = await ctx.service.record.downloadAll(JSON.parse(ctx.args[0]), nsp.sockets[id]);
        for (const itemEnterprise of enterpriseIds) {
          await ctx.service.record.downloadAll(
            JSON.parse(ctx.args[0]),
            nsp.sockets[id],
            itemEnterprise
          );
        }
        // const companyInfo = await ctx.model.Adminorg.findOne(
        const companyInfo = await ctx.service.db.findOne(
          'Adminorg',
          { _id: EnterpriseID },
          { cname: 1, downloadRecordList: 1 },
          { lean: true }
        );
        // const companyInfo2 = JSON.parse(JSON.stringify(companyInfo));
        const zipContent = companyInfo.downloadRecordList.find(item => item.year === year);
        if (zipContent) {
          zipContent.url = `${app.config.enterprise_path}` + zipContent.url;
          zipContent.cname = companyInfo.cname;
        }
        // const zipContent2 = companyInfo2.downloadRecordList.find(
        //   item => item.year === year
        // );
        // if (zipContent2) {
        //   zipContent2.url = `${app.config.static.prefix}${app.config.enterprise_http_path}` + zipContent2.url;
        //   zipContent2.cname = companyInfo.cname;
        // }
        const zipContentBranchInfo = await ctx.service.db.find('Adminorg', { _id: { $in: enterpriseIds } }, { cname: 1, downloadRecordList: 1 });
        const zipContentBranch = [];
        for (const item of zipContentBranchInfo) {
          const zipContentBranchItem = item.downloadRecordList.find(item => item.year === year);
          if (zipContentBranchItem) {
            zipContentBranchItem.url = `${app.config.enterprise_path}` + zipContentBranchItem.url;
            zipContentBranchItem.cname = item.cname;
            zipContentBranch.push(zipContentBranchItem);
          }
        }
        const endZipContent = await ctx.service.dealBranch.dealDownloadAllBranch({
          masterZipContent: zipContent,
          branchZipContent: zipContentBranch,
        });
        console.log('🚀 ~ file: progress.js:118 zipContent:', endZipContent);
        if (endZipContent) {
          nsp.sockets[id].emit('downloadAllRecordsEnd', {
            errArr: res.errArr,
            data: res.records,
            companyName: companyInfo.cname,
            zipContent,
            successCount: res.successCount,
          });
        }
        nsp.sockets[id].emit('downloadAllRecordsEnd', {
          errArr: res.errArr,
          data: res.records,
          companyName: companyInfo.cname,
          zipContent,
          successCount: res.successCount,
        });
      } catch (error) {
        console.log(error);
        ctx.auditLog('档案一键下载报错：', JSON.stringify(error), 'error');
        nsp.sockets[id].emit('downloadAllRecordsEnd', { errMsg: '服务器出错' });
      }
    }
    async addCheckAssessments(ctx) {
      const nsp = ctx.socket.nsp; // 获取到
      const id = ctx.socket.id; // 获取到连接到此命名空间的客户机ID
      try {
        const parseRes = await ctx.service.commonService.parseCheckAssessmentsData(ctx, JSON.parse(ctx.args[0]));
        await ctx.service.commonService.add(parseRes);
        nsp.sockets[id].emit('addCheckAssessmentsEnd');
      } catch (error) {
        nsp.sockets[id].emit('addCheckAssessmentsEnd', { errMsg: '服务器出错' });
        console.log(error);
      }
    }

    async addSomeEmployee(ctx) {
      console.time('addSomeEmployee');
      let employeeInfo = {};
      let value = {};
      const nsp = ctx.socket.nsp; // 获取到
      const id = ctx.socket.id; // 获取到连接到此命名空间的客户机ID
      const employeeData = JSON.parse(ctx.args[0]);
      const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
      employeeData.forEach(async item => {
        item.EnterpriseID = EnterpriseID;
        item.status = item.status.includes('离') ? 0 : 1;
        item.departs += '';
        item.departs = item.departs ? item.departs.split(/,|，/gi) : '';
        for (let i = 0; i < item.departs.length; i++) {
          if (!item.departs[i]) {
            item.departs.splice(i, 1);
          } else {
            item.departs[i] = item.departs[i].split(/-|_|\//gi);
          }
        }
      });
      const res = await ctx.service.employee.addSomeEmployee(employeeData, EnterpriseID);
      const errData = [];
      await Promise.allSettled(res).then(async res => {
        // const data = res.filter(item => {
        //   return item.status === 'rejected';
        // });
        for (let i = 0; i < res.length; i++) {
          if (res[i].status === 'rejected') {
            errData.push(res[i].reason);
          }
          // 如果人员导入成功，就处理人员对应的部门
          if (res[i].status === 'fulfilled') {
            value = res[i].value;
            employeeInfo = value.doc;
            if (!res[i].value.forceSave) {
              // 同步或创建adminUser,user htt+++
              await updateData(ctx, 'employee', { _id: employeeInfo._id, EnterpriseID, type: '1' });
            }
            const UDeparts = await ctx.service.employee.departsHandle({
              Uid: employeeInfo._id,
              departs: employeeInfo.departs,
              EnterpriseID,
            });
            /**
             * htt++++++++++
             * 同步工作场所人员以及添加转岗和转部门记录
             */
            await ctx.service.employee.handleUpdateMillEmployee(value.originDoc, value.type, value.originDeparts, value.offDutyTime, UDeparts, employeeInfo, EnterpriseID, value.mills, value.originstation);
          }
          if (nsp.sockets[id]) {
            nsp.sockets[id].emit('progress', res[i]);
            if (i === res.length - 1) {
              if (res.length > errData.length) {
                // htt++++++++++++++++++++++=  更新档案完成度 millConstruction
                await ctx.service.mill.updateFilesCompleteness();
                // 更新档案完成度 employees
                await ctx.service.filesCompleteness.update({
                  employees: { completion: 100 },
                });
              }
              nsp.sockets[id].emit('addSomeEmployeeEnd', errData);
            }
          }
        }
      }).catch(error => {
        console.log(error);
      });
      console.timeEnd('addSomeEmployee');
    }
  }
  return Controller;
};
