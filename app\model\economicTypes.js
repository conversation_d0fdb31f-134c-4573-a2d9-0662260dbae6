/**
 * <AUTHOR>
 * @description 经济类型
 * @createdAt 2021-11-01
 */


module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  // 经济类型
  const economicTypesSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    code: String, // 经济类型编码
    content: [ String ], // 经济类型名称,可能一个编码会对应多个不同的叫法，所以这里存数组
  });


  return mongoose.model('economicTypes', economicTypesSchema, 'economicTypes');

};

