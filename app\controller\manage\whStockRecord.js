/**
 * @file 万华商城库存调整记录控制器 - 管理后台
 * @description 处理库存调整记录相关请求（管理后台）
 * @createDate 2025-03-15
 */

const Controller = require('egg').Controller;

class WhStockRecordManageController extends Controller {
  /**
   * 获取库存调整记录列表
   */
  async list() {
    const { ctx, service } = this;
    try {
      const {
        page = 1,
        limit = 10,
        sku,
        EnterpriseID,
        departmentCode,
        adjustType,
        startDate,
        endDate,
        createBy,
      } = ctx.query;

      const query = {};

      query.EnterpriseID = EnterpriseID || ctx.session.adminUserInfo.EnterpriseID;

      if (adjustType) {
        query.adjustType = adjustType;
      }

      if (createBy) {
        query.createBy = createBy;
      }

      // 日期范围查询
      if (startDate || endDate) {
        query.adjustDate = {};
        if (startDate) {
          query.adjustDate.$gte = new Date(startDate);
        }
        if (endDate) {
          query.adjustDate.$lte = new Date(endDate);
        }
      }

      let result;
      if (sku) {
        // 根据 SKU 查询相关的库存调整记录，同时传入企业ID进行筛选
        result = await service.whStockRecord.listBySku(sku, page, limit, departmentCode);
      } else {
        // 查询普通列表
        result = await service.whStockRecord.list(query, page, limit);
      }

      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.logger.error('获取库存调整记录列表失败:', err);
      ctx.helper.renderFail(ctx, {
        message: err.message || '获取库存调整记录列表失败',
      });
    }
  }

  /**
   * 获取库存调整记录详情
   */
  async detail() {
    const { ctx, service } = this;
    try {
      const { id } = ctx.query;
      if (!id) {
        throw new Error('缺少记录ID');
      }

      const record = await service.whStockRecord.getById(id);
      ctx.helper.renderSuccess(ctx, {
        data: record,
      });
    } catch (err) {
      ctx.logger.error('获取库存调整记录详情失败:', err);
      ctx.helper.renderFail(ctx, {
        message: err.message || '获取库存调整记录详情失败',
      });
    }
  }

  /**
   * 创建库存调整记录
   */
  async create() {
    const { ctx, service } = this;
    try {
      const data = ctx.request.body;

      // 简单验证
      if (!data.details || !data.details.length) {
        throw new Error('库存调整明细不能为空');
      }

      if (!data.adjustType) {
        throw new Error('调整类型不能为空');
      }

      if (!data.adjustReason) {
        throw new Error('调整原因不能为空');
      }

      // 如果是报废类型，必须有报废原因
      if (data.adjustType === 'scrap' && !data.scrapReason) {
        throw new Error('报废原因不能为空');
      }

      // 设置创建人信息
      const adminInfo = ctx.session.adminUserInfo;
      data.createBy = adminInfo._id;
      data.createByName = adminInfo.userName;
      // 如果没有设置调整日期，使用当前时间
      if (!data.adjustDate) {
        data.adjustDate = new Date();
      }
      // 创建记录
      const result = await service.whStockRecord.create(data);

      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '创建库存调整记录成功',
      });
    } catch (err) {
      ctx.logger.error('创建库存调整记录失败:', err);
      ctx.helper.renderFail(ctx, {
        message: err.message || '创建库存调整记录失败',
      });
    }
  }
}

module.exports = WhStockRecordManageController;
