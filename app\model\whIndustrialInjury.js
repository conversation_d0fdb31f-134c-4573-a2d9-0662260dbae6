/**
 * @file 工伤管理模型
 * @description 管理工伤事故记录、处理跟踪信息
 * <AUTHOR>
 * @createDate 2025-03-18
 */
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');
  const { createWorkspacePermissionFilter } = require('../utils/workspacePermissionFilter.js');
  const IndustrialInjurySchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    state: {
      type: String,
      default: '1', // 1正常，0删除
      enum: [ '0', '1' ],
    },
    // 公司标识，对应SQL中的siteeventid
    injuryId: {
      type: String,
      unique: true,
    },
    // 事故类型，对应SQL中的EventType
    eventType: {
      type: String,
      enum: [
        'Non PSM - Environmental Pollution', 'Non PSM - Fire/Explosion',
        'Non PSM - Injury & Illness', 'Non PSM - Other',
        'Non PSM - Property Damage', 'Non PSM - Security',
        'Non PSM - Vehicle Accident', 'PSM - Abnormal Production',
        'PSM - Environmental Pollution', 'PSM - Fire/Explosion',
        'PSM - Injury & Illness', 'PSM - Spill/Release',
        'Required to Report Event',
      ],
    },
    // 是否为主事故，对应SQL中的Primary Event
    isPrimary: {
      type: Boolean,
      default: true,
    },
    // 公司信息
    company: {
      type: String,
      required: true,
      trim: true,
    },
    // 现场ID，对应SQL中的SiteID
    siteId: {
      type: Number,
    },
    // 现场名称，对应SQL中的Sitename
    siteName: {
      type: String,
      trim: true,
    },
    // 区域ID，对应SQL中的OrgID
    orgId: {
      type: Number,
    },
    // 区域名称，对应SQL中的OrgName
    orgName: {
      type: String,
      trim: true,
    },
    // 子区域名称，对应SQL中的SubOrgName
    subOrgName: {
      type: String,
      trim: true,
    },
    // 二级单位/部门，对应SQL中的dept
    department: {
      type: String,
      required: true,
      trim: true,
    },
    // 部门ID，对应SQL中的DeptID
    departmentId: {
      type: Number,
    },
    // 装置/模块，对应SQL中的subsite
    module: {
      type: String,
      trim: true,
    },
    // 班组，对应SQL中的Shift
    shift: {
      type: String,
      trim: true,
    },
    // 伤员信息 - 用于手动录入，不从SQL数据源自动同步
    injuredEmployees: [{
      _id: {
        type: String,
        default: shortid.generate,
      }, // 伤员记录ID
      name: String, // 伤员姓名
      employeeId: String, // 伤员工号
      gender: String, // 性别
      age: Number, // 年龄
      position: String, // 职位
      department: String, // 部门
      injuryLevel: String, // 伤害程度
      injuryPart: String, // 受伤部位
      treatmentStatus: String, // 治疗状态
      workStatus: String, // 工作状态
      contact: String, // 联系方式
      remark: String, // 备注
    }],
    // 主要伤员姓名 - 用于兼容过渡
    employeeName: {
      type: String,
      trim: true,
    },
    // 主要伤员工号 - 用于兼容过渡
    employeeId: {
      type: String,
      trim: true,
    },
    // 事故发生时间，对应SQL中的event_date + event_time
    occurrenceTime: {
      type: Date,
      default: Date.now,
      required: true,
    },
    // 事故发生日期，对应SQL中的EventDate
    eventDate: {
      type: String,
      trim: true,
    },
    // 事故发生时间，对应SQL中的EventTime
    eventTime: {
      type: String,
      trim: true,
    },
    // 事故标题，对应SQL中的event_title
    injuryTitle: {
      type: String,
      required: true,
      trim: true,
    },
    // 事故描述，对应SQL中的event_description
    injuryDescription: {
      type: String,
      trim: true,
    },
    // 国家，对应SQL中的Country
    country: {
      type: String,
      default: 'China',
      trim: true,
    },
    // 世界区域，对应SQL中的WorldRegion
    worldRegion: {
      type: String,
      trim: true,
    },
    // 医院结论，对应SQL中的主要受伤部位和医疗处置情况整合
    hospitalConclusion: {
      type: String,
      trim: true,
    },
    // 伤害部位，对应SQL中的principal_body_part
    injuryPart: {
      type: String,
      trim: true,
    },
    // 详情或影响范围，对应SQL中的Details or Influence
    detailsOrInfluence: {
      type: String,
      trim: true,
    },
    // 处理状态
    processingStatus: {
      type: String,
      enum: [ '待处理', '处理中', '已结案' ],
      default: '待处理',
    },
    // 员工状态
    employeeStatus: {
      type: String,
      enum: [ '治疗期', '康复期', '复工后工作受限', '复工' ],
      default: '治疗期',
    },
    // 详细地点，对应SQL中的detailed_location
    location: {
      type: String,
      trim: true,
    },
    // 发生地点类型，对应SQL中的occurence_locationtype
    locationType: {
      type: String,
      trim: true,
    },
    // 事故等级，对应SQL中的event_level
    injuryLevel: {
      type: String,
      enum: [ '轻微', '一般', '严重', '重大' ],
      default: '一般',
    },
    // 事故环节，对应SQL中的Event Process
    eventProcess: {
      type: String,
      trim: true,
    },
    // 事故原因，对应SQL中的event_cause
    injuryCause: {
      type: String,
      trim: true,
    },
    // 事故经过，对应SQL中的Event Details
    eventDetails: {
      type: String,
      trim: true,
    },
    // 分析过程，对应SQL中的Analysis Process
    analysisProcess: {
      type: String,
      trim: true,
    },
    // 承包商公司相关，对应SQL中的contractor_firm_related
    contractorRelated: {
      type: Boolean,
      default: false,
    },
    // 设备相关，对应SQL中的equipment_related
    equipmentRelated: {
      type: Boolean,
      default: false,
    },
    // 设备信息
    equipment: {
      name: String, // 设备名称，对应SQL中的equipment_name
      locationId: String, // 设备位号，对应SQL中的equipment_location_id
      category: String, // 设备种类，对应SQL中的equipment_category
      type: String, // 设备类型，对应SQL中的equipment_type
    },
    // 直接经济损失，对应SQL中的direct_economic_loss_amount_10k
    economicLoss: {
      amount: Number, // 损失金额（万元）
      currency: {
        type: String,
        default: 'CNY',
      }, // 货币类型，对应SQL中的direct_economic_loss_currency
    },
    // 人员影响统计
    personnelImpact: {
      firstAid: { type: Number, default: 0 }, // 急救人数，对应SQL中的Number of Personnel involved in First Aid
      medicalTreatment: { type: Number, default: 0 }, // 医疗处置人数，对应SQL中的Number of Personnel involved in Medical Treatment
      awayFromWork: { type: Number, default: 0 }, // 工伤离岗人数，对应SQL中的Number of Away From Work personnel
      jobRestriction: { type: Number, default: 0 }, // 工作受限人数，对应SQL中的Number of Job Restriction Personnel
      occupationalDisease: { type: Number, default: 0 }, // 职业病人数，对应SQL中的Number of Occupational Disease Personnel
      permanentDisability: { type: Number, default: 0 }, // 永久性伤残人数，对应SQL中的Number of Permanent Disability personnel
      death: { type: Number, default: 0 }, // 死亡人数，对应SQL中的Death Toll
    },
    // 判级维度
    criteriaAssessment: {
      firstAid: String, // 急救判级维度，对应SQL中的Criteria 1: First Aid
      medicalTreatment: String, // 医疗处置判级维度，对应SQL中的Criteria 2: Medical Treatment
      jobRestriction: String, // 工作受限判级维度，对应SQL中的Criteria 3: Job Restriction
      awayFromWork: String, // 工伤离岗判级维度，对应SQL中的Criteria 4: Away From Work/Lose Consciousness
      permanentDisability: String, // 永久性伤残判级维度，对应SQL中的Criteria 5: Permanent Disability
      occupationalDisease: String, // 职业病判级维度，对应SQL中的Criteria 6: Occupational Disease (Health Impact)
      death: String, // 死亡判级维度，对应SQL中的Criteria 7: Death
      reputationEffect: String, // 声誉影响判级维度，对应SQL中的Criteria 8: Reputation Effect
      economicLoss: String, // 经济损失判级维度，对应SQL中的Criteria 9: Direct Economic Loss
    },
    // 风险等级
    riskLevel: {
      consequence: String, // 风险等级潜在风险的后果，对应SQL中的Risk Level Consequence
      likelihood: String, // 风险等级潜在风险的可能性，对应SQL中的Risk Level Likelihood
    },
    // 案例等级
    caseLevel: {
      recommended: String, // 推荐事故等级，对应SQL中的Recommended Main Case Level
      adjusted: String, // 修正事故等级，对应SQL中的Adjusted Case Level
    },
    // 推荐主事故类型，对应SQL中的Recommended Main Event Type
    recommendedMainEventType: {
      type: String,
      trim: true,
    },
    // 工艺/非工艺相关事故，对应SQL中的PSM / Non PSM Associated Events?
    psmRelated: {
      type: String,
      trim: true,
    },
    // 需记录/需报告，对应SQL中的Record/Report Required?
    recordRequired: {
      type: String,
      trim: true,
    },
    // 事故网页链接，对应SQL中的Link
    eventLink: {
      type: String,
      trim: true,
    },
    // 相关的伤害&疾病案例ID，对应SQL中的Associated I&I Case ID(s)
    associatedCaseId: {
      type: Number,
    },
    // 报告信息
    reportInfo: {
      reportedTime: Date, // 上报日期及时间，对应SQL中的ReportedDatetime
      initiator: String, // 发起人，对应SQL中的Initiator
    },
    // 关闭信息
    closureInfo: {
      closureDate: Date, // 事故关闭日期，对应SQL中的EventClosureDate
      updatedBy: String, // 最后一次更新用户，对应SQL中的UpdateBy
      updatedDate: Date, // 最后更新时间，对应SQL中的UpdateDate
    },
    // 事故跟踪记录
    followUpRecords: [{
      _id: {
        type: String,
        default: shortid.generate,
      }, // 记录ID
      date: {
        type: Date,
        default: Date.now,
      }, // 跟踪日期
      department: String, // 跟踪部门
      person: String, // 跟踪人
      form: String, // 跟踪形式
      employeeStatus: {
        type: String,
        enum: [ '治疗期', '康复期', '复工后工作受限', '复工' ],
      }, // 员工状态
      employeeCondition: String, // 员工情况
      attachments: [{ // 附件
        name: String, // 文件名
        path: String, // 文件路径
        type: String, // 文件类型
        size: Number, // 文件大小
      }],
    }],
    // 记录创建时间
    createdTime: {
      type: Date,
      default: Date.now,
    },
    // 最后更新时间
    updatedTime: {
      type: Date,
      default: Date.now,
    },
  }, {
    timestamps: true, // 自动管理 createdAt 和 updatedAt
    versionKey: false, // 不需要 __v 字段
    collection: 'whIndustrialInjury', // 指定集合名称
    minimize: false, // 保存空对象
    strict: true, // 严格模式，不允许定义外的字段
    toObject: { virtuals: true },
    toJSON: { virtuals: true },
  });

  // 创建索引
  IndustrialInjurySchema.index({ injuryId: 1 }, { unique: true });
  IndustrialInjurySchema.index({ company: 1 });
  IndustrialInjurySchema.index({ department: 1 });
  IndustrialInjurySchema.index({ employeeStatus: 1 });
  IndustrialInjurySchema.index({ occurrenceTime: -1 });
  IndustrialInjurySchema.index({ employeeName: 1 });
  IndustrialInjurySchema.index({ eventType: 1 });
  IndustrialInjurySchema.index({ state: 1 });
  IndustrialInjurySchema.index({ 'reportInfo.reportedTime': -1 });
  IndustrialInjurySchema.index({ departmentId: 1 });

  // 统一权限过滤器，通过 departmentId -> encode 映射进行权限控制

  const ctx = app.createAnonymousContext();
  IndustrialInjurySchema.plugin(createWorkspacePermissionFilter(ctx, {
    filterField: 'departmentId', // 工伤表中的部门ID字段
    storageType: 'encode', // 通过视图 encode 字段映射到 fullId 进行权限匹配
  }));
  return mongoose.model('WhIndustrialInjury', IndustrialInjurySchema);
};
