const Controller = require('egg').Controller;
const shortid = require('shortid');
const { adminGroupRule } = require('@validate');

const _ = require('lodash');

class PolicyManageController extends Controller {
  // 获取人员列表
  // async getEmployeeInfo(ctx) {
  //   try {
  //     const { EnterpriseID } = ctx.session.adminUserInfo;
  //     const { searchName } = ctx.request.body;
  //     const res = await ctx.service.policyManage.getEmployeeInfo({
  //       EnterpriseID,
  //       searchName,
  //     });
  //     ctx.helper.renderSuccess(ctx, {
  //       status: 200,
  //       message: '获取成功',
  //       data: res,
  //     });
  //   } catch (error) {
  //     ctx.helper.renderCustom(ctx, {
  //       message: error,
  //       status: 500,
  //     });
  //   }
  // }

  // 判断当前用户是否是集团管理员（管理旗下所有企业）
  async getIsGroupAdmin(ctx) {
    try {
      const { _id: userId, EnterpriseID } = ctx.session.adminUserInfo;
      const res = await ctx.service.policyManage.getIsGroupAdmin({
        userId,
        EnterpriseID,
      });
      ctx.helper.renderSuccess(ctx, {
        status: 200,
        message: '获取成功',
        data: res,
      });
    } catch (error) {
      ctx.helper.renderCustom(ctx, {
        message: error,
        status: 500,
      });
    }
  }

  // 获取企业数据权限资源列表
  async getScopeEnterpriseList(ctx) {
    try {
      const { id = '' } = ctx.request.query;
      const policyResourceList =
        await ctx.service.policyManage.getScopeEnterpriseList(id);
      ctx.helper.renderSuccess(ctx, {
        data: policyResourceList,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  // 获取部门数据权限资源列表
  async getScopeDingtreeList(ctx) {
    try {
      const { id = '' } = ctx.request.query;
      const topEnterprise = await ctx.helper.getTopEnterpriseId();
      const policyResourceList =
        await ctx.service.policyManage.getScopeDingtreeList(topEnterprise, id);
      ctx.helper.renderSuccess(ctx, {
        data: policyResourceList,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  // 获取工作场所数据权限资源列表
  async getScopeWorkplaceList(ctx) {
    try {
      const { id = '', type, level } = ctx.request.query;
      const topEnterprise = await ctx.helper.getTopEnterpriseId();
      const policyResourceList =
        await ctx.service.policyManage.getScopeWorkplaceList(topEnterprise, id, type, level);
      ctx.helper.renderSuccess(ctx, {
        data: policyResourceList,
      });
    } catch (err) {
      ctx.logger.error('获取工作场所列表失败', err);
      ctx.helper.renderFail(ctx, {
        message: '获取工作场所列表失败',
      });
    }
  }

  // 获取部门数据及人员资源列表
  async getDingtreeAndUserList(ctx) {
    try {
      const { id = '' } = ctx.request.query;
      const topEnterprise = await ctx.helper.getTopEnterpriseId();
      const policyResourceList =
        await ctx.service.policyManage.getDingtreeAndUserList(
          topEnterprise,
          id
        );
      ctx.helper.renderSuccess(ctx, {
        data: policyResourceList,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  // 获取指定user列表
  async getUserList(ctx) {
    try {
      const { name = '' } = ctx.request.query;
      const policyResourceList = await ctx.service.policyManage.getUserList(
        name
      );
      ctx.helper.renderSuccess(ctx, {
        data: policyResourceList,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  // 获取指定企业列表
  async getEnterpriseList(ctx) {
    try {
      const { name = '' } = ctx.request.query;
      const policyResourceList =
        await ctx.service.policyManage.getEnterpriseList(name);
      ctx.helper.renderSuccess(ctx, {
        data: policyResourceList,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  // 获取指定部门列表
  async getDingtreeList(ctx) {
    try {
      const { name = '' } = ctx.request.query;
      const policyResourceList = await ctx.service.policyManage.getDingtreeList(
        name
      );
      ctx.helper.renderSuccess(ctx, {
        data: policyResourceList,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  // 获取指定工作场所列表
  async getWorkplaceList(ctx) {
    try {
      const { name = '' } = ctx.request.query;
      const policyResourceList =
        await ctx.service.policyManage.getWorkplaceList(name);
      ctx.helper.renderSuccess(ctx, {
        data: policyResourceList,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  // 获取角色列表
  async getAdminGroupList(ctx) {
    try {
      const adminGroupList = await ctx.service.policyManage.getAdminGroupList();
      ctx.helper.renderSuccess(ctx, {
        data: adminGroupList,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  // 创建子管理员
  async createPolicy(ctx) {
    try {
      const { body } = ctx.request;
      const res = await ctx.service.policyManage.createPolicy({
        ...body,
      });
      ctx.helper.renderSuccess(ctx, {
        status: 200,
        message: '创建成功',
        data: res,
      });
    } catch (error) {
      ctx.helper.renderCustom(ctx, {
        message: error,
        status: 500,
      });
    }
  }

  // 获取子管理员列表
  async getPolicyList(ctx) {
    try {
      const query = ctx.request.query;
      query.current = query.current ? Number(query.current) : 1;
      query.pageSize = query.pageSize ? Number(query.pageSize) : 10;
      const { topEnterpriseID } = ctx.session.adminUserInfo;
      query.topEnterpriseID = topEnterpriseID;
      const res = await ctx.service.policyManage.getPolicyList(query);
      ctx.helper.renderSuccess(ctx, {
        status: 200,
        message: '获取成功',
        data: res,
      });
    } catch (error) {
      ctx.helper.renderCustom(ctx, {
        message: error,
        status: 500,
      });
    }
  }

  // 获取指定子管理员信息
  async getPolicyInfo(ctx) {
    try {
      const { id } = ctx.request.query;
      const res = await ctx.service.policyManage.getPolicyInfo(id);
      ctx.helper.renderSuccess(ctx, {
        status: 200,
        message: '获取成功',
        data: res,
      });
    } catch (error) {
      ctx.helper.renderCustom(ctx, {
        message: error,
        status: 500,
      });
    }
  }

  // 编辑子管理员
  async updatePolicy(ctx) {
    try {
      const { body } = ctx.request;
      const res = await ctx.service.policyManage.updatePolicy({
        ...body,
      });
      ctx.helper.renderSuccess(ctx, {
        status: 200,
        message: '编辑成功',
        data: res,
      });
    } catch (error) {
      ctx.helper.renderCustom(ctx, {
        message: error,
        status: 500,
      });
    }
  }

  // 获取当前角色是否是超级管理员
  async getIsSuper(ctx) {
    try {
      const { id } = ctx.request.query;
      const res = await ctx.service.policyManage.getIsSuper(id);
      ctx.helper.renderSuccess(ctx, {
        status: 200,
        message: '获取成功',
        data: res,
      });
    } catch (error) {
      ctx.helper.renderCustom(ctx, {
        message: error,
        status: 500,
      });
    }
  }

  // 获取当前用户是否是超级管理员
  async getIsSuperAdmin(ctx) {
    try {
      const userId = ctx.session.adminUserInfo._id;
      const res = await ctx.helper.getIsSuperAdmin(userId);
      ctx.helper.renderSuccess(ctx, {
        status: 200,
        message: '获取成功',
        data: res,
      });
    } catch (error) {
      ctx.helper.renderCustom(ctx, {
        message: error,
        status: 500,
      });
    }
  }

  // 获取超级管理员用户
  async getSuperAdminUser(ctx) {
    try {
      const { topEnterpriseID } = ctx.session.adminUserInfo;
      const res = await ctx.service.policyManage.getSuperAdminUser({ topEnterpriseID });
      ctx.helper.renderSuccess(ctx, {
        status: 200,
        message: '获取成功',
        data: res,
      });
    } catch (error) {
      ctx.helper.renderCustom(ctx, {
        message: error,
        status: 500,
      });
    }
  }

  // 新增企业角色
  async addQyGroup(ctx) {
    const { service } = this;
    try {
      const fields = ctx.request.body || {};
      const { topEnterpriseID } = ctx.session.adminUserInfo;

      const formObj = {
        _id: fields._id || shortid.generate(), // 测试用
        name: fields.name,
        comments: fields.comments,
        enable: fields.enable,
        topEnterpriseID,
      };

      ctx.validate(adminGroupRule.form(ctx), formObj);

      await service.policyManage.addQyGroup(formObj);

      ctx.helper.renderSuccess(ctx);
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  // 获取企业角色列表
  async getQyGroupList(ctx) {
    const { service } = this;
    try {
      const query = ctx.query;
      const { topEnterpriseID } = ctx.session.adminUserInfo;
      query.topEnterpriseID = topEnterpriseID;
      const adminGroupList = await service.policyManage.getQyGroupList({}, { query });

      ctx.helper.renderSuccess(ctx, {
        data: adminGroupList,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  // 删除企业角色
  async deleteQyGroup(ctx) {
    const { service } = this;
    try {
      const targetIds = ctx.query.ids;
      await service.policyManage.deleteQyGroup(ctx, targetIds);
      ctx.helper.renderSuccess(ctx);
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  // 更新企业角色
  async updateOneQyGroup(ctx) {
    const { service } = this;
    try {
      const fields = ctx.request.body || {};
      const { topEnterpriseID } = ctx.session.adminUserInfo;
      const formObj = {
        name: fields.name,
        comments: fields.comments,
        power: fields.power,
        topEnterpriseID,
      };

      ctx.validate(adminGroupRule.form(ctx), formObj);

      await service.policyManage.updateOneQyGroup(ctx, fields._id, formObj);

      ctx.helper.renderSuccess(ctx);
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  // 获取企业资源列表
  async getQyResourceList(ctx) {
    const { service } = this;
    try {
      const payload = ctx.query;
      _.assign(payload, {
        isPaging: '0',
      });
      const qyResourceList = await service.policyManage.getQyResourceList(payload);

      ctx.helper.renderSuccess(ctx, {
        data: qyResourceList,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  // 获取单个企业角色
  async getOneQyGroup(ctx) {
    const { service } = this;
    try {
      const _id = ctx.query.id;

      const targetUser = await service.policyManage.getOneQyGroup(ctx, {
        query: {
          _id,
        },
      });

      ctx.helper.renderSuccess(ctx, {
        data: targetUser,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  // 删除子管理员
  async deletePolicy(ctx) {
    try {
      const { id } = ctx.request.body;
      const res = await ctx.service.policyManage.deletePolicy(id);
      ctx.helper.renderSuccess(ctx, {
        status: 200,
        message: '删除成功',
        data: res,
      });
    } catch (error) {
      ctx.helper.renderCustom(ctx, {
        message: error,
        status: 500,
      });
    }
  }
}

module.exports = PolicyManageController;
