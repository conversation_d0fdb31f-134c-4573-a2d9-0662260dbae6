const shortid = require('shortid');
const _ = require('lodash');
const apiResourceRule = require('../../validate/apiResource');
const { siteFunc } = require('@utils');

const ApiResourceController = {
  async getResourceList(ctx) {
    const { service } = ctx;
    try {
      const params = ctx.query,
        _id = params._id || '';
      const files = 'api _id label enable routePath parentId type comments';
      const payload = {
        pageSize: 1000,
      };
      const adminGroup = await service.adminGroup.item(ctx, {
        query: {
          _id,
        },
        files: { _id: 0, apiPower: 1 },
      });
      const apiPower = adminGroup.apiPower;
      const manageCates = await service.apiResource.find(payload, {
        query: { _id: apiPower, enable: true },
        files,
      });
      _.assign(payload, {
        isPaging: '0',
      });
      const manageCatesAll = await service.apiResource.find(payload, {
        query: { enable: true },
        files,
      });
      const currentCates = await siteFunc.renderNoPowerAPI(
        manageCatesAll,
        apiPower
      );
      manageCates.docs.push(...currentCates);
      ctx.helper.renderSuccess(ctx, {
        data: manageCates,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  async list(ctx) {
    const { service } = ctx;
    try {
      const payload = ctx.query;
      _.assign(payload, {
        pageSize: 1000,
      });
      const apiResourceList = await service.apiResource.find(payload);

      ctx.helper.renderSuccess(ctx, {
        data: apiResourceList,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  async alllist(ctx) {
    return await ctx.service.apiResource.find(
      {
        isPaging: '0',
      },
      {
        type: '1',
      }
    );
  },

  async create(ctx) {
    const { service } = ctx;
    try {
      const fields = ctx.request.body || {};
      const formObj = {
        _id: fields._id || shortid.generate(), // 测试用
        label: fields.label,
        type: fields.type,
        api: fields.api,
        parentId: fields.parentId,
        sortId: fields.sortId,
        routePath: fields.routePath,
        icon: fields.icon,
        componentPath: fields.componentPath,
        enable: fields.enable,
        comments: fields.comments,
      };

      ctx.validate(apiResourceRule.form(ctx), formObj);

      if (fields.type === '0' && !fields.label) {
        formObj.label = fields.routePath;
      } else if (fields.type === '1' && !fields.label) {
        formObj.label = fields.api;
      }

      const oldResource = await service.apiResource.item(ctx, {
        query: {
          label: formObj.label,
        },
      });
      if (!_.isEmpty(oldResource)) {
        throw new Error(
          ctx.__('user_action_tips_repeat', [ ctx.__('label_resourceName') ])
        );
      }

      await service.apiResource.create(formObj);

      ctx.helper.renderSuccess(ctx);
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  async getOne(ctx) {
    const { service } = ctx;
    try {
      const _id = ctx.query.id;

      const targetItem = await service.apiResource.item(ctx, {
        query: {
          _id,
        },
      });

      ctx.helper.renderSuccess(ctx, {
        data: targetItem,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  async update(ctx) {
    const { service } = ctx;
    try {
      const fields = ctx.request.body || {};
      const formObj = {
        label: fields.label,
        type: fields.type,
        api: fields.api,
        parentId: fields.parentId,
        sortId: fields.sortId,
        routePath: fields.routePath,
        icon: fields.icon,
        componentPath: fields.componentPath,
        enable: fields.enable,
        comments: fields.comments,
      };

      ctx.validate(apiResourceRule.form(ctx), formObj);

      if (fields.type === '0' && fields.routePath) {
        formObj.label = fields.routePath;
      } else if (fields.type === '1') {
        formObj.label = fields.api;
      }

      const oldResource = await service.apiResource.item(ctx, {
        query: {
          label: formObj.label,
        },
      });

      if (!_.isEmpty(oldResource) && oldResource._id !== fields._id) {
        throw new Error(
          ctx.__('user_action_tips_repeat', [ ctx.__('label_resourceName') ])
        );
      }

      await service.apiResource.update(ctx, fields._id, formObj);
      ctx.helper.renderSuccess(ctx);
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  async updateParentId(ctx) {
    const { service } = ctx;

    try {
      const fields = ctx.request.body || {};

      const formObj = {
        parentId: fields.parentId,
      };

      const oldResource = await service.apiResource.item(ctx, {
        query: {
          label: fields.label,
        },
      });

      if (!_.isEmpty(oldResource) && oldResource._id !== fields._id) {
        throw new Error(
          ctx.__('user_action_tips_repeat', [ ctx.__('label_resourceName') ])
        );
      }

      await service.apiResource.update(ctx, fields._id, formObj);
      ctx.helper.renderSuccess(ctx);
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  async removes(ctx) {
    const { service } = ctx;
    try {
      const targetIds = ctx.query.ids;

      const oldResource = await service.apiResource.item(ctx, {
        query: { _id: targetIds },
        files: { _id: 1, parentId: 1 },
      });
      if (oldResource.parentId === '0') {
        const cResources = await service.apiResource.find(
          { isPaging: '0' },
          {
            query: { parentId: oldResource._id },
            files: { _id: 1, parentId: 1 },
          }
        );
        await service.apiResource.removes(ctx, oldResource._id);
        for (let i = 0; i < cResources.length; i++) {
          const cResourceId = cResources[i]._id;
          // 删除主类
          await service.apiResource.removes(ctx, cResourceId);
          // 删除子类
          await service.apiResource.removes(ctx, cResourceId, 'parentId');
        }
      } else {
        // 删除主类
        await service.apiResource.removes(ctx, oldResource._id);
        // 删除子类
        await service.apiResource.removes(ctx, oldResource._id, 'parentId');
      }
      ctx.helper.renderSuccess(ctx);
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },
};

module.exports = ApiResourceController;
