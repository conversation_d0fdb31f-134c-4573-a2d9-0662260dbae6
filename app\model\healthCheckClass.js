/**
 * 体检项目分类
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  const HealthCheckClassSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    name: String, // 分类名称
  }, { timestamps: true });

  return mongoose.model('HealthCheckClass', HealthCheckClassSchema, 'healthCheckClass');
};
