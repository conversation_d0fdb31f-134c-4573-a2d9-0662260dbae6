/**
 * @file 万华商城商品控制器
 * @description 万华商城商品相关接口
 * @createDate 2025-03-14
 */

const Controller = require('egg').Controller;

/**
 * @controller 万华商城商品管理
 */
class WhGoodsController extends Controller {
  /**
   * @summary 获取商品列表
   * @description 获取万华商城商品列表，支持分页和条件筛选
   * @return {Promise<void>} 无返回值
   */
  async getGoodsList() {
    const { ctx, service } = this;
    try {
      // 获取查询参数
      const params = ctx.query;
      const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
      // 获取商品列表
      const result = await service.manage.whGoodsService.getGoodsList({
        ...params,
        EnterpriseID,
      });

      // 记录操作日志
      ctx.auditLog('查询万华商城商品列表', `查询条件: ${JSON.stringify(params)}`, 'info');

      // 返回结果
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      // 记录错误日志
      ctx.logger.error('获取商品列表失败:', err);

      // 返回错误信息
      ctx.helper.renderFail(ctx, {
        message: err.message || '获取商品列表失败',
      });
    }
  }

  /**
   * @summary 绑定商品与防护用品库存
   * @description 将商品与防护用品库存进行绑定
   * @return {Promise<void>} 无返回值
   */
  async bindInventory() {
    const { ctx, service } = this;
    try {
      // 获取商品ID和库存ID
      const { goodsId, inventoryId } = ctx.request.body;

      // 参数校验
      ctx.validate({
        goodsId: { type: 'string', required: true },
        inventoryId: { type: 'string', required: true },
      }, ctx.request.body);

      // 绑定库存
      const result = await service.manage.whGoodsService.bindInventory(goodsId, inventoryId);

      // 记录操作日志
      ctx.auditLog('绑定商品与防护用品库存', `商品ID: ${goodsId}, 库存ID: ${inventoryId}`, 'info');

      // 返回结果
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '绑定库存成功',
      });
    } catch (err) {
      // 记录错误日志
      ctx.logger.error('绑定库存失败:', err);

      // 返回错误信息
      ctx.helper.renderFail(ctx, {
        message: err.message || '绑定库存失败',
      });
    }
  }

  /**
   * @summary 解除商品与防护用品库存的绑定
   * @description 解除商品与防护用品库存的绑定关系
   * @return {Promise<void>} 无返回值
   */
  async unbindInventory() {
    const { ctx, service } = this;
    try {
      // 获取商品ID
      const { goodsId } = ctx.request.body;

      // 参数校验
      ctx.validate({
        goodsId: { type: 'string', required: true },
      }, ctx.request.body);

      // 解除绑定
      const result = await service.manage.whGoodsService.unbindInventory(goodsId);

      // 记录操作日志
      ctx.auditLog('解除商品与防护用品库存的绑定', `商品ID: ${goodsId}`, 'info');

      // 返回结果
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '解除绑定成功',
      });
    } catch (err) {
      // 记录错误日志
      ctx.logger.error('解除绑定失败:', err);

      // 返回错误信息
      ctx.helper.renderFail(ctx, {
        message: err.message || '解除绑定失败',
      });
    }
  }

  /**
   * @summary 获取商品分类列表
   * @description 获取万华商城商品分类列表
   * @return {Promise<void>} 无返回值
   */
  async getCategoryList() {
    const { ctx, service } = this;
    try {
      // 获取分类列表
      const result = await service.manage.whGoodsService.getCategoryList();

      // 记录操作日志
      ctx.auditLog('查询万华商城商品分类列表', '', 'info');

      // 返回结果
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      // 记录错误日志
      ctx.logger.error('获取商品分类列表失败:', err);

      // 返回错误信息
      ctx.helper.renderFail(ctx, {
        message: err.message || '获取商品分类列表失败',
      });
    }
  }

  /**
   * @summary 获取防护用品库存列表
   * @description 获取可用于绑定的防护用品库存列表
   * @return {Promise<void>} 无返回值
   */
  async getInventoryList() {
    const { ctx, service } = this;
    try {
      // 获取查询参数
      const params = ctx.query;

      // 获取库存列表
      const result = await service.manage.whGoodsService.getInventoryList(params);

      // 记录操作日志
      ctx.auditLog('查询防护用品库存列表', `查询条件: ${JSON.stringify(params)}`, 'info');

      // 返回结果
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '获取防护用品库存列表成功',
      });
    } catch (err) {
      // 记录错误日志
      ctx.logger.error('获取防护用品库存列表失败:', err);

      // 返回错误信息
      ctx.helper.renderFail(ctx, {
        message: err.message || '获取防护用品库存列表失败',
      });
    }
  }

  /**
   * @summary 获取商品详情
   * @description 获取万华商城商品详情信息
   * @return {Promise<void>} 无返回值
   */
  async getGoodsDetail() {
    const { ctx, service } = this;
    try {
      // 获取商品ID
      const { id } = ctx.query;
      if (!id) {
        throw new Error('缺少商品ID');
      }

      // 获取商品详情
      const result = await service.manage.whGoodsService.getGoodsDetail(id);
      if (!result) {
        throw new Error('商品不存在');
      }

      // 记录操作日志
      ctx.auditLog('查询万华商城商品详情', `商品ID: ${id}`, 'info');

      // 返回结果
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      // 记录错误日志
      ctx.logger.error('获取商品详情失败:', err);

      // 返回错误信息
      ctx.helper.renderFail(ctx, {
        message: err.message || '获取商品详情失败',
      });
    }
  }

  /**
   * @summary 获取防护用品分类列表
   * @description 获取防护用品分类列表，用于创建防护用品时选择分类
   * @return {Promise<void>} 无返回值
   */
  async getInventoryCategoryList() {
    const { ctx, service } = this;
    try {
      // 获取企业ID
      const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';

      // 获取防护用品分类列表
      const result = await service.manage.whGoodsService.getInventoryCategoryList(EnterpriseID);

      // 记录操作日志
      ctx.auditLog('查询防护用品分类列表', '', 'info');

      // 返回结果
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      // 记录错误日志
      ctx.logger.error('获取防护用品分类列表失败:', err);

      // 返回错误信息
      ctx.helper.renderFail(ctx, {
        message: err.message || '获取防护用品分类列表失败',
      });
    }
  }

  /**
   * @summary 创建防护用品并绑定到商品
   * @description 创建新的防护用品记录并绑定到商品
   * @return {Promise<void>} 无返回值
   */
  async createAndBindInventory() {
    const { ctx, service } = this;
    try {
      // 获取商品ID和防护用品数据
      const { goodsId, materialCode, product, productSpec, categoryId, protectionType, function: functionValue, modelNumber, surplus, syncSpecToGoods } = ctx.request.body;

      // 记录请求参数，用于调试
      ctx.logger.info('创建并绑定防护用品请求参数:', ctx.request.body);

      // 参数校验
      ctx.validate({
        goodsId: { type: 'string', required: true },
        materialCode: { type: 'string', required: true },
        product: { type: 'string', required: true },
        productSpec: { type: 'string', required: true },
        categoryId: { type: 'string', required: false },
        protectionType: { type: 'string', required: false },
        function: { type: 'string', required: false },
        modelNumber: { type: 'string', required: false },
        surplus: { type: 'number', required: true, min: 0 },
        syncSpecToGoods: { type: 'boolean', required: false },
      }, ctx.request.body);

      // 构建防护用品数据
      const inventoryData = {
        materialCode,
        product,
        productSpec,
        categoryId,
        protectionType,
        function: functionValue,
        modelNumber,
        surplus,
      };

      // 创建防护用品并绑定
      const result = await service.manage.whGoodsService.createAndBindInventory(goodsId, inventoryData, syncSpecToGoods);

      // 记录操作日志
      ctx.auditLog('创建防护用品并绑定到商品', `商品ID: ${goodsId}, 防护用品数据: ${JSON.stringify(inventoryData)}`, 'info');

      // 返回结果
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '创建并绑定成功',
      });
    } catch (err) {
      // 记录错误日志
      ctx.logger.error('创建并绑定失败:', err);

      // 返回错误信息
      ctx.helper.renderFail(ctx, {
        message: err.message || '创建并绑定失败',
      });
    }
  }

  /**
   * @summary 创建商品
   * @description 创建新商品
   * @return {Promise<void>} 无返回值
   */
  async createGoods() {
    const { ctx, service } = this;
    try {
      // 获取请求数据
      const goodsData = ctx.request.body;

      // 获取当前管理员信息
      const adminInfo = ctx.session.adminUserInfo || {};

      // 添加企业ID
      if (adminInfo.EnterpriseID) {
        goodsData.EnterpriseID = adminInfo.EnterpriseID;
      }

      // 添加创建人信息
      goodsData.createBy = adminInfo.userName || 'system';

      // 参数校验
      ctx.validate({
        sku: { type: 'string', required: true, max: 60 },
        goodsName: { type: 'string', required: true, max: 300 },
        goodsUnit: { type: 'string', required: false, max: 500 },
        categoryCode: { type: 'string', required: false },
        categoryName: { type: 'string', required: false },
        departmentCode: { type: 'string', required: false },
        departName: { type: 'string', required: false },
        price: { type: 'number', required: true, min: 0 },
        goodsTaxRate: { type: 'number', required: false, min: 0, max: 100 },
        stockQuantity: { type: 'number', required: false, min: 0 },
      }, goodsData);

      // 创建商品
      const result = await service.manage.whGoodsService.createGoods(goodsData);

      // 记录操作日志
      ctx.auditLog('创建商品', `SKU: ${goodsData.sku}, 商品名称: ${goodsData.goodsName}`, 'info');

      // 返回结果
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '创建商品成功',
      });
    } catch (err) {
      // 记录错误日志
      ctx.logger.error('创建商品失败:', err);

      // 返回错误信息
      ctx.helper.renderFail(ctx, {
        message: err.message || '创建商品失败',
      });
    }
  }

  /**
   * @summary 更新商品
   * @description 更新商品信息
   * @return {Promise<void>} 无返回值
   */
  async updateGoods() {
    const { ctx, service } = this;
    try {
      // 获取商品ID和更新数据
      const id = ctx.params.id;
      const goodsData = ctx.request.body;

      // 获取当前管理员信息
      const adminInfo = ctx.session.adminUserInfo || {};

      // 添加更新人信息
      goodsData.updateBy = adminInfo.userName || 'system';

      // 参数校验
      ctx.validate({
        sku: { type: 'string', required: false, max: 60 },
        goodsName: { type: 'string', required: false, max: 300 },
        goodsUnit: { type: 'string', required: false, max: 500 },
        categoryCode: { type: 'string', required: false },
        categoryName: { type: 'string', required: false },
        departmentCode: { type: 'string', required: false },
        departName: { type: 'string', required: false },
        price: { type: 'number', required: false, min: 0 },
        goodsTaxRate: { type: 'number', required: false, min: 0, max: 100 },
        stockQuantity: { type: 'number', required: false, min: 0 },
      }, goodsData);

      // 更新商品
      const result = await service.manage.whGoodsService.updateGoods(id, goodsData);

      // 记录操作日志
      ctx.auditLog('更新商品', `商品ID: ${id}`, 'info');

      // 返回结果
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '更新商品成功',
      });
    } catch (err) {
      // 记录错误日志
      ctx.logger.error('更新商品失败:', err);

      // 返回错误信息
      ctx.helper.renderFail(ctx, {
        message: err.message || '更新商品失败',
      });
    }
  }
}

module.exports = WhGoodsController;
