/**
 * @file Todo控制器
 * @description 待办事项的API接口
 */
const Controller = require('egg').Controller;

class TodoController extends Controller {
  /**
   * @summary 创建待办事项
   * @description 创建一个新的待办事项
   * @return {Promise<void>} 无返回值
   */
  async create() {
    const { ctx } = this;
    try {
      const payload = ctx.request.body;
      // 参数校验
      ctx.validate({
        title: { type: 'string', required: true, max: 100 },
        content: { type: 'string', required: false, max: 500 },
        dueDate: { type: 'string', required: false, format: /^\d{4}-\d{2}-\d{2}( \d{2}:\d{2}:\d{2})?$/ },
      }, payload);

      const result = await ctx.service.api.todo.create(payload);
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '创建成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '创建失败',
      });
    }
  }

  /**
   * @summary 获取待办事项列表
   * @description 获取所有未删除的待办事项列表
   * @return {Promise<void>} 无返回值
   */
  async list() {
    const { ctx } = this;
    try {
      const { completed, keyword } = ctx.query;
      const query = {};
      // 根据完成状态筛选
      if (completed !== undefined) {
        query.completed = completed === 'true';
      }

      // 根据关键词搜索标题
      if (keyword) {
        query.title = new RegExp(keyword, 'i');
      }

      const result = await ctx.service.api.todo.list(query);
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '获取成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '获取失败',
      });
    }
  }

  /**
   * @summary 获取待办事项详情
   * @description 根据ID获取待办事项详情
   * @return {Promise<void>} 无返回值
   */
  async get() {
    const { ctx } = this;
    try {
      const { id } = ctx.params;
      const result = await ctx.service.api.todo.get(id);
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '获取成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '获取失败',
      });
    }
  }

  /**
   * @summary 更新待办事项
   * @description 根据ID更新待办事项信息
   * @return {Promise<void>} 无返回值
   */
  async update() {
    const { ctx } = this;
    try {
      const { id } = ctx.params;
      const payload = ctx.request.body;

      const result = await ctx.service.api.todo.update(id, payload);
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '更新成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '更新失败',
      });
    }
  }

  /**
   * @summary 删除待办事项
   * @description 根据ID删除待办事项（软删除）
   * @return {Promise<void>} 无返回值
   */
  async delete() {
    const { ctx } = this;
    try {
      const { id } = ctx.params;
      const result = await ctx.service.api.todo.delete(id);
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '删除成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '删除失败',
      });
    }
  }
}

module.exports = TodoController;
