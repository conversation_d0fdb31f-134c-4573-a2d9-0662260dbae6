
module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const conn = mongoose.createConnection(app.config.mongoose.tools.url, app.config.mongoose.tools.options);
  // const conn = app.mongooseDB.get('tools'); // 需要在配置文件中使用mongoose.clients.tools: {} 配置，现配置中间缺少一层clients

  const touchLimitsSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },

    initial: String,
    chineseName: String,
    englishName: String,
    casNum: String,
    OELs: {
      MAC: String,
      PC_TWA: String,
      PC_STEL: String,
    },
    healthEffect: String,
    note: String,
    seriousHarm: Boolean, // 是否为严重危害因素
  });

  return conn.model('TouchLimits', touchLimitsSchema, 'touchLimits');
  // return mongoose.model('TouchLimits', touchLimitsSchema, 'touchLimits');

};
