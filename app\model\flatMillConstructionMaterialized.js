/**
 * @file 扁平化工厂结构物化视图模型
 * @description 将嵌套的工厂结构展平为单层物化视图，包含 mill、workspaces 和 stations 三个层级
 * <AUTHOR>
 * @createDate 2025-05-22
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');
  const ctx = app.createAnonymousContext();
  const { createWorkspacePermissionFilter } = require('../utils/workspacePermissionFilter');


  // 扁平化工厂结构物化视图的 Schema 定义
  const FlatMillConstructionMaterializedSchema = new Schema(
    {
      _id: {
        type: String,
        default: shortid.generate,
      },
      name: {
        type: String,
        required: true,
        trim: true,
      },
      unitCode: {
        type: String,
        trim: true,
      },
      encode: {
        type: Number,
      },
      level: { // 表示层级关系
        type: String,
        enum: [ 'mill', 'workspaces', 'stations' ],
        required: true,
      },
      category: String, // 业务层面分类
      fullId: {
        type: String,
        required: true,
        unique: true,
      },
      // 新增字段：支持层级查询
      parentId: {
        type: String,
        index: true,
      },
      EnterpriseID: {
        type: String,
        required: true,
        index: true,
      },
      // 人员列表
      employees: [],
      // 新增字段：层级人员总数
      totalEmployeeCount: {
        type: Number,
        default: 0,
      },
      state: {
        type: String,
        default: '1',
        enum: [ '0', '1' ],
      },
    }, {
      timestamps: true, // 自动添加 createdAt 和 updatedAt 字段
      versionKey: false, // 禁用 __v 字段
      collection: 'flatMillConstructionMaterialized', // 指向集合名称
      strict: true, // 严格模式，只允许 Schema 中定义的字段
      read: 'secondaryPreferred', // 优先从从节点读取，减轻主节点负担
    });

  // 创建索引以提高查询性能
  FlatMillConstructionMaterializedSchema.index({ level: 1 });
  FlatMillConstructionMaterializedSchema.index({ fullId: 1 }, { unique: true });
  FlatMillConstructionMaterializedSchema.index({ unitCode: 1 });
  // 在物化集合上添加索引
  FlatMillConstructionMaterializedSchema.index({ encode: 1 });
  FlatMillConstructionMaterializedSchema.index({ parentId: 1 });
  FlatMillConstructionMaterializedSchema.index({ EnterpriseID: 1 });

  // 复合索引优化
  FlatMillConstructionMaterializedSchema.index({ EnterpriseID: 1, level: 1 });
  FlatMillConstructionMaterializedSchema.index({ parentId: 1, level: 1 });
  FlatMillConstructionMaterializedSchema.index({ EnterpriseID: 1, parentId: 1 });

  // 修改默认查询，始终排除已删除的记录
  FlatMillConstructionMaterializedSchema.pre('find', function() {
    if (!this.getQuery().state) {
      this.where({ state: '1' }); // 默认只查询状态为 '1' 的记录
    }
  });
  const permissionFilter = createWorkspacePermissionFilter(ctx, {
    filterField: '_id',
    storageType: 'nodeId',
  });
  FlatMillConstructionMaterializedSchema.plugin(permissionFilter);
  // 返回模型
  return mongoose.model('FlatMillConstructionMaterialized', FlatMillConstructionMaterializedSchema);
};
