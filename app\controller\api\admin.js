/*
 * @Author: doramart
 * @Date: 2019-07-07 13:07:27
 * @Last Modified by: mikey.z<PERSON><PERSON>g
 * @Last Modified time: 2021-08-18 15:35:57
 */
const Controller = require('egg').Controller;
const jwt = require('jsonwebtoken');
const {
  authToken,
} = require('@utils');

class SystemConfigController extends Controller {
  // 陕西焦煤统一登录
  async sxccSignIn() {
    const { ctx, app } = this;
    const { code } = ctx.query;
    console.log('code', code);
    if (!code) {
      ctx.body = {
        appid: 'wsjk', // 回发给mid系统
        code: 'E', // "S或E，S代表成功，E代表失败",
        desc: '缺少统一身份认证编码', // "成功反馈'接收成功'，失败时反馈错误描述",
        redirect: '', // 登录成功后，第三方系统跳转地址
      };
      return;
    }

    // 查询统一身份认证编码是否在系统中存在
    // const employee = await ctx.model.Employee.findOne({ unitCode: code });
    const employee = await ctx.service.db.findOne('Employee', { unitCode: code });
    if (!employee) {
      ctx.body = {
        appid: 'wsjk', // 回发给mid系统
        code: 'E', // "S或E，S代表成功，E代表失败",
        desc: '系统不存在该人员', // "成功反馈'接收成功'，失败时反馈错误描述",
        redirect: '', // 登录成功后，第三方系统跳转地址
      };
      return;
    }

    // 查询是否存在user以及adminuser
    const adminUser = await ctx.service.adminUser.findAdminUser(employee._id);
    if (!adminUser) {
      ctx.body = {
        appid: 'wsjk', // 回发给mid系统
        code: 'E', // "S或E，S代表成功，E代表失败",
        desc: '系统缺少该用户', // "成功反馈'接收成功'，失败时反馈错误描述",
        redirect: '', // 登录成功后，第三方系统跳转地址
      };
      return;
    }

    // const targetOrg = await ctx.model.Adminorg.findOne({
    const targetOrg = await ctx.service.db.findOne(
      'Adminorg',
      {
        $or: [{ adminUserId: adminUser._id }, { adminArray: { $all: [ adminUser._id ] } }],
      }
    );
    const adminUserToken = jwt.sign(
      {
        _id: adminUser._id,
        EnterpriseID: targetOrg._id,
        isTrialUser: false, // 试用账号
      },
      this.app.config.encrypt_key,
      {
        expiresIn: '30day',
      }
    );
    const toUrl = `${app.config.domainNames.enterprise_int}/api/ssoLogin?token=${adminUserToken}`;
    console.log('toUrl', toUrl);

    ctx.body = {
      appid: 'wsjk', // 回发给mid系统
      code: 'S', // "S或E，S代表成功，E代表失败",
      desc: '成功', // "成功反馈'接收成功'，失败时反馈错误描述",
      redirect: toUrl, // 登录成功后，第三方系统跳转地址
    };
  }
  async ssoLogin() {
    const { ctx, config, app } = this;
    try {
      const { token } = ctx.query;
      if (!token) {
        ctx.redirect('/admin/login');
      }
      const checkToken = await authToken.checkToken(token, app.config.encrypt_key);
      if (!checkToken) {
        ctx.redirect(`${ctx.session.basePath}/login`);
      }

      ctx.cookies.set(
        'admin_' + config.auth_cookie_name,
        token,
        {
          path: '/',
          maxAge: config.adminUserMaxAge,
          signed: true,
          httpOnly: false,
        }
      );

      ctx.redirect('/admin/dashboard');
    } catch (e) {
      ctx.auditLog('企业端单点登录', `登录失败：${JSON.stringify(e)}`, 'error');
      ctx.redirect(`${ctx.session.basePath}/login`);
    }
  }

  // 测试数据权限
  async testDataPermission(ctx) {
    try {
      const { key, model, fun, value, authCheck = true } = ctx.query;
      if (fun === 'aggregate') {
        const res = await ctx.service.db[fun](model, [{
          $match: { [key]: value },
        }], { authCheck });
        ctx.helper.renderSuccess(ctx, {
          data: res,
        });
      } else {
        const res = await ctx.service.db[fun](model, { [key]: value });
        ctx.helper.renderSuccess(ctx, {
          data: res,
        });
      }
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
}

module.exports = SystemConfigController;
