videojs.addLanguage('pt-BR', {
  "Audio Player": "Reprodutor de áudio",
  "Video Player": "Reprodutor de vídeo",
  "Play": "Tocar",
  "Pause": "Pausar",
  "Replay": "Tocar novamente",
  "Current Time": "Tempo",
  "Duration": "Duração",
  "Remaining Time": "Tempo Restante",
  "Stream Type": "Tipo de Stream",
  "LIVE": "AO VIVO",
  "Loaded": "Carregado",
  "Progress": "Progresso",
  "Progress Bar": "Barra de progresso",
  "progress bar timing: currentTime={1} duration={2}": "{1} de {2}",
  "Fullscreen": "Tela Cheia",
  "Non-Fullscreen": "Tela Normal",
  "Mute": "Mudo",
  "Unmute": "Habilitar Som",
  "Playback Rate": "Velocidade",
  "Subtitles": "Legendas",
  "subtitles off": "Sem Legendas",
  "Captions": "Anotações",
  "captions off": "Sem Anotações",
  "Chapters": "Capítulos",
  "Descriptions": "Descrições",
  "descriptions off": "sem descrições",
  "Audio Track": "Faixa de áudio",
  "Volume Level": "Nível de volume",
  "You aborted the media playback": "Você parou a execução do vídeo.",
  "A network error caused the media download to fail part-way.": "Um erro na rede causou falha durante o download da mídia.",
  "The media could not be loaded, either because the server or network failed or because the format is not supported.": "A mídia não pode ser carregada, por uma falha de rede ou servidor ou o formato não é suportado.",
  "No compatible source was found for this media.": "Nenhuma fonte foi encontrada para esta mídia.",
  "The media playback was aborted due to a corruption problem or because the media used features your browser did not support.": "A reprodução foi interrompida devido à um problema de mídia corrompida ou porque a mídia utiliza funções que seu navegador não suporta.",
  "The media is encrypted and we do not have the keys to decrypt it.": "A mídia está criptografada e não temos as chaves para descriptografar.",
  "Play Video": "Tocar Vídeo",
  "Close": "Fechar",
  "Close Modal Dialog": "Fechar Diálogo Modal",
  "Modal Window": "Janela Modal",
  "This is a modal window": "Isso é uma janela-modal",
  "This modal can be closed by pressing the Escape key or activating the close button.": "Esta janela pode ser fechada pressionando a tecla de Escape.",
  ", opens captions settings dialog": ", abre as configurações de legendas de comentários",
  ", opens subtitles settings dialog": ", abre as configurações de legendas",
  ", opens descriptions settings dialog": ", abre as configurações",
  ", selected": ", selecionada",
  "captions settings": "configurações de legendas de comentários",
  "subtitles settings": "configurações de legendas",
  "descriptions settings": "configurações das descrições",
  "Text": "Texto",
  "White": "Branco",
  "Black": "Preto",
  "Red": "Vermelho",
  "Green": "Verde",
  "Blue": "Azul",
  "Yellow": "Amarelo",
  "Magenta": "Magenta",
  "Cyan": "Ciano",
  "Background": "Plano-de-Fundo",
  "Window": "Janela",
  "Transparent": "Transparente",
  "Semi-Transparent": "Semi-Transparente",
  "Opaque": "Opaco",
  "Font Size": "Tamanho da Fonte",
  "Text Edge Style": "Estilo da Borda",
  "None": "Nenhum",
  "Raised": "Elevado",
  "Depressed": "Acachapado",
  "Uniform": "Uniforme",
  "Dropshadow": "Sombra de projeção",
  "Font Family": "Família da Fonte",
  "Proportional Sans-Serif": "Sans-Serif(Sem serifa) Proporcional",
  "Monospace Sans-Serif": "Sans-Serif(Sem serifa) Monoespaçada",
  "Proportional Serif": "Serifa Proporcional",
  "Monospace Serif": "Serifa Monoespaçada",
  "Casual": "Casual",
  "Script": "Script",
  "Small Caps": "Maiúsculas Pequenas",
  "Reset": "Redefinir",
  "restore all settings to the default values": "restaurar todas as configurações aos valores padrão",
  "Done": "Salvar",
  "Caption Settings Dialog": "Caíxa-de-Diálogo das configurações de Legendas",
  "Beginning of dialog window. Escape will cancel and close the window.": "Iniciando a Janela-de-Diálogo. Pressionar Escape irá cancelar e fechar a janela.",
  "End of dialog window.": "Fim da Janela-de-Diálogo",
  "{1} is loading.": "{1} está carregando.",
  "Exit Picture-in-Picture": "Sair de Picture-in-Picture",
  "Picture-in-Picture": "Picture-in-Picture"
});