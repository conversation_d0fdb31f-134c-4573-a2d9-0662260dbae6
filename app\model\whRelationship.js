/**
 * @file 万华编码关系表
 * @description 万华编码关系表模型
 * @createDate 2025-05-20
 */
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');
  const WhRelationshipSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
  }, { timestamps: true });
  return mongoose.model('WhRelationship', WhRelationshipSchema);
};
