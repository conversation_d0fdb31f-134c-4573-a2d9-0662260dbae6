const Controller = require('egg').Controller;
class DataBigScreenController extends Controller {
  // 获取成员单位，集团总人数，接害人数，监护档案数量，顶部数据
  async getScreenTopData() {
    const { ctx } = this;
    try {
      const { EnterpriseID } = ctx.session.adminUserInfo;
      const res = await ctx.service.dataBigScreen.getScreenTopData(
        EnterpriseID
      );
      ctx.helper.renderSuccess(ctx, {
        status: 200,
        data: res,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  async getScreenLeftData() {
    const { ctx } = this;
    try {
      const { EnterpriseID } = ctx.session.adminUserInfo;
      const { currentYear = new Date().getFullYear() } = ctx.query;
      const res = await ctx.service.dataBigScreen.getScreenLeftData(
        { EnterpriseID, currentYear: Number(currentYear) }
      );
      ctx.helper.renderSuccess(ctx, {
        status: 200,
        data: res,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  async getScreenRightData() {
    const { ctx } = this;
    try {
      const { EnterpriseID } = ctx.session.adminUserInfo;
      const { currentYear = new Date().getFullYear() } = ctx.query;
      const res = await ctx.service.dataBigScreen.getScreenRightData({
        EnterpriseID,
        currentYear: Number(currentYear),
      });
      ctx.helper.renderSuccess(ctx, {
        status: 200,
        data: res,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  async getOnlineMonitorData() {
    const { ctx } = this;
    try {
      const { EnterpriseID } = ctx.session.adminUserInfo; // 为啥用缓存的
      const res = await ctx.service.dataBigScreen.getOnlineMonitorData(EnterpriseID);
      ctx.helper.renderSuccess(ctx, {
        status: 200,
        data: res,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
}
module.exports = DataBigScreenController;
