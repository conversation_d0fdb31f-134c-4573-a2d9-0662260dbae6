module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  // 标准库
  const StandardLibrarySchema = new Schema(
    {
      _id: {
        type: String,
        default: shortid.generate,
      },
      uploader: {
        type: String,
        ref: 'AdminUser',
        default: '',
      }, // 上传人
      EnterpriseID: {
        type: String,
        ref: 'Adminorg',
        default: '',
      }, // 企业
      standardNumber: String, // 标准编号
      standardName: {
        // 标准名称
        type: String,
        default: '',
      },
      // 公布日期
      publishDate: {
        type: Date,
      },
      // 实施日期
      implementDate: {
        type: Date,
      },
      // 状态
      status: {
        type: Number,
        default: 1, // 1现行 2废止 3即将实施
      },
      // 文件名地址
      downLoadPath: String,
    },
    { timestamps: true }
  );


  return mongoose.model('StandardLibrary', StandardLibrarySchema);
};
