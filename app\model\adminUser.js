/**
 * Created by Zhanglc on 2022/4/7.
 * 企业用户对象
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const ctx = app.createAnonymousContext();
  const Schema = mongoose.Schema;
  const CryptoJS = require('crypto-js');
  const permissionPlugin = require('../utils/permission.js');

  const AdminUserSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    name: String, // 姓名
    unitCode: String, // 编码
    userName: String, // 用户名
    password: {
      type: String,
      set(val) {
        // return CryptoJS.AES.encrypt(val, app.config.encrypt_key).toString();
        return val && val.length > 30
          ? val
          : CryptoJS.SHA256(val + app.config.salt_sha2_key).toString();
      },
    }, // 密码
    // 存储密码有效期 val为new Date()
    passwordExpiresAt: {
      type: Date,
    },
    loginAttempts: Number, // 记录登录尝试次数
    loginAttemptsTimestamp: Array, // 记录登录尝试时间
    smsLoginAttempts: Number, // 记录短信登录尝试次数
    smsLoginAttemptsTimestamp: Array, // 记录短信登录尝试时间
    readedNotice: {
      type: Boolean,
      default: false,
    }, // 用户是否已读职业病防治法告知书 越城在使用，校准时间：2022.4.7
    email: String, // 邮箱
    phoneNum: {
      type: String,
      sparse: true,
      trim: true,
    }, // 手机号
    countryCode: {
      type: String,
      default: '86',
    }, // 手机号前国家代码
    comments: String, // 描述
    date: {
      type: Date,
      default: Date.now,
    }, // 创建时间
    logo: {
      type: String,
      default: '/static/upload/images/defaultlogo.png',
    }, // 头像
    enable: {
      type: Boolean,
      default: true,
    }, // 是否有效
    state: {
      type: String,
      default: '1', // 1正常，0删除
    }, // 假删除
    auth: {
      type: Boolean,
      default: false,
    }, // 是否是发布文章的作者 知识库用
    group: {
      type: String,
      ref: 'AdminGroup',
      default: app.config.groupID.adminGroupID,
    }, // 角色
    targetEditor: {
      type: String,
      ref: 'Employees',
    }, // 企业员工绑定ID, 已弃用
    IDcard: {
      type: String,
      default: '',
    }, // 身份证号
    expireDate: {
      type: String,
      default: '',
    }, // 身份证有效期
    userId: {
      type: String,
      ref: 'User',
    }, // 对应的是users表中的id
    newAddEnterpriseID: {
      type: String,
      default: '',
      ref: 'Adminorg',
    }, // 当前新添加企业ID 是企业添加多个企业时用于告知是添加的哪个企业，以此获取其审核状态
    landline: String, // 座机,
    employees: [
      {
        type: String,
        ref: 'Employees',
      },
    ], // 新的绑定员工ID
    source: {
      // 来源端
      type: String,
      default: 'enterprise',
      enum: [ 'enterprise', 'tj', 'super', 'dbscript', 'opt', 'jc', 'oapi' ],
    },
  });

  AdminUserSchema.index(
    { phoneNum: -1 },
    {
      unique: true,
      partialFilterExpression: { phoneNum: { $exists: true } },
    }
  );
  AdminUserSchema.index(
    { phoneNum: 1 },
    {
      unique: true,
      partialFilterExpression: { phoneNum: { $exists: true } },
    }
  );
  AdminUserSchema.index(
    { userName: -1 },
    {
      unique: true,
      partialFilterExpression: { phoneNum: { $exists: true } },
    }
  );
  AdminUserSchema.index(
    { userName: 1 },
    {
      unique: true,
      partialFilterExpression: { phoneNum: { $exists: true } },
    }
  );
  AdminUserSchema.index({ userId: -1 });
  AdminUserSchema.index({ employees: -1 });

  AdminUserSchema.plugin(permissionPlugin, {
    ctx,
    fieldMapping: {
      enterpriseId: 'newAddEnterpriseID',
    },
  });

  AdminUserSchema.plugin(app.dataMaskPlugin, {
    fields: {
      phoneNum: 'phoneNum',
    },
  });

  // targetEditor废弃，但代码中有使用的地方，无法清除，故未写修改脚本

  return mongoose.model('AdminUser', AdminUserSchema, 'adminusers');
};
