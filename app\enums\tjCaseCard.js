exports.CaseCardRecordStatusEnum = {
  PENDING_PARSE: 1, // comment: 待解析
  PARSING: 2, // comment: 解析中
  PARSE_FAILED: 3, // comment: 解析失败
  PARSED_NOT_REPORTED: 4, // comment: 已解析/未上报
  REPORT_SUCCESS: 5, // comment: 上报完成
  REPORT_FAILED: 6, // comment: 上报失败
};

exports.DocParseStatusMilestoneEnum = {
  PENDING: 0, // 开始
  PASSE_DOC_TEXT: 1, // 文档提取文字
  HAZARD_FACTOR_PARSE: 2, // 解析危害因素
  GET_HINT_WORDS: 3, // 获取提示词
  PARSE_DATA: 4, // 解析数据
  DONE: 5, // 完成
};

exports.DocParseStatusStatusEnum = {
  PENDING_PARSE: 0, // 待解析
  PARSING: 1, // 解析中
  PARSE_FAILED: 2, // 解析失败
  PARSE_SUCCESS: 3, // 解析成功
};

const MilestoneStatus = [
  { code: 1, label: '待解析', key: 'PENDING_PARSE' },
  { code: 2, label: '待校对', key: 'PENDING_REVIEW' },
  { code: 3, label: '校对中', key: 'REVIEWING' },
  { code: 4, label: '放弃上报', key: 'REPORT_ABANDONED' },
  { code: 5, label: '网络失败', key: 'NETWORK_FAILED' },
  { code: 6, label: '上报失败', key: 'REPORT_FAILED' },
  { code: 7, label: '上报成功', key: 'REPORT_SUCCESS' },
];

exports.MilestoneStatusEnum = MilestoneStatus.reduce((_enum, item, index) => {
  const _item = {
    ...item,
    priority: index,
  };
  _enum[item.key] = _item;
  _enum[item.code] = _item;
  return _enum;
}, {});
