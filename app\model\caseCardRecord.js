/**
 * tools 工具箱用户表
 */

// const { MilestoneStatusEnum } = require('../enums/tjCaseCard');

// const getMilestonePriority = milestone => {
//   return MilestoneStatusEnum[milestone.status].priority;
// };

module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  const CaseCardRecordSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    fileName: String, // 文件名
    staticName: String, // 实际存储的文件名
    status: { // 1. 待解析 2. 解析中 3. 解析失败 4. 已解析/未上报 5. 上报完成 6. 上报失败
      type: Number,
      default: 1,
    },
    completeTime: Date, // 解析完成时间
    docParseStatus: { // 文档解析状态
      milestone: Number, // 1. 危害因素解析 2. 获取提示词 3. 解析数据
      status: Number, // 0.待解析 1. 解析中 2. 解析失败
    },
    dockingStatus: {
      type: Number,
      default: 0,
    }, // 对接状态 0：未对接suspect 1：对接suspect成功 2: 对接suspect失败
    dockingMsg: String, // 对接原因
    dockingTime: Date, // 对接成功时间

    fileid: String, // 解析的id

    uid: String, // 当前上传文件的用户id

    hazards: [ String ], // 涉及的危害因素
    careType: String, // 监护种类  01：上岗前 02：在岗期间 03：离岗时 04：应急 05：离岗后
    parseData: Object, // 解析后的原始结果

    reportCode: String, // 编号
    // 以下是格式化后的数据 - 用于国家对接报送
    baseForm: {
      ohOrgCode: String, // 检查机构编码
      reportOrgName: String, // 检查单位名称
      empOrgName: String, // 用人单位名称
      empOrgBizCode: String, // 用人单位信用代码
      peFlag: String, // 初检/复查
      peDate: String, // 体检日期
      reportCode: String, // 体检编号
      patientName: String, // 体检人项目
      idType: String, // 证件类型 01居民身份证 02居民户口簿 03护照 04军官证 05驾驶证 06港澳通行证 07台湾通行证 99其他法定有效证件
      idcard: String, // 证件号
      sexCode: String, // 性别
      birthday: String, // 生日
      age: String, // 年龄
      empPhone: String, // 联系电话
      hazardNames: String, // 危害因素名称
      hazardOther: String, // 其他危害因素
      contactHazardCode: String, // 危害因素名编码
      contactHazardOther: String, // 其他危害因素编码
      typeOfWorkCode: String, // 工种编号
      typeOfWorkOther: String, // 其他工种
      employerEnterpriseName: String, // 用工单位
      employerCreditCode: String, // 用工单位信用代码
      preparerName: String, // 填表人
      preparerPhone: String, // 填表人电话
      preparerDate: String, // 报告出具日期
      propose: String, // 备注
      protectiveEquipmentCode: String, // 防护用品佩戴情况 1无佩戴 2偶尔佩戴 3基本佩戴 4经常佩戴
      conclusion: String, // 结论
      checkType: String, // 体检类型 0上岗前 1在岗 2离岗时 3复查 4应急 5 离岗后 6 普通体检
    }, // 基础信息
    employmentHistory: [{
      workType: String, // 工种
      hazards: String, // 接触的危害因素
      protectiveMeasures: String, // 防护措施
    }], // 职业史
    personalHistory: {
      smokingStatus: String, // 吸烟情况 1现在每天吸 2现在吸，但不是每天吸 3过去吸，现在不吸 4从不吸
      personalHistoryYear: String, // 吸烟史 - 年
      personalHistoryMonth: String, // 吸烟史 - 月
      dailySmokingVolum: String, // 吸烟史 - 日
    }, // 个人生活史
    mainFactorsData: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      careTypeCode: String, // 监护种类 01上岗前 02在岗期间 03离岗时 04应急 05离岗后
      hazardCode: String, // 危害因素代码
      hazardCodeName: String, // 危害因素名称
      monitorTypeCode: String, // 监测种类 1常规监测 2主动监测
      resultOptionCode: String, // 体检结论代码 20疑似职业病 30职业禁忌证 40复查 60其他疾病或异常 70目前未见异常
      occDiseaseCode: String, // 职业病代码 当体检结论为“疑似职业病”时，不可为空
      otherContent: String, // 其他疾病或异常 当体检结论为“疑似职业病”时，为职业病名称; 当体检结论为“职业禁忌证”或“其他疾病或异常” 时，不可为空
      isMain: String, // 是否是主要粉尘 1 主要;0 次要
      hazardStartDate: String, // 开始接害日期 体检类型是上岗前非必填，其余必填
      hazardYear: String, // 接害工龄 - 年 同上
      hazardMonth: String, // 接害工龄 - 月 同上
      hazardDay: String, // 接害工龄 - 日 同上
      enterpriseName: String, // 单位名称 当体监护种类是上岗前，体检结论是疑似职业病时，此项必填
      enterpriseBizCode: String, // 单位信用代码 同上
    }], // 主要危害因素
    ohItemClassResult: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      itemClassName: String, // 体检类别名称
      ohItemResult: [
        {
          _id: {
            type: String,
            default: shortid.generate,
          },
          itemCode: String, // 项目编码
          itemName: String, // 项目名称
          dataVersion: String, // 符号代码,非要求的项目不可填写。 0 代表=、1 代表<
          result: String, // 结果
          upperLimit: String, // 上限
          lowerLimit: String, // 下限
          unit: String, // 单位
          resultFlag: String, // 是否异常 不可为空;胸片类项目可不传; 0:正常;1:异常;3:未检查;
          radiographResult: String, // 胸片类项目检查结果 0未见异常 1尘肺样改变 2其他异常 3未检查
        },
      ],
    }], // 项目体检结果
    milestone: {
      status: { // 1.待解析 2.待校对 3.校对中 4.放弃上报 5.网络失败 6.上报失败 7.上报成功
        type: Number,
        default: 1,
      },
      message: String,
    },
  }, { timestamps: true });

  // CaseCardRecordSchema.pre('updateOne', function(next) {
  //   const oldDoc = this.get('milestone', { getOriginal: true });
  //   if (!oldDoc?.milestone?.status) {
  //     next();
  //     return;
  //   }
  //   if (this.milestone && this.milestone.status && getMilestonePriority(this.milestone.status) <= getMilestonePriority(oldDoc.milestone.status)) {
  //     this.milestone = oldDoc.milestone;
  //   }
  //   next();
  // });

  return mongoose.model('CaseCardRecord', CaseCardRecordSchema, 'caseCardRecord');
};
