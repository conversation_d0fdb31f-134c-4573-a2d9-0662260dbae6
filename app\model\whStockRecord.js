/**
 * @file 万华商城库存调整记录表
 * @description 记录库存调整的历史，包括手动调整和系统自动调整
 * @createDate 2025-03-15
 */
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');
  const ctx = app.createAnonymousContext();
  const permissionPlugin = require('../utils/permission.js');

  // 库存调整明细子模式
  const WhStockRecordDetailSchema = new Schema({
    // 商品SKU
    sku: {
      type: String,
      required: true,
      trim: true,
    },
    // 商品名称
    goodsName: {
      type: String,
      required: true,
      trim: true,
    },
    // 规格
    specification: {
      type: String,
      trim: true,
      required: false,
    },
    // 型号
    model: {
      type: String,
      trim: true,
      required: false,
    },
    // 调整前数量
    beforeQuantity: {
      type: Number,
      required: true,
    },
    // 调整数量（正数为增加，负数为减少）
    adjustQuantity: {
      type: Number,
      required: true,
    },
    // 调整后数量
    afterQuantity: {
      type: Number,
      required: true,
    },
    // 备注
    remark: {
      type: String,
      trim: true,
      required: false,
    },
    _id: false, // 禁用子文档的_id
  });

  const WhStockRecordSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    // 记录编号（自动生成）
    recordNo: {
      type: String,
      default() {
        // 生成格式为 SR + 年月日时分秒 + 4位随机数的记录编号
        const now = new Date();
        const dateStr =
          now.getFullYear().toString().substr(2) +
          ('0' + (now.getMonth() + 1)).slice(-2) +
          ('0' + now.getDate()).slice(-2) +
          ('0' + now.getHours()).slice(-2) +
          ('0' + now.getMinutes()).slice(-2) +
          ('0' + now.getSeconds()).slice(-2);
        const randomNum = Math.floor(Math.random() * 10000)
          .toString()
          .padStart(4, '0');
        const recordNo = `SR${dateStr}${randomNum}`;
        console.log('recordNo', recordNo);
        return recordNo;
      },
      unique: true,
    },
    // 组织编码
    EnterpriseID: {
      type: String,
      trim: true,
      required: true,
    },
    // 部门编码
    departmentCode: {
      type: String,
      trim: true,
      required: true,
    },
    // 组织名称
    orgName: {
      type: String,
      trim: true,
      required: false,
    },
    // 装置HSE编码
    plantCode: {
      type: String,
      trim: true,
      required: false,
    },
    // 装置HSE名称
    plantName: {
      type: String,
      trim: true,
      required: false,
    },
    // 调整类型：manual(手动调整), system(系统同步), scrap(报废)
    adjustType: {
      type: String,
      required: true,
      enum: [ 'manual', 'system', 'scrap' ],
    },
    // 调整日期
    adjustDate: {
      type: Date,
      required: true,
    },
    // 调整原因
    adjustReason: {
      type: String,
      trim: true,
      required: true,
    },
    // 报废原因（当adjustType为scrap时必填）
    scrapReason: {
      type: String,
      trim: true,
      required: false,
    },
    // 调整明细
    details: {
      type: [ WhStockRecordDetailSchema ],
      required: true,
    },
    // 创建人
    createBy: {
      type: String,
      trim: true,
      required: true,
    },
    // 创建人姓名
    createByName: {
      type: String,
      trim: true,
      required: true,
    },
    // 创建时间
    createDate: {
      type: Date,
      default: Date.now,
    },
    // 更新时间
    updateDate: {
      type: Date,
      required: false,
    },
    // 删除标记 (normal正常，deleted删除)
    deleteFlag: {
      type: String,
      default: 'normal',
      enum: [ 'normal', 'deleted' ],
    },
  }, {
    timestamps: { createdAt: 'createDate', updatedAt: 'updateDate' },
    versionKey: false,
    collection: 'wh_stock_record',
    minimize: false,
    strict: true,
  });

  // 创建索引
  WhStockRecordSchema.index({ EnterpriseID: 1 });
  WhStockRecordSchema.index({ adjustType: 1 });
  WhStockRecordSchema.index({ adjustDate: -1 });
  WhStockRecordSchema.index({ createBy: 1 });
  WhStockRecordSchema.index({ recordNo: 1 }, { unique: true });
  WhStockRecordSchema.index({ 'details.sku': 1 });
  WhStockRecordSchema.index({ deleteFlag: 1 });

  // 查询中间件 - 默认只查询未删除的数据
  WhStockRecordSchema.pre('find', function() {
    if (!this._conditions.deleteFlag) {
      this._conditions.deleteFlag = 'normal';
    }
  });

  WhStockRecordSchema.pre('findOne', function() {
    if (!this._conditions.deleteFlag) {
      this._conditions.deleteFlag = 'normal';
    }
  });

  WhStockRecordSchema.plugin(permissionPlugin, {
    ctx,
    fieldMapping: {
      enterpriseId: 'EnterpriseID',
    },
  });

  return mongoose.model('WhStockRecord', WhStockRecordSchema);
};
