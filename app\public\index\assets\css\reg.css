   /*媒体查询,不同屏幕下的根元素字体大小设置，利于rem*/
   @media (min-width:1300px) {
       html {
           font-size: 80px !important
       }

       * {
           margin: 0;
           padding: 0;
       }
   }

   @media (min-width:1536px) {
       html {
           font-size: 90px !important
       }

       * {
           margin: 0;
           padding: 0;
       }
   }

   @media (min-width:1800px) {
       html {
           font-size: 100px !important
       }

       * {
           margin: 0;
           padding: 0;
       }
   }

   @media (min-width: 801px) {
       html {
           font-size: 100px;
       }

        .imageCode-img {
            height: 35px !important;
            margin-right: 15px !important;
            margin-top: 26px;
        }
       .regApp .el-form-item__content {
           line-height: 0.55rem;
           font-size: 0.14rem;
       }

       .aggree {
           text-decoration: none;
           color: rgba(42, 145, 252, 1);
           padding: 0;
           font-size: 0.14rem;
           border-radius: 0;
       }

       .step_3_back {
           margin-top: 0.18rem;
           width: 4.1rem;
           height: 0.65rem;
           background: #F1F1F1;
           border: none;
           font-size: 0.2rem;
           border-radius: 0.04rem;
           font-family: PingFangSC-Medium, PingFang SC;
           font-weight: 500;
           color: #1972E3;
           line-height: 0.3rem;
       }

       .step_3_repeat {
           margin-top: 0.18rem;
           width: 4.1rem;
           height: 0.65rem;
           border: none;
           font-size: 0.2rem;
           background: rgba(0, 99, 224, 0.88);
           border-radius: 0.04rem;
           font-family: PingFangSC-Medium, PingFang SC;
           font-weight: 500;
           color: #FFFFFF;

       }


       .back {
           padding-top: 0.34rem;
           height: 0.45rem;
           margin-left: 0.4rem;
           font-size: 0.18rem;
           color: #2A91FC;
           z-index: 999;
       }

       * {
           margin: 0;
           padding: 0;
       }

       body {
           background: rgba(245, 249, 255, 1);
           width: 100%;
           height: 100vh;
       }

       .back {
           padding-top: 0.14rem;
           height: 0.25rem;
           margin-left: 0.4rem;
           font-size: 0.18rem;
           ;
           color: #2A91FC;
           z-index: 999
       }

       .backa {
           font-size: 0.18rem;
           color: #2A91FC;
           text-decoration: none;
       }

       .main {
           width: 100%;
           height: 100%;
           margin-top: -0.39rem;
           display: flex;
           justify-content: center;
           align-items: center;
           text-align: center;
       }

       .content {
           margin-top: 0.21rem;
           height: 6.61rem;
           width: 9.3rem;
           margin: 0 auto;
       }

       .regApp .el-form-item__error {
           font-size: 0.1rem;
       }


       .steps {
           display: flex;
           flex-wrap: nowrap;
           border: 0.01rem solid #DCDEE0;
       }

       .step {
           width: 3.2rem;
           height: 0.48rem;
           display: flex;
       }

       .step_3_tips {
           margin-top: 0.23rem;
           padding: 0 5%;
       }

       .step-head-1 {
           width: 3.19rem;
           height: 0.48rem;
           font-size: 0.14rem;
           font-weight: 500;
           position: relative;
           background: #0063E0;
           display: flex;
           justify-content: center;
           align-items: center;
           color: #FFFFFF;
       }

       .step-head-1::after {
           content: '';
           position: absolute;
           right: 0;
           display: block;
           border-top: 0.24rem solid transparent;
           border-bottom: 0.24rem solid transparent;
           border-left: 0.15rem solid #0063E0;
           z-index: 1800
       }

       .step-head-1::before {
           content: '';
           position: absolute;
           right: -0.01rem;
           display: block;
           border-top: 0.24rem solid #FFFFFF;
           border-bottom: 0.24rem solid #FFFFFF;
           border-left: 0.15rem solid rgba(220, 222, 224, 1);
           z-index: 1799;
       }

       .is-sucess1>.step-head-1 {
           background: #FFFFFF;
           color: #323233;
       }

       .is-sucess1>.step-head-1::after {
           border-left: 0.15rem solid #FFFFFF;
           border-top: 0.24rem solid #0063E0;
           border-bottom: 0.24rem solid #0063E0;
           right: -0.01rem;
           color: #FFFFFF;
       }

       .last-sucess2>.step-head-2 {
           background: #0063E0;
           color: #FFFFFF;
       }

       .last-sucess2>.step-head-2::after {
           border-left: 0.15rem solid #0063E0;
       }

       .step-head-2 {
           width: 3.19rem;
           height: 0.48rem;
           position: relative;
           background: #FFFFFF;
           font-size: 0.14rem;
           font-weight: 500;
           display: flex;
           justify-content: center;
           align-items: center;
           color: #323233;
       }

       .step-head-2::after {
           content: '';
           position: absolute;
           right: 0;
           display: block;
           border-top: 0.24rem solid transparent;
           border-bottom: 0.24rem solid transparent;
           border-left: 0.15rem solid #ffffff;
           z-index: 1800
       }

       .step-head-2::before {
           content: '';
           position: absolute;
           right: -0.01rem;
           display: block;
           border-top: 0.24rem solid #FFFFFF;
           border-bottom: 0.24rem solid #FFFFFF;
           border-left: 0.15rem solid rgba(220, 222, 224, 1);
           z-index: 1799;
       }

       .is-sucess2>.step-head-2 {
           background: #FFFFFF;
           color: #323233;
       }

       .is-sucess2>.step-head-2::after {
           border-left: 0.15rem solid #FFFFFF;
           border-top: 0.24rem solid #0063E0;
           border-bottom: 0.24rem solid #0063E0;
           right: -0.01rem;
           color: #FFFFFF;
       }

       .over-sucess2>.step-head-1::after {
           border-left: 0.15rem solid #ffffff !important;
           border-top: 0.24rem solid transparent;
           border-bottom: 0.24rem solid transparent;
           right: 0.01rem;
           color: #FFFFFF;
       }

       .step-head-3 {
           width: 100%;
           height: 0.48rem;
           position: relative;
           background: #FFFFFF;
           font-size: 0.14rem;
           font-weight: 500;
           display: flex;
           justify-content: center;
           align-items: center;
           color: #323233;
       }

       .last-sucess3>.step-head-3 {
           background: #0063E0;
           color: #FFFFFF;
       }

       .step-main {
           font-size: 12px;
           color: #48576a;
           margin-top: 15px;
           font-size: 14px;
           color: #444444;
       }

       .last-sucess>.step-head {
           color: #ffffff;
           border-color: #60B34F;
           background: rgba(0, 99, 224, 1);
       }

       .last-sucess>.step-head .step-circle {
           background-color: #2f318e;
       }

       .step_1 {
           margin-top: 0.25rem;
           width: 9.3rem;
           height: 5.88rem;
           background-color: #FFFFFF;
           display: flex;

       }

       .step_1_form {
           display: grid;
           width: 4.1rem;
       }

       .step_1_left {
           width: 4.65em;
           height: 6.6rem;
           background-image: url('/static/images/reg_left.jpg');
           background-repeat: no-repeat;
           background-size: 100% 100%;
       }

       .step_1_right {
           width: 4.65em;
           height: 6.6rem;
           background-color: #FFFFFF;

       }

       .step_1_content {
           margin-top: 0.36rem;
           margin-left: 0.25rem;
       }

       .step_1_title {
           width: 1.2rem;
           height: 0.33rem;
           font-size: 0.24rem;
           font-family: PingFangSC-Semibold, PingFang SC;
           font-weight: 600;
           color: #000000;
           line-height: 0.33rem;
       }

       .step_1_name {
        margin-top: 0.19rem;
        width: 100%;
        }

        .step_1_IDcard {
            margin-top: 0.19rem;
            width: 100%;
        }

       .step_1_phone {
           margin-top: 0.19rem;
           width: 100%;
       }

       .step_1_code {
           margin-top: 0.18rem;
           width: 63%;
       }

       .step_1_pwd {
           margin-top: 0.18rem;
           width: 4.1rem;
       }

       .step_1_rpwd {
           margin-top: 0.18rem;
           width: 4.1rem;
       }

       .step_1_next {
           margin-top: 0.18rem;
           width: 4.1rem;
           height: 0.48rem;
           background: rgba(0, 99, 224, 0.88);
           border-radius: 0.04rem;
           font-size: 0.2rem;
           font-family: PingFangSC-Medium, PingFang SC;
           font-weight: 500;
           color: #FFFFFF;
           border: none;
           line-height: 0.3rem;
       }

       .step_1_exit {
           margin-top: 0.18rem;
           width: 4.1rem;
           height: 0.48rem;
           background: #F1F1F1;
           border: none;
           font-size: 0.2rem;
           border-radius: 0.04rem;
           font-family: PingFangSC-Medium, PingFang SC;
           font-weight: 500;
           color: #1972E3;
           line-height: 0.3rem;
       }

       /* 步骤二 */
       .step_2 {
           margin-top: 0.25rem;
           width: 9.3rem;
           height: 6.47rem;
           background-color: #FFFFFF;
           display: flex;
       }

       .step_2_form {
           display: grid;
           margin-top: 0.46rem;
           margin-left: 0.19rem;
           width: 4.1rem;
       }

       .step_2_left {
           width: 4.65em;
           height: 6.47rem;
           background-color: #FFFFFF;
       }

       .step_2_leftmain {
           margin-top: 0.46rem;
           margin-left: 0.36rem;
           width: 4.1rem;
           height: 5.55rem;
           background: #F2F7FD;
           border: 0.01rem solid #E0E2E3;
       }

       .step_2_right {
           width: 4.65em;
           height: 6.47rem;
           background-color: #FFFFFF;
       }

       .step_2_ename {
           width: 4.1rem;
       }

       .step_2_xycode {
           margin-top: 0.16rem;
           width: 4.1rem;
       }

       .step_2_legal {
           margin-top: 0.16rem;
           width: 4.1rem;
       }

       .step_2_name {
           margin-top: 0.16rem;
           width: 4.1rem;
       }

       .step_2_address {
           margin-top: 0.16rem;
           width: 4.1rem;
           height: 0.48rem;
       }

       .step_2_deaddress {
           margin-top: 0.16rem;
           width: 4.1rem;
       }

       .step_2_agree {
           margin-top: 0.15rem;
           margin-right: 0.36rem;
           width: 100%;
           font-size: 0.14rem;
           font-family: PingFangSC-Regular, PingFang SC;
           font-weight: 400;
           color: #979797;
           display: flex;
           justify-content: flex-end;
           align-items: center;
       }

       .step_2_submit {
           margin-top: 0.16rem;
           width: 4.1rem;
           height: 0.48rem;
           background: rgba(0, 99, 224, 0.88);
           border-radius: 0.04rem;
           font-size: 0.2rem;
           font-family: PingFangSC-Medium, PingFang SC;
           font-weight: 500;
           color: #FFFFFF;
           border: none;
           line-height: 0.3rem;
       }

       .step_2_back {
           margin-top: 0.18rem;
           width: 4.1rem;
           height: 0.48rem;
           background: #F1F1F1;
           border: none;
           font-size: 0.2rem;
           border-radius: 0.04rem;
           font-family: PingFangSC-Medium, PingFang SC;
           font-weight: 500;
           color: #1972E3;
           line-height: 0.3rem;
       }

       .step_3 {
           margin-top: 0.25rem;
           width: 9.3rem;
           height: auto;
           padding-bottom: 0.23rem;
           background: #FFFFFF;
           display: flex;
           text-align: center;
           justify-content: center;
           flex-wrap: wrap;
           align-items: center;
           font-size: 0.16rem;
           font-family: PingFangSC-Medium, PingFang SC;
           font-weight: 500;
           color: #0063E0;
       }

       .step3m {
           width: 100%;
       }

       .sendVerifyCode {
           margin-top: 0.28rem;
           width: 1.1rem;
           height: 0.5rem;
           background: #F2F7FD;
           border: none;
           font-family: PingFangSC-Medium, PingFang SC;
           font-size: 0.14rem;
           font-weight: 500;
           color: #0063E0;
           line-height: 0.22rem;
       }

       .regApp .el-input__inner {
           height: 0.48rem;
       }

       .regApp .el-message .el-icon-error {
           font-size: 0.14rem;
       }

       .regApp .el-message .el-icon-warning {
           font-size: 0.14rem;
       }

       .regApp .el-message .el-icon-success {
           font-size: 0.14rem;
       }


       .regApp .el-form-item {
           margin-bottom: 0;
       }

       /* 待删除 */
       input {
           height: 0.24rem;
           border: 0.01rem solid #E0E4EB;
           padding: 0.12rem
       }

       input:focus {
           outline: none;
       }

       select:focus {
           outline: none;
       }

       ::-webkit-input-placeholder {
           /* Chrome/Opera/Safari */
           font-size: 0.16rem;
           font-family: PingFangSC-Regular, PingFang SC;
           font-weight: 400;
           color: #8F9399;
           line-height: 0.24rem;
       }

       ::-moz-placeholder {
           /* Firefox 19+ */
           font-size: 0.16rem;
           font-family: PingFangSC-Regular, PingFang SC;
           font-weight: 400;
           color: #8F9399;
           line-height: 0.24rem;
       }

       :-ms-input-placeholder {
           /* IE 10+ */
           font-size: 0.16rem;
           font-family: PingFangSC-Regular, PingFang SC;
           font-weight: 400;
           color: #8F9399;
           line-height: 0.24rem;
       }

       :-moz-placeholder {
           /* Firefox 18- */
           font-size: 0.16rem;
           font-family: PingFangSC-Regular, PingFang SC;
           font-weight: 400;
           color: #8F9399;
           line-height: 0.24rem;
       }

       #right {
           width: 0.37rem;
           height: 0.37rem;
           background: #52C41A;
           border-radius: 50%;
           display: flex;
           justify-content: center;
           align-items: center;
       }

       #right::before {
           content: "";
           display: block;
           width: 0.15rem;
           height: 0.08rem;
           border: 0.02rem solid #ffffff;
           border-right: none;
           border-top: none;
           transform: rotate(-45deg) translate(0.02rem, -0.02rem);
       }

       #righterror {
           width: 0.37rem;
           height: 0.37rem;
           background: #F46C6C;
           border-radius: 50%;
           display: flex;
           justify-content: center;
           align-items: center;
       }

       #righterror::before {
           content: "";
           display: block;
           width: 0.08rem;
           height: 0.08rem;
           border: 0.02rem solid #ffffff;
           border-right: none;
           border-top: none;
           transform: rotate(-45deg) translate(0.08rem, -0.01rem);
       }

       #righterror::after {
           content: "";
           display: block;
           width: 0.08rem;
           height: 0.08rem;
           border: 0.02rem solid #ffffff;
           border-right: none;
           border-top: none;
           transform: rotate(135deg) translate(0.07rem, 0rem);
       }

       .step_3_main {
           margin-top: 0.23rem;
           display: flex;
           justify-content: center;
           align-items: center;
       }

       img[Attributes] {
           width: 2.2rem;
       }

       .step_2_tip {
           margin-top: 0.38rem;
           margin-left: 0.3rem;
           width: 3.5rem;
           height: 1.02rem;
           font-size: 0.14rem;
           font-family: PingFangSC-Regular, PingFang SC;
           font-weight: 400;
           color: #9EA1A6;
           line-height: 0.26rem;
       }

       .step_2_file {
           width: 100%;
           height: 0.22rem;
           font-size: 0.16rem;
           font-family: PingFangSC-Medium, PingFang SC;
           font-weight: 500;
           color: #9EA1A6;
       }

       .step_2_upbtn {
           background: transparent;
           height: 0.22rem;
           font-size: 0.16rem;
           font-family: PingFangSC-Medium, PingFang SC;
           font-weight: 500;
           color: #9EA1A6;
           line-height: 0.22rem;
           outline: none;
           border: none;
       }


   }


   @media (max-width: 800px) {
       html {
           font-size: 75px;
       }

       * {
           margin: 0;
           padding: 0;
       }

        .imageCode-img {
            height: 40px !important;
            margin-right: 51px !important;
        }
       .regApp .el-message-box {
           width: 100%;
       }

       .regApp .el-message-box__wrapper {
           top: 25%;

       }

       .regApp .el-cascader__dropdown {
           max-width: 100%;
           overflow-x: auto;
       }

       .regApp .el-button {
           font-size: 12px;
       }

       body {
           background: rgba(245, 249, 255, 1);
           width: 100%;
           height: 100vh;
       }

       .back {
           padding-top: 0.34rem;
           height: 0.25rem;
           margin-left: 0.4rem;
           font-size: 0.18rem;
           ;
           color: #2A91FC;
           z-index: 999
       }

       .backa {
           font-size: 0.18rem;
           color: #2A91FC;
           text-decoration: none;
       }

       .main {
           width: 100%;
           height: 100%;
           margin-top: -0.59rem;
           display: flex;
           justify-content: center;
           align-items: center;
           text-align: center;
       }

       .content {
           margin-top: 0.21rem;
           height: 85%;
           width: 90%;
           margin: 0 auto;
           overflow-y: hidden;
       }

       .steps {
           display: flex;
           flex-wrap: nowrap;
           border: 0.01rem solid #DCDEE0;
       }

       .step {
           width: 34%;
           height: 0.48rem;
           display: flex;
       }

       .step-head-1 {
           width: 3.19rem;
           height: 0.48rem;
           font-size: 0.14rem;
           font-weight: 500;
           position: relative;
           background: #0063E0;
           display: flex;
           justify-content: center;
           align-items: center;
           color: #FFFFFF;
       }

       .step-head-1::after {
           content: '';
           position: absolute;
           right: 0;
           display: block;
           border-top: 0.24rem solid transparent;
           border-bottom: 0.24rem solid transparent;
           border-left: 0.15rem solid #0063E0;
           z-index: 1800
       }

       .step-head-1::before {
           content: '';
           position: absolute;
           right: -0.01rem;
           display: block;
           border-top: 0.24rem solid #FFFFFF;
           border-bottom: 0.24rem solid #FFFFFF;
           border-left: 0.15rem solid rgba(220, 222, 224, 1);
           z-index: 1799;
       }

       .is-sucess1>.step-head-1 {
           background: #FFFFFF;
           color: #323233;
       }

       .is-sucess1>.step-head-1::after {
           border-left: 0.15rem solid #FFFFFF;
           border-top: 0.24rem solid #0063E0;
           border-bottom: 0.24rem solid #0063E0;
           right: -0.01rem;
           color: #FFFFFF;
       }

       .last-sucess2>.step-head-2 {
           background: #0063E0;
           color: #FFFFFF;
       }

       .last-sucess2>.step-head-2::after {
           border-left: 0.15rem solid #0063E0;
       }

       .step-head-2 {
           width: 3.19rem;
           height: 0.48rem;
           position: relative;
           background: #FFFFFF;
           font-size: 0.14rem;
           font-weight: 500;
           display: flex;
           justify-content: center;
           align-items: center;
           color: #323233;
       }

       .step-head-2::after {
           content: '';
           position: absolute;
           right: 0;
           display: block;
           border-top: 0.24rem solid transparent;
           border-bottom: 0.24rem solid transparent;
           border-left: 0.15rem solid #ffffff;
           z-index: 1800
       }

       .step-head-2::before {
           content: '';
           position: absolute;
           right: -0.01rem;
           display: block;
           border-top: 0.24rem solid #FFFFFF;
           border-bottom: 0.24rem solid #FFFFFF;
           border-left: 0.15rem solid rgba(220, 222, 224, 1);
           z-index: 1799;
       }

       .is-sucess2>.step-head-2 {
           background: #FFFFFF;
           color: #323233;
       }

       .is-sucess2>.step-head-2::after {
           border-left: 0.15rem solid #FFFFFF;
           border-top: 0.24rem solid #0063E0;
           border-bottom: 0.24rem solid #0063E0;
           right: -0.01rem;
           color: #FFFFFF;
       }

       .over-sucess2>.step-head-1::after {
           border-left: 0.15rem solid #ffffff !important;
           border-top: 0.24rem solid transparent;
           border-bottom: 0.24rem solid transparent;
           right: 0.01rem;
           color: #FFFFFF;
       }

       .step-head-3 {
           width: 100%;
           height: 0.48rem;
           position: relative;
           background: #FFFFFF;
           font-size: 0.14rem;
           font-weight: 500;
           display: flex;
           justify-content: center;
           align-items: center;
           color: #323233;
       }

       .last-sucess3>.step-head-3 {
           background: #0063E0;
           color: #FFFFFF;
       }


       .step-main {
           font-size: 12px;
           color: #48576a;
           margin-top: 15px;
           font-size: 14px;
           color: #444444;
       }




       .last-sucess>.step-head {
           color: #ffffff;
           border-color: #60B34F;
           background: rgba(0, 99, 224, 1);
       }

       .last-sucess>.step-head .step-circle {
           background-color: #2f318e;
       }

       .step_1 {
           margin-top: 0.25rem;
           width: 100%;
           height: 100%;
           background-color: #FFFFFF;
           display: flex;

       }

       .step_1_form {
           display: grid;
           width: 96%;
       }

       .step_1_left {
           display: none;
           width: 4.65em;
           height: 5.88rem;
           background-image: url('/static/images/reg_left.jpg');
           background-repeat: no-repeat;
           background-size: 100% auto;
       }

       .step_1_right {
           width: 100%;
           height: 5.88rem;
           background-color: #FFFFFF;

       }

       .step_1_content {
           margin-top: 0.56rem;
           margin-left: 4%;
       }

       .step_1_title {
           width: 1.2rem;
           height: 0.33rem;
           font-size: 0.24rem;
           font-family: PingFangSC-Semibold, PingFang SC;
           font-weight: 600;
           color: #000000;
           line-height: 0.33rem;
       }

       .step_1_phone {
           margin-top: 0.19rem;
           width: 100%
       }

       .step_1_code {
           /* margin-top:0.28rem ; */
           width: 63%;
       }

       .step_1_pwd {
           /* margin-top:0.28rem ; */
           width: 100%
       }

       .step_1_rpwd {
           /* margin-top:0.28rem ; */
           width: 100%
       }

       .step_1_next {
           margin-top: 0.15rem;
           width: 100%;
           height: 0.65rem;
           background: rgba(0, 99, 224, 0.88);
           border-radius: 0.04rem;
           font-size: 0.2rem;
           font-family: PingFangSC-Medium, PingFang SC;
           font-weight: 500;
           color: #FFFFFF;
           border: none;
           line-height: 0.3rem;
       }

       .step_1_exit {
           margin-top: 0.18rem;
           width: 100%;
           height: 0.65rem;
           background: #F1F1F1;
           border: none;
           font-size: 0.2rem;
           border-radius: 0.04rem;
           font-family: PingFangSC-Medium, PingFang SC;
           font-weight: 500;
           color: #1972E3;
           line-height: 0.3rem;
       }

       /* 步骤二 */
       .step_2 {
           margin-top: 0.25rem;
           width: 100%;
           height: 100%;
           background-color: #FFFFFF;
           overflow-y: auto;
           /* display: flex; */
       }

       .step_2_left {
           width: 100%;
           /* height:6.27rem; */
           background-color: #FFFFFF;
       }

       .step_2_leftmain {
           /* margin-top: 0.46rem; */
           /* margin-left: 0.36rem; */
           margin: 0.46rem auto;
           width: 92%;
           height: 5.45rem;
           background: #F2F7FD;
           border: 0.01rem solid #E0E2E3;
       }

       .step_2_right {
           width: 100%;
           height: 8.5rem;
           background-color: #FFFFFF;
       }

       .step_2_ename {
           width: 100%;
       }

       .step_2_xycode {
           /* margin-top: 0.16rem; */
           width: 100%;
       }

       .step_2_legal {
           /* margin-top: 0.16rem; */
           width: 100%;
       }

       .step_2_name {
           /* margin-top: 0.16rem; */
           width: 100%;
       }

       .step_2_address {
           /* margin-top: 0.16rem; */
           width: 100%;
           height: 0.65rem;
       }

       .step_2_deaddress {
           /* margin-top: 0.16rem; */
           width: 100%;
       }

       .step_2_agree {
           width: 100%;
           font-size: 0.14rem;
           font-family: PingFangSC-Regular, PingFang SC;
           font-weight: 400;
           color: #979797;
           display: flex;
           justify-content: flex-end;
           align-items: center;
       }

       .step_2_submit {
           margin-top: 0.16rem;
           width: 100%;
           height: 0.65rem;
           background: rgba(0, 99, 224, 0.88);
           border-radius: 0.04rem;
           font-size: 0.2rem;
           font-family: PingFangSC-Medium, PingFang SC;
           font-weight: 500;
           color: #FFFFFF;
           border: none;
           line-height: 0.3rem;
       }

       .step_2_back {
           margin-top: 0.18rem;
           width: 100%;
           height: 0.65rem;
           background: #F1F1F1;
           border: none;
           font-size: 0.2rem;
           border-radius: 0.04rem;
           font-family: PingFangSC-Medium, PingFang SC;
           font-weight: 500;
           color: #1972E3;
           line-height: 0.3rem;
       }

       .step_3 {
           margin-top: 0.25rem;
           width: 100%;
           height: auto;
           padding-bottom: 0.23rem;
           background: #FFFFFF;
           display: flex;
           text-align: center;
           justify-content: center;
           flex-wrap: wrap;
           align-items: center;
           font-size: 0.16rem;
           font-family: PingFangSC-Medium, PingFang SC;
           font-weight: 500;
           color: #0063E0;
       }

       .step_3_tips {
           margin-top: 0.23rem;
           padding: 0.2rem;
       }

       .step_2_form {
           display: grid;
           width: 92%;
           margin: 0 auto;
       }

       .sendVerifyCode {
           width: 32%;
           background: #F2F7FD;
           border: none;
           font-family: PingFangSC-Medium, PingFang SC;
           font-weight: 500;
           color: #0063E0;
           line-height: 0.22rem;
       }

       .step3m {
           width: 100%
       }

       .regApp .el-input__inner {
           height: 0.65rem;
       }

       .regApp .el-message .el-icon-error {
           font-size: 0.14rem;
       }

       .regApp .el-message .el-icon-success {
           font-size: 0.14rem;
       }

       .regApp .el-message .el-icon-warning {
           font-size: 0.14rem;
       }

       /* 待删除 */
       input {
           height: 0.24rem;
           border: 0.01rem solid #E0E4EB;
           padding: 0.12rem
       }

       input:focus {
           outline: none;
       }

       select:focus {
           outline: none;
       }

       ::-webkit-input-placeholder {
           /* Chrome/Opera/Safari */
           font-size: 0.16rem;
           font-family: PingFangSC-Regular, PingFang SC;
           font-weight: 400;
           color: #8F9399;
           line-height: 0.24rem;
       }

       ::-moz-placeholder {
           /* Firefox 19+ */
           font-size: 0.16rem;
           font-family: PingFangSC-Regular, PingFang SC;
           font-weight: 400;
           color: #8F9399;
           line-height: 0.24rem;
       }

       :-ms-input-placeholder {
           /* IE 10+ */
           font-size: 0.16rem;
           font-family: PingFangSC-Regular, PingFang SC;
           font-weight: 400;
           color: #8F9399;
           line-height: 0.24rem;
       }

       :-moz-placeholder {
           /* Firefox 18- */
           font-size: 0.16rem;
           font-family: PingFangSC-Regular, PingFang SC;
           font-weight: 400;
           color: #8F9399;
           line-height: 0.24rem;
       }

       #right {
           width: 0.37rem;
           height: 0.37rem;
           background: #52C41A;
           border-radius: 50%;
           display: flex;
           justify-content: center;
           align-items: center;
       }

       #right::before {
           content: "";
           display: block;
           width: 0.15rem;
           height: 0.08rem;
           border: 0.02rem solid #ffffff;
           border-right: none;
           border-top: none;
           transform: rotate(-45deg) translate(0.02rem, -0.02rem);
       }

       .step_3_main {
           display: flex;
           justify-content: center;
           align-items: center;
       }

       img[Attributes] {
           width: 2.2rem;
       }

       .step_2_tip {
           /* margin-top: 0.38rem; */
           /* margin-left: 0.3rem; */
           margin: 0.38rem auto 0 auto;
           width: 95%;
           height: 1.02rem;
           font-size: 0.14rem;
           font-family: PingFangSC-Regular, PingFang SC;
           font-weight: 400;
           color: #9EA1A6;
           line-height: 0.26rem;
       }

       .step_2_file {
           width: 100%;
           height: 0.22rem;
           font-size: 0.16rem;
           font-family: PingFangSC-Medium, PingFang SC;
           font-weight: 500;
           color: #9EA1A6;
       }

       .step_2_upbtn {
           background: transparent;
           height: 0.22rem;
           font-size: 0.16rem;
           font-family: PingFangSC-Medium, PingFang SC;
           font-weight: 500;
           color: #9EA1A6;
           line-height: 0.22rem;
           outline: none;
           border: none;
       }

       .step_3_back {
           margin-top: 0.18rem;
           width: 4.1rem;
           height: 0.65rem;
           background: #F1F1F1;
           border: none;
           font-size: 0.2rem;
           border-radius: 0.04rem;
           font-family: PingFangSC-Medium, PingFang SC;
           font-weight: 500;
           color: #1972E3;
           line-height: 0.3rem;
       }

       .step_3_repeat {
           margin-top: 0.18rem;
           width: 4.1rem;
           height: 0.65rem;
           border: none;
           font-size: 0.2rem;
           background: rgba(0, 99, 224, 0.88);
           border-radius: 0.04rem;
           font-family: PingFangSC-Medium, PingFang SC;
           font-weight: 500;
           color: #FFFFFF;

       }

       #righterror {
           width: 0.37rem;
           height: 0.37rem;
           margin-top: 0.23rem;
           background: #F46C6C;
           border-radius: 50%;
           display: flex;
           justify-content: center;
           align-items: center;
       }

       #righterror::before {
           content: "";
           display: block;
           width: 0.08rem;
           height: 0.08rem;
           border: 0.02rem solid #ffffff;
           border-right: none;
           border-top: none;
           transform: rotate(-45deg) translate(0.08rem, -0.01rem);
       }

       #righterror::after {
           content: "";
           display: block;
           width: 0.08rem;
           height: 0.08rem;
           border: 0.02rem solid #ffffff;
           border-right: none;
           border-top: none;
           transform: rotate(135deg) translate(0.07rem, 0rem);
       }

       .aggree {
           text-decoration: none;
           color: rgba(42, 145, 252, 1);
           padding: 0;
           font-size: 0.14rem;
           border-radius: 0;
       }

   }