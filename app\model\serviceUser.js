/**
 * 机构端用户对象
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const moment = require('moment');
  const CryptoJS = require('crypto-js');
  const serviceGroupID = app.config.groupID.serviceGroupID || ''; // 机构用户角色ID

  require('./adminGroup');

  const ServiceUserSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    userName: { // 用户名
      type: String,
      default: '',
    },
    name: { // 姓名
      type: String,
      default: '',
    },
    phoneNum: {
      type: String,
      index: true, // 普通索引
      // require: true,
      default: '',
    },
    email: {
      type: String,
      default: '',
    },
    password: {
      type: String,
      set(val) {
        return CryptoJS.SHA256(val + app.config.salt_sha2_key).toString();
      },
    },
    // 存储密码有效期 val为new Date()
    passwordExpiresAt: {
      type: Date,
    },
    loginAttempts: Number, // 记录登录尝试次数
    loginAttemptsTimestamp: Array, // 记录登录尝试时间
    org_id: { // 所属机构id
      type: String,
      default: '',
      ref: 'ServiceOrg', // 关联机构表
    },
    org: { // 所属机构名
      type: String,
      default: '',
    },
    department_id: { // 所属部门id
      type: String,
      default: '',
    },
    department: { // 所属部门名称
      type: String,
      default: '',
    },
    isQLC: { // 是否机构VIP(全流程)用户
      type: Boolean,
      default: false,
    },
    group: {
      type: String,
      default: serviceGroupID,
      // ref: 'AdminGroup',
      ref: 'JcGroup',
    },
    countryCode: { // 手机号前国家代码
      type: String,
      default: '86',
    },
    ctime: { // 创建/更改时间
      type: Date,
      default: Date.now,
      set(time) {
        return moment(time).add(8, 'hours');
      },
    },
    logo: { // 头像
      type: String,
      default: '/static/upload/images/defaultlogo.png',
    },
    state: { // 在职状态
      type: Boolean,
      default: true,
    },
    enable: { // 是否能用
      type: Boolean,
      default: true,
    },
    comments: String, // 备注
    readVersionId: { // 已阅读的上一个更新版本的版本id
      type: String,
    },
  });

  return mongoose.model('ServiceUser', ServiceUserSchema, 'serviceUsers');


};

