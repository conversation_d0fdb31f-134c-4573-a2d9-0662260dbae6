/**
 * @file 万华商城商品表
 * @description 万华商城商品表模型（仅包含从订单详情中能获取到的字段）
 * @createDate 2025-03-14
 */
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');
  const ctx = app.createAnonymousContext();
  const permissionPlugin = require('../utils/permission.js');
  const WhGoodsSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    // 商品SKU
    sku: {
      type: String,
      required: true,
      unique: true,
      trim: true,
    },
    // 组织编码
    EnterpriseID: {
      type: String,
      trim: true,
      required: false,
    },
    // 部门编码
    departmentCode: {
      type: String,
      trim: true,
      required: false,
    },
    // 商品名称
    goodsName: {
      type: String,
      required: true,
      trim: true,
    },
    // 商品价格
    price: {
      type: Number,
      required: true,
      min: 0,
    },
    // 商品规格
    goodsUnit: {
      type: String,
      trim: true,
      required: false,
    },
    // 商品税率
    goodsTaxRate: {
      type: Number,
      default: 13,
      min: 0,
      max: 100,
    },
    // 商品分类编码
    categoryCode: {
      type: String,
      trim: true,
      required: false,
    },
    // 商品分类名称
    categoryName: {
      type: String,
      trim: true,
      required: false,
    },
    // 库存数量
    stockQuantity: {
      type: Number,
      default: 0,
      min: 0,
    },
    // 销量
    salesVolume: {
      type: Number,
      default: 0,
      min: 0,
    },
    // 关联的防护用品库存ID
    inventoryId: {
      type: String,
      trim: true,
      required: false,
    },
    // 删除标记 (normal正常，deleted删除)
    deleteFlag: {
      type: String,
      default: 'normal',
      enum: [ 'normal', 'deleted' ],
    },
    // 创建时间
    createDate: {
      type: Date,
      default: Date.now,
    },
    // 更新时间
    updateDate: {
      type: Date,
      required: false,
    },
  }, {
    timestamps: { createdAt: 'createDate', updatedAt: 'updateDate' },
    versionKey: false,
    collection: 'wh_goods',
    minimize: false,
    strict: true,
  });

  // 创建索引
  WhGoodsSchema.index({ goodsName: 1 });
  WhGoodsSchema.index({ categoryCode: 1 });
  WhGoodsSchema.index({ deleteFlag: 1 });
  WhGoodsSchema.index({ createDate: -1 });
  WhGoodsSchema.index({ inventoryId: 1 });

  // 查询中间件 - 默认只查询未删除的数据
  WhGoodsSchema.pre('find', function() {
    if (!this._conditions.deleteFlag) {
      this._conditions.deleteFlag = 'normal';
    }
  });

  WhGoodsSchema.pre('findOne', function() {
    if (!this._conditions.deleteFlag) {
      this._conditions.deleteFlag = 'normal';
    }
  });
  WhGoodsSchema.plugin(permissionPlugin, {
    ctx,
    fieldMapping: {
      enterpriseId: 'EnterpriseID',
    },
  });

  return mongoose.model('WhGoods', WhGoodsSchema);
};
