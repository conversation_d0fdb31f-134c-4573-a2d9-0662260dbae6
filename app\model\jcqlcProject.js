/**
 * Created by <PERSON><PERSON> on 2021/10/29.
 * 全流程数据
 */

module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const ctx = app.createAnonymousContext();
  const permissionPlugin = require('../utils/permission.js');
  const jcqlcProjectSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    publicityStatus: {
      type: Boolean,
      default: false,
    }, // 公示状态
    isSubProjects_type: {
      type: String,
      default: '',
    }, // 是否是子项目，如果是属于哪一类，Y是预采样，F是复测，B是补测，Z是子项目，正式项目则为空字符串
    pigeonholeFile: { // 归档文件
      name: { type: String, default: '' }, // 文件夹名称
      url: { type: String, default: '' }, // 文件夹下载地址
    },
    sceneInvestigationFiles: [
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        fileType: String, // 文件类型
        originName: String, // 原文件名称
        staticName: String, // 服务器文件名
        createdAt: { // 上传日期
          type: Date,
          default: Date.now,
        },
      },
    ], // 现场调查文件
    sceneTime: Date, // 现场调查时间
    sceneWordFileName: String, // 现场调查word文件名
    // ==================== 受检单位信息
    EnterpriseID: { type: String }, // 企业ID
    EnterpriseName: { type: String }, // 企业名称
    companyID: {
      type: String,
    }, // 用人单位ID ，取信用代码
    companyAddress: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      districts: Array,
      address: String, // 具体地址
    }], // 用人单位地址
    companyContact: {
      type: String,
      default: '',
    }, // 用人单位联系人
    companyContactPhoneNumber: {
      type: String,
      default: '',
    }, // 联系人手机号码
    companyContactEmail: {
      type: String,
      default: '',
    }, // 邮箱
    companyIndustry: {
      type: Array,
      default: [],
    }, // '所属行业',
    riskLevel: {
      type: String,
    }, // 风险等级，从adminorg中的level来
    districtRegAdd: {
      type: Array,
      default: [],
    }, // 注册地址
    // 申报地址，由于用户可以自行修改，所以和下面的workAdd区别开，
    // 存area_code，该code用于后注册的行政端也能读取申报的项目列表，
    // 存area_code前六位，即区县代码有效位数，其父级单位通过代码即可找到，全国的则需特殊处理
    workPlaces: [{ // 受检单位的工作场所地址
      _id: {
        type: String,
        default: shortid.generate,
      },
      workAddName: String, // 工作场所的中文地址

      name: String, // 工作场所的名称
      workAdd: {
        type: Array,
      }, // 工作场所具体地址，area_code
      checked: {
        type: Boolean,
        default: true,
      }, // 是否被检测，默认一般是检测了
    }], // 检测的工作场所，用于识别
    // ==================== 委托单位信息
    anthemEnterpriseID: {
      type: String,
    }, // 企业id
    anthemCompanyID: {
      type: String,
    }, // 委托单位ID ，取信用代码
    newAnthemCompany: {
      type: String,
    }, // 新增的委托单位
    anthemCompanyName: {
      type: String,
      default: '',
    }, // 委托单位名称
    anthemCompanyContact: {
      type: String,
      default: '',
    }, // 用人单位联系人
    anthemCompanyContactPhoneNumber: {
      type: String,
      default: '',
    }, // 联系人手机号码
    anthemCompanyContactEmail: {
      type: String,
      default: '',
    }, // 邮箱
    anthemCompanyAddress: [{
      districts: Array, // 营业执照注册地址
      address: String, // 具体地址
      _id: String,
    }], // 委托单位地址
    serviceOrgId: { type: String }, // 机构id
    serviceCon: {
      type: String,
      default: '',
    }, // 服务内容
    jobHealthId: { type: String }, // 检测项目id
    // ============================================项目相关信息
    projectName: { type: String }, // 检测项目名称
    shortProjectName: { type: String }, // 检测项目简称
    projectSN: { type: String }, // 检测项目编号
    serviceType: { type: String }, // 检测类别
    expectStartTime: {
      type: Date,
    }, // 预计开始时间
    expectStopTime: {
      type: Date,
    }, // 预计结束时间,
    requiredTime: {
      type: Date,
    }, // 要求完成的时间
    applyTime: {
      type: Date,
    }, // 上报时间
    completeTime: {
      type: Date,
    }, // 实际完成时间
    status: {
      type: Number,
      default: 0,
    }, // 申报状态,0，未报送，1，已报送，
    completeStatus: {
      type: Number,
      default: 0,
    }, // 完成状态，0，未完成； 1，已完成
    projectStop: {
      type: Boolean,
      dafault: false,
    }, // 项目是否暂停，false：未终止，true：终止
    projectCancel: {
      type: Boolean,
      dafault: false,
    }, // 项目是否终止，false：未终止，true：终止
    completeUpdate: {
      type: Number,
      default: 0,
    }, // 上报后更新状态
    InformationChange: [{
      _id: String,
      content: {
        type: Array,
        default: [],
      }, // 变更内容
      superUser: [ // 监管端id
        {
          type: String,
          ref: 'SuperUser',
        },
      ],
      nowTime: {
        type: Date,
        default: Date.now,
      }, // 变更内容
    }], // 信息变更 数组
    projectGroup: {
      type: String,
      default: '',
      ref: 'serviceDingtrees',
    }, // '项目检测组',
    subProjects_group: {
      projectGroup: [{
        type: String,
        default: '',
        ref: 'serviceDingtrees',
      }],
      personInCharge: [{
        type: String,
        default: '',
        ref: 'ServiceEmployee',
      }],
      EvaluationGroup: [{
        type: String,
        default: '',
        ref: 'serviceDingtrees',
      }],
      EvaluationProjectManagerAdmin: [{
        type: String,
        default: '',
        ref: 'ServiceEmployee',
      }],
    }, // '子项目的检测组、项目负责人、评价组、评价组经理',
    EvaluationProjectManager: [{
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }], // '评价项目成员
    EvaluationProjectManagerAdmin: {
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }, // 评价组经理
    EvaluationGroup: {
      type: String,
      default: '',
      ref: 'serviceDingtrees',
    }, // 评价组
    personInCharge: {
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }, // '项目负责人ID',
    personsOfProject: [{
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }], // '项目组成员ID',
    personsOfCompiling: [{
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }], // 编制人成员ID
    personsOfReviewer: [{
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }], // 审核人成员ID
    salesman: {
      type: String,
    },
    farmOut: { // 是否分包lht+(chenke)
      type: Boolean,
    },
    farmOutParams: { // 分包参数
      type: String,
    },
    description: { // 项目简介
      type: String,
    },
    vipRequirement: { // vip需求
      type: String,
    },
    wordFileName: { // 实验室检测报告单的word
      name: { type: String, default: '' }, // 原文件名
      url: { type: String, default: '' }, // 文件下载地址
    },
    // =============钱money
    projectPrice: {
      type: Number,
      default: 0,
    }, // 项目价格
    cooperationFee: {
      type: Number,
      default: 0,
    }, // 合作费
    reviewFee: {
      type: Number,
      default: 0,
    }, // 评审费
    otherFee: {
      type: Number,
      default: 0,
    }, // 其他费
    netEarnings: {
      type: Number,
      default: 0,
    }, // 净赚=价格-合作费-评审费-其他费
    // ============== 快递
    mailingAddress: {
      address: String,
      districts: Array,
    }, // 邮寄地址
    recipient: {
      type: String,
      default: '',
    }, // 收件人
    recipientPhoneNum: {
      type: String,
      default: '',
    }, // 收件号码

    // =============银行
    accountsBank: {
      type: String,
      default: '',
    },
    accountsAddress: {
      type: String,
      default: '',
    },
    accountsNumber: {
      type: String,
      default: '',
    },
    accountsPhoneNum: {
      type: String,
      default: '',
    },
    tariffNumber: {
      type: String,
      default: '',
    },
    year: Date, // 年度
    detectionCustom: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      fileName: {
        type: String,
        default: '',
      },
      url: {
        type: String,
        default: '',
      },
      source: {
        type: String,
        default: 'service', // super || enterprise || operate
      }, // 文件存储来源，不同端字段默认值不一样，如果查出来是null，那么就是企业端存着enterprise，这样为了兼容老数据
      // 一旦这个字段不是enterprise，那么企业端就无权修改
    }], // 自定义机构资质
    contract: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      fileName: {
        type: String,
      },
      url: {
        type: String,
      },
      source: {
        type: String,
        default: 'service', // super || enterprise || operate
      }, // 文件存储来源，不同端字段默认值不一样，如果查出来是null，那么就是企业端存着enterprise，这样为了兼容老数据
    }], // 合同书
    summary: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      fileName: {
        type: String,
      },
      url: {
        type: String,
      },
      source: {
        type: String,
        default: 'service', // super || enterprise || operate
      }, // 文件存储来源，不同端字段默认值不一样，如果查出来是null，那么就是企业端存着enterprise，这样为了兼容老数据
    }], // 汇总表
    report: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      fileName: {
        type: String,
      },
      url: {
        type: String,
      },
      source: {
        type: String,
        default: 'service', // super || enterprise || operate
      }, // 文件存储来源，不同端字段默认值不一样，如果查出来是null，那么就是企业端存着enterprise，这样为了兼容老数据
    }], // 报告书
    invoice: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      fileName: {
        type: String,
      },
      url: {
        type: String,
      },
      source: {
        type: String,
        default: 'service', // super || enterprise || operate
      }, // 文件存储来源，不同端字段默认值不一样，如果查出来是null，那么就是企业端存着enterprise，这样为了兼容老数据
    }], // 发票
    reportTime: {
      type: Date,
    }, // 检测时间
    checkPointImg: String, // 检测点分布示意图
    isVIP: { // 会员类型：战略客户、vip客户、普通客户
      type: String, // svip、vip、ordinary
      default: 'ordinary',
    },
    source: {
      type: String,
      default: 'service', // ck || service
    }, // 数据来源
    isUrgent: {
      type: Boolean,
      default: false,
    }, // 是否加急
    comment: String, // 整个项目的备注

    // 审批start=========================
    // // 合同审批状态以及审批实例id
    // approvedStatus: {
    //   type: String,
    //   default: '0',
    // }, // , 0 未发起审批 NEW：新创建 RUNNING：审批中 TERMINATED：被终止 COMPLETED：完成 CANCELED：取消 REFUSE:已拒绝
    // approvedTime: Date, // 审批时间（状态变更时都要更新）
    process_instance_id: String, // 审批编号
    approvedStartTime: Date, // 合同审批发起时间
    approvedWordFileName: String, // 生成word文件名称
    approvedFormData: {
      legitimate: String, // 是否符合法律法规的要求
      qualificationOK: String, // 本公司和服务人员资质是否满足要求
      instrumentOK: String, // 检测检验设备是否满足要求
      standardOK: String, // 检测检验方法、标准适用
      isPackage: String, // 是否分包
      packageInfo: String, // 分包具体项目
      packageOrg: String, // 拟选分包机构
      packageBeInformed: String, // 合同或协议是否已明确分包内容并告知客户
      controllableRisk: String, // 技术风险和商业风险是否可控
      chargeOK: String, // 项目收费是否能满足工作要求
      progressOK: String, // 能否按合同要求进度完成项目
      logisticsOK: String, // 后勤保障能否满足要求
      secrecyOK: String, // 是否满足项目和合同保密要求
    },
    approvedComment: String, // 最总评审结论
    approvedUsers: [{ // 所有评审人员,评审负责人：如果项目已经完成了，那么就是最后一个人
      _id: String,
      signPath: String, // 签字
      // name:String,//名字
      serviceEmployeeId: String,
      userId: String, // 钉钉id
    }],

    // 报告审批状态以及审批实例id
    reportProcessInstanceId: String, // 报告单审批实例id

    // reportApprovedStatus: { type: String }, // 报告单审批状态，同上
    // reportApprovedTime: Date, // 报告单审批时间（状态变更时都要更新）
    reportApprovedStartTime: Date, // 报告单审批发起时间

    reportFinalProcessInstanceId: String, // 报告审批实例id
    // reportFinalApprovedStatus: { type: String, default: '0' }, // 报告审批状态
    //  0 未发起审批 NEW：新创建 RUNNING：审批中 TERMINATED：被终止 COMPLETED：完成 CANCELED：取消 REFUSE:已拒绝
    // reportFinalApprovedTime: Date, // 报告审核时间（状态变更时都要更新）
    // finishTime: Date, // 报告审核完成时间
    approvalPerson: String, // 报告审核批准人（通过钉钉接口获取的钉钉通讯录的姓名）
    // 审批 end========================
    // checkResultStatus: Number, // 检测结果计算状态 0 未计算 1 已计算

    // sampleList: { default: false, type: Boolean }, // 交样单是否完成

    // reportStatus: { // 报告生成状态 0未生成  1已生成  2已上传
    //   type: String,
    //   default: '0',
    // },
    reportGenerateFileName: { // 生成报告文件名
      type: String,
      default: '',
    },
    reportUploadFile: { // 上传报告文件
      name: { type: String, default: '' }, // 原文件名
      url: { type: String, default: '' }, // 文件下载地址
    },
    officialReportUploadFile: { // 正式稿文件
      name: { type: String, default: '' }, // 原文件名
      url: { type: String, default: '' }, // 文件下载地址
    },
    // 职位实验室记录单 jhw++
    reportRecordUploadFile: { // 职位记录上传
      staticName: { type: String },
      url: { type: String },
    },

    labReportRecordUploadFile: { // 实验室记录上传
      staticName: { type: String },
      url: { type: String },
    },

    // 实验室相关资料 jhw ++
    sampleListFile: { // 收样单
      staticName: { type: String },
      url: { type: String },
    },
    labRecordFile: { // 实验室原始记录单
      staticName: { type: String },
      url: { type: String },
    },
    labSampleScheduleFile: { // 排样单
      staticName: { type: String },
      url: { type: String },
    },
    // end
    serviceArea: [{
      type: String,
      match: /^[0-9]*$/,
    }], // 关于此次项目的企业涉及的技术服务领域，存得是编码，报送信息中要用 [采矿业:'1','化工、石化及医药':'2','冶金、建材':'3','机械制造、电力、纺织、建筑和交通运输等行业领域':'4','核设施':'5','核技术应用':'6']
    detectionTime: Date, // 现场采样/检测时间
    serviceMatters: [[ String ]], // 项目组成员的服务事项
    entrustDate: { type: Date }, // 委托时间 发起合同审核的时间，如果是老项目,委托时间设为检测时间的前一个月
    schemeSampleDate: { type: Date }, // 方案的采样开始日期
    samplingDays: { type: Number, default: 1 }, // 一批样品需要采样的天数
    samplePrepareId: { type: String }, // 样品备样单Id
    instrumentsApplyId: { type: String }, // 仪器申请Id
    // isFinish: { // 项目是否已结束，若已经结束那么这个项目的所有阶段数据不可再更改，默认为false，未结束
    //   type: Boolean,
    //   default: false,
    // },
    labReportAuthor: { type: String }, // 实验室检测报告单编制人
    labBasicsInfo: [
      { // 实验室原始记录单每个参数的基础信息
        sample_date: Date, // 采样日期
        inspector: String, // 检测人员
        reviewer: String, // 审核人
        dustComment: String, // 粉尘备注
        SampleAnalyseNumber: { // 单个危害因素分析的样品数量
          type: Number,
          default: 0,
        },
        allSampleAnalyseNumber: { // 单个危害因素分析的样品总数量
          type: Number,
          default: 0,
        },
        batch: Number, // 批次

        QualityControlSampleId: { // 质控样
          type: String,
          ref: 'labInfoCopys',
        },
        StandardSolutionsId: { // 标准液
          type: String,
          ref: 'labInfoCopys',
        },

        curveManageId: String, // 曲线id
        Quality_records: Object, // 质控记录 GLC的记录

        parameter: String, // 参数(危害因素)
        sample_count: String, // 样品数量
        TTD: Date, // 检测时间
        sample_send_time: Date, // 送样时间 (sample_send_time)
        sample_make_date: String, // 质控样品配置信息  (sample_make_date)
        by_make_date: Date, // 标液配置日期 (标准储备液 by_make_date)
        curve_date: Date, // 标准曲线制备日期

        sample_temperature: String, // 做样温度
        sample_humidity: String, // 做样湿度

        comment: String, // 备注

        equipment: Array, // 仪器 目前只有气相有用到
        detectTime: Array, // 设备分析时间

        // AFS
        fluorescenceIntensity: String, // 标准曲线荧光强度


        // DUST
        Preparing_temperature: String, // 备样温度
        Preparing_humidity: String, // 备样湿度


        // AAS
        actual_measured_value: String, // 实测值

        // FG
        Abs_volume: String, // 吸收液体积
        take_sample_volume: String, // 取样体积
        QC_detection: String, // 质控样品检测值

        // ISE
        potential_value_f: String, // 标准曲线电位值

        // 游离二氧化硅
        baking_temperature: String, // 烘干温度
        ashing_temperature: String, // 灰化温度

        // GLC
        DPI: String, // 解析率

        // 油雾
        flag_volume: String, // 标杆体积

        unox_QC_Abs: String,
        unox_equation_regression: String, // 回归方程
        unox_coefficient_association: String, // 相关系数
        unox_slope: String, // 斜率
        unox_intercept: String, // 截距

        QC_Abs2: String, // 质控样吸光度 气象
        QC_Abs: String, // 标液吸光度  FG用来制作曲线 AAS也用来制作曲线

        createAt: { // 创建日期
          type: Date,
          default: new Date(),
        },
      },
    ],
    subProjects: [ // 项目-子项目-批次的基本信息
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        batch: Number, // 批次
        expectTimeStart: Date, // 预取开始时间
        expectTimeEnd: Date, // 预取结束时间
        samplingTime: Date, // 实际采样开始时间
        weatherScene: { // 天气
          weather: String, // 天气情况
          pressure: String, // 气压
          temperature: String, // 温度
          humidity: String, // 湿度
        },
        calibration: [{
          _id: {
            type: String,
            default: shortid.generate,
          },
          instrumentID: String, // 仪器id
          internalNumber: String, // 仪器编号
          standardSoundSource: String, // 标准声源
          soundSourceSN: String, // 声源编号
          calibrationValue: String, // 声源编号
          deviationValue: String, // 偏差值
          staff: String, // 校准人
          isCalibration: Boolean, // 是否校准
          calibrationTime: Date, // 校准时间
        }], //
        samplingCompletedStatus: { // 该批现场检测流转单完成状态
          type: Number, // 0 未完成 1 进行中 2 已完成
          default: 0,
        },
        samplingCompletedTime: Date, // 该批现场检测流转单提交时间
        spotProjectCompletedStatus: { // 该批现场检测完成状态
          type: Number, // 0 未完成 1 进行中 2 已完成
          default: 0,
        },
        spotProjectCompletedTime: Date, // 该批现场检测完成时间
        signManage: [{
          _id: {
            type: String,
            default: shortid.generate,
          },
          signRole: String, // 陪同人 采样人 测量人
          name: String, // 姓名
          signPath: String, // 签名文件储存位置
          signTime: Date, // 签名时间
        }], // 签名
        // 后续拓展 实验室 相关 交样收样字段
        scenePhotos: [
          {
            _id: {
              type: String,
              default: shortid.generate,
            },
            fileName: String,
            src: String,
            describe: String,
            // station: String, // 岗位名称
            stationId: String, // 岗位id
            // workPlace: String, // 车间名称
            workspaceId: String, // 车间id
            createTime: Date,
          },
        ],
        // 流转单 ++
        jcUser: String, // 送样人 ==> 点完成检测的人
        sampleManager: String, // 流转单确认人
        samplingConfirmDate: Date, // 流转单确认时间
        sampleListIsReceived: { // 流转单是否接收
          type: Boolean,
          default: false,
        },
        receiveManager: String, // 实验室接受样品人
        samplingReceiveDate: Date, // 样品接收时间
        labSampleListIsReceived: { // 收样单是否接收
          type: Boolean,
          default: false,
        },
      },
    ],
    completeReportArchive: {
      status: { // 0 未完成 1 已完成
        type: Number,
        default: 0,
      }, // 是否确认签收归档 默认不签收0 已经签收1
      completedTime: Date, // 完成时间
    },
    progress: { // 进度
      // 项目创建时间
      createProject_time: {
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 2,
        },
        completedTime: {
          type: Date,
          default: Date.now,
        },
      },
      // 合同评审状态
      approved: {
        status: { // 0 未创建 1 进行中（审批中） 2 已完成（已同意）3 已拒绝  4 被终止  5 已取消
          type: Number,
          default: 0,
        },
        completedTime: Date, // 状态变化时间
        records: [], // 钉钉审批记录
      },
      // 评价组分配状态
      ProjectPj: {
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 状态变化时间
      },
      // 评价项目经理分配状态
      EvaluationProjectManagerAdmin: {
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 状态变化时间
      },
      // 检测组分配状态
      projectGroup: {
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      // 检测项目经理分配状态
      PersonInCharge: {
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      // 现场调查状态
      scene: {
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      // 采样方案整体完成进度
      samplingSchemes: {
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      // 采样方计划单整体完成进度
      samplingPlans: {
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      samplePrepare: { // 样品备样单
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      instrumentsApply: { // 仪器申请单
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      // 现场记录单整体完成进度
      spotRecord: {
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      sampleList: { // 检测交样
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      labSampleList: { // 实验室收样
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      labRecord: { // 实验室分析
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      labTestReport: { // 实验室检测报告
        status: { // 0 未保存 1 进行中 2 已保存
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      // 报告单审批状态
      reportApproved: {
        status: { // 0 未创建 1 进行中（审批中） 2 已完成（已同意）3 已拒绝  4 被终止
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
        records: [], // 钉钉审批记录
      },
      // 检测结果计算状态
      checkResult: {
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      reviewReport: { // 检评报告(初稿？待定)
        status: { // 0 未保存 1 进行中 2 已保存
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      // 检评报告审批状态
      reportFinalApproved: {
        status: { // 0 未创建 1 进行中（审批中） 2 已完成（已同意）3 已拒绝  4 被终止 5 取消
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
        records: [], // 钉钉审批记录
      },
      officialReport: { // 正式稿
        status: { // 0 未保存 1 进行中 2 已保存
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      reportPrint: { // 报告打印
        status: { // 0 未进行 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      reportArchive: { // 报告归档
        status: { // 0 未进行 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      projectPjFirstReview: { // 评价项目报告初审
        status: { // 0 未进行 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      projectPjThreeReview: { // 评价报告三审
        status: { // 0 未进行 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      projectPjProfessorReview: { // 评价报告专家审
        status: { // 0 未进行 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
    },

    allSampleNumber: { // 整个项目样品总个数
      type: Number,
      default: 0,
    },
    top: { // 实验室列表一键置顶
      default: 0,
      type: Number,
    },
    allSampleAnalyseNumber: { // 实验室分析的样品数量
      type: Number,
      default: 0,
    },
    station: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      workTimeWeek: {
        type: Number,
        default: 0,
      }, // 每周工作天数(天)
      time: {
        type: Number,
        default: 0,
      }, // 每班接触时间
      workShopName: String, // 厂房
      workShopId: String, // 厂房Id
      workspaceName: String, // 车间
      workspaceId: String, // 车间Id
      oldStationName: String, // 在点数变多之前的岗位名称，方案要用到
      stationName: String, // 岗位
      stationId: String, // 岗位Id
      workTypeName: String, // 工种
      pointNum: Number, // 测点编号

      productionStatus: {
        type: Array,
        default: [ 1, 1 ],
      }, // 生产状况
      protecFacilitieRun: {
        type: String,
        default: '是',
      }, // 防护设置是否运行
      personalProtecRun: {
        type: String,
        default: '是',
      }, // 个人是否运行

      isCounts: {
        type: Number,
        default: 0,
      }, // 是否是多个点生成的岗位。方案需要根据该字段判断是否需要显示，0表示是第一个点的，方案需要显示，大于0则表示是多个点的，方案不显示。方案后的流程正常获取数据
      harmFactors: [
        {
          _id: {
            type: String,
            default: shortid.generate,
          },
          category: String, // 危害因素所属类别   粉尘/物理等
          harmFactorsName: String, // 危害因素字符串格式
          harmFactorsNameArray: [{ // 危害因素数组格式
            _id: {
              type: String,
              default: shortid.generate,
            },
            labLeftNum: {
              type: Number,
              default: 0,
            },
            name: String, // 危害因素名 如果有总呼的话会带（总尘）/（呼尘）
            harmFactorId: String, // 危害因素id
            oelId: String, // 大表限值名
            curveId: String, // 曲线
            QualityControlSampleId: String, // 质控样id
            StandardSolutionsId: String, // 标准溶液id
            labInstrument: Array, // 实验室仪器
            temperature: String, // 温度
            humidity: String, // 湿度
            analyzeDuration: Array, // 实验室分析时间段

            // 流转单 ++
            sampleListNote: String, // 备注
            // 检测结果计算与判定化学 ++++
            // chemistryCheckResult: [{
            //   MAC: String,
            //   TWA: String,
            //   STEL: String,
            //   PE: String,
            //   batch: { type: Number }, // 批次
            //   smaplingTime: Date, // 检测时间
            //   smaplingTimeFormat: String, // 检测时间格式化好的时间
            //   PC_MAC: String, // MAC限值
            //   PC_TWA: String, // twa限值
            //   PC_STEL: String, // stel限值
            //   PC_PE: String, // pe限值
            //   checkResult: String, // 结果判定
            // }],
          }],
          count: String, // 点数
          sampleNumber: String, // 样品数
          dayNumber: String, // 天数
          sampleMode: String, // 采样方式，定点/个体
          sampleFrame: String, // 采样时机/时段
          sampleTime: String, // 采样时间
          sampleFlow: String, // 采样流量
          airCollector: String, // 空气收集器
          sampleEquipment: String, // 采样设备
          sampleStorage: String, // 样品保存期限和保存条件
          remark: String, // 备注
          individual: { type: Boolean, default: false }, // 判断该危害因素是否生成了个体采样
          dustOriginName: [{
            workShopId: String, // 厂房
            workspaceId: String, // 车间
            stationId: String, // 岗位
            harmFactorsName: String, // 检测项目
          }], // 关联到这个游离sio2的粉尘的数据
          spotRecordSort: String, // 类型
          stationsList: [ // 个体对应的岗位列表
            {
              stationName: String, // 岗位
              stationId: String, // 岗位Id
            },
          ],
          sample: [
            {
              _id: {
                type: String,
                default: shortid.generate,
              },
              planSampleDate: {
                type: Date,
              }, // 计划采样日期
              batch: {
                type: Number,
              }, // 批次
              subProjectsId: { type: String }, // 最外层subProjects字段对应批次的id
              sampleSN: {
                type: String,
              }, // 样品编号
              isBlankSample: { type: Boolean }, // 是否是空白样品，true是，false不是

              // 现场记录单 ++++++++++++++++++++++++++
              // samplingConfirmDate: Date, // 交样日期
              // compare_date: Date, // 收样日期
              // isComfirm: { Boolean, default: false }, // 是否确认流转单
              // isReceived: { Boolean, default: false }, // 是否确认收样单
              instrument: [{
                instrumentName: String, // 仪器名
                internalNumber: String, // 仪器编号
                instrumentID: String, // 仪器id
                instrumentModel: String, // 仪器型号
                _id: {
                  type: String,
                  default: shortid.generate,
                },
              }], // 仪器
              samplingPlace: String,
              samplingFlow: String, // 采样流量 - 定点
              samplingFlowStart: String, // 采样前流量 - 个体
              samplingFlowEnd: String, // 采样后流量 - 个体

              smaplingTimeStart: Date, // 采样起始时间
              smaplingTimeEnd: Date, // 采样结束时间

              smaplingTimeSlot: String, // 采样时间段 （仅限 定点 存在）个体噪声

              temperature: String, // 温度
              humidity: String, // 湿度

              wearerName: String, // 佩戴人姓名/作业人员姓名（个体 存在）
              isComplete: {
                type: Boolean,
                default: false,
              }, // 是否完成
              measureTime: Date, // 测量or采样时间
              contactPosition: String, // 接触位置
              contactTime: String, // 接触时间 - 高温 高频电场 手传振动 个体噪声
              contactTimes: {
                type: Array,
                default: undefined,
              }, // 接触时间 h/d d/w
              WBGTValues: {
                type: Array,
                default: undefined,
              }, // WBGT指数(℃)2
              WBGT: String, // WBGT指数(℃)
              WBGTAvg: String, // WBGT指数平均值(℃)
              WBGTWeight: String, // 加权WBGT指数

              measureValue: {
                type: Array,
                default: undefined,
              }, // 测量值 - 工频电场 手传振动 照度
              measureAvg: String, // 平均值
              measureMax: String, // 最大值 - 工频电场
              contactPersons: String, // 接触人数

              CTWA: String, // CTWA结果 Co
              CSTEL: String, // CSTEL Co
              measureValueC1: String, // 测量值C1
              measureValueC2: String, // 标况浓度

              windSpeed: String, // 风速
              pressure: String, // 气压

              ahw: String, //
              measurePosition: String, // 测量方位
              vibrateSpeedWeight: String, // 4h加权振动加速度

              eyeMeasureValue: {
                type: Array,
                default: undefined,
              }, // 眼部测量值
              eyeEffectValue: String, // 眼部有效辐照度
              faceMeasureValue: {
                type: Array,
                default: undefined,
              }, // 面部测量值
              faceEffectValue: String, // 面部有效辐照度
              limbsMeasureValue: {
                type: Array,
                default: undefined,
              }, // 肢体测量值
              limbsEffectValue: String, // 肢体有效辐照度
              otherMeasureValue: {
                type: Array,
                default: undefined,
              }, // 其他测量值
              otherEffectValue: String, // 其他有效辐照度

              lightSource: String, // 光源

              measureHeight: String, // 高度

              LAeqValue: String, // LAeq 检测值
              LEXValue: String, // LEX 等效声级

              nosiseType: String, // 噪声类型

              exhaustHoodType: String, // 排风罩形式
              coverArea: String, // 罩口面积

              waveType: String, // 波形
              waveMode: String, // 辐射方式
              headMeasureValue: String, // 头部测量值
              chestMeasureValue: String, // 胸部测量值
              abdomenMeasureValue: String, // 腹部测量值
              localMeasureValue: String, // 局部测量值

              magneticMeasureValue: {
                type: Array,
                default: undefined,
              }, // 磁场测量值
              magneticAvg: String, // 磁场测量平均值
              electricMeasureValue: {
                type: Array,
                default: undefined,
              }, // 电场测量值
              electricAvg: String, // 电场测量值平均值

              measureValueAndTime: {
                type: Array,
                default: undefined,
              }, // 测量时间与值

              pointImg: String, // 检查点示意图

              laserModel: String, // 激光器型号
              laserWave: String, // 激光波长
              exposure: String, // 照射量
              exposureTime: String, // 照射时间
              irradiance: String, // 辐照度

              equipment: String, // 被测设备名称 - 高频和超高频
              frequency: String, // 被测设备参数/频率 - 高频和超高频
              posture: String, // 操作姿势 超高频

              laborIntensity: String, // 劳动强度
              protectiveMeasures: String, // 防护措施
              remarks: String, // 备注

              samplingStaff: String, // 采样人员

              needUpdateSampleList: Boolean, // 是否是交样后又重新修改的

              // 现场记录单 ++++++++++++++++++++++++++

              // 物理检测结果与判定++
              samplingTime: Date,
              touchLimit: { type: String, default: '85' }, // 职业接触限值 噪声、高温（℃）、工频电场
              // -------------------高温start-------------------
              touchTime: { type: String }, // 接触时间  高温、手传振动、工频电场  contactTime 如果没写单位那么单位为h
              contactTimeRate: String, // 接触时间率
              // labourIntensity: { type: String }, // 体力劳动强度 laborIntensity
              // -------------------高温end-------------------

              // -------------------手传振动start-------------------
              // dayTouchTime: { type: String }, // 日接触时间 ,workDay
              // fourHoursAccelerated: String, // 4h等能量频率计权振动加速度值（m/s2） fourHoursAccelerated
              // -------------------手传振动end-------------------

              // -------------------紫外辐射start-------------------
              measureDatas: [{
                _id: String,
                checkPosition: String, // 检测部位
                irradiance: String, // 有效辐照度
              }], // 检测数据
              // measurePosition: String, // 测量方向 measurePosition
              eightHoursTouchLimit: String, // 8h职业接触限值（μW/cm2）
              // -------------------紫外辐射end-------------------

              // -------------------激光辐射start-------------------
              // irradiance: String, // 有效辐照度 irradiance
              // laserWave: String, // 激光波长
              laserMeasureValue: Array, // 检测值 measureValueAndTime.map(item => item.measureValue)
              // testAverage: String, // 平均检测值（W/cm2） measureAvg
              // -------------------激光辐射end-------------------

              // -------------------工频电场start-------------------
              //  electricIntensity: Array, // 电场强度测量值（kV/m） dataItem.measureValue

              // -------------------工频电场end-------------------

              // -------------------高频电磁场start-------------------
              pc_electricIntensity: String, // 电场强度（V/m）限值
              // electricIntensityData: Array, // 电场强度测量值（V/m） electricMeasureValue
              pc_magneticIntensity: String, // 磁场强度（A/m）限值
              // magneticIntensityData: Array, // 磁场强度测量值（A/m）magneticMeasureValue
              // -------------------高频电磁场end-------------------

              // -------------------微波辐射start-------------------
              shortTimeContactPowerDensity: String, // 短时间接触功率密度最大值（mW/cm2）
              shortTimeLimit: String, // 短时间接触功率密度接触限值（mW/cm2）
              // average: String, // 平均值（μW/cm2） measureAvg
              averagePowerDensityLimit: String, // 8h平均功率密度接触限值（μW/cm2）
              // -------------------微波辐射end-------------------

              // 收样单 ++
              sampleIsReceive: { type: Boolean, default: false },

              // 实验室++
              chromatogramsPdf: {
                originName: {
                  type: String,
                },
                staticName: {
                  type: String,
                },
                url: {
                  type: String,
                },
              }, // 图谱pdf文件
              chromatogramsXml: {
                originName: {
                  type: String,
                },
                staticName: {
                  type: String,
                },
                url: {
                  type: String,
                },
              }, // 图谱xml文件
              lab: [{
                _id: {
                  type: String,
                  default: shortid.generate,
                },
                // 通用
                harmFactor: String,
                Abs: String, // 吸光值
                uno_Abs: String, // 不氧化吸光值
                sample_content: String, // 样品含量
                sample_volume: String, // 采样体积
                conversion_coefficient: String, // 转换系数
                air_concentration: String, // 空气中浓度
                duration_of_exposure: String, // 接触时间
                comment: String, // 备注  稀释倍数
                // ISE 电极
                potential_value: String, // 电位值
                sample_content_log: String, // 样品溶液中含量对应对数值
                sample_content_ise: String, // 样品溶液中浓度

                // AFS
                emptyFluorescenceIntensity: String,
                compared_emptyFluorescenceIntensity: String,


                // AAS 原子吸收法
                sample_solution: String, // 样品溶液浓度

                // DUST 粉尘
                before_volume: String, // 采样前滤膜重
                after_volume: String, // 采样后滤膜重
                extra_volume: String, // 滤膜增重

                // 石蜡烟
                empty_beaker_weight: String,
                volatilize_sample_weigth: String,
                empty_beaker_weighting: String,

                // GLC 气相
                peakArea_front: String, // 峰面积前段
                peakArea_back: String, // 峰面积后段

                peakArea_front2: String, // 2-丁氧基乙醇 用到
                peakArea_back2: String,

                subtract_peakArea: String, // 减空白后峰面积
                sample_concentration: String, // 样品浓度

                // FSiO2 游离二氧化硅
                sample_weight: String, // 样品质量G
                crucible_weight: String, // 坩埚质量m1
                ashing_crucible_weight: String, // 灰化后坩埚加沉渣质量m2
                acid_crucible_weight: String, // 氢氟酸处理后坩埚加沉渣质量m3
                SiO2_concentration: String, // 游离二氧化硅含量SiO2

                // YW 油雾
                filter_concentration1: String, // 滤膜清洗液油份浓度
                filter_concentration2: String, // 滤膜清洗液油雾浓度


                // IC
                peakArea_IC: String,
                subtract_peakArea_IC: String,
                sample_concentration_IC: String,

                excursion_limit: String, // 超限倍数
              }],

            },
          ],
        },
      ],
    }],
  });

  // 实验室查询优化 jhw
  jcqlcProjectSchema.index({ projectSN: 1 });
  jcqlcProjectSchema.index({ category: 1, sampleFlow: 1 });

  // jcqlcProjectSchema.index({ "station.harmFactors.harmFactorsNameArray.name": 1});
  // lab end

  jcqlcProjectSchema.index({ projectGroup: 1 });
  jcqlcProjectSchema.index({ EvaluationProjectManager: 1 });
  jcqlcProjectSchema.index({ EvaluationProjectManagerAdmin: 1 });
  jcqlcProjectSchema.index({ EvaluationGroup: 1 });
  jcqlcProjectSchema.index({ personInCharge: 1 });
  jcqlcProjectSchema.index({ approvedStatus: 1 });
  jcqlcProjectSchema.index({ reportApprovedStatus: 1 });
  jcqlcProjectSchema.index({ reportFinalApprovedStatus: 1 });
  jcqlcProjectSchema.index({ 'progress.createProject_time.completedTime': 1 });
  jcqlcProjectSchema.index({ 'station.harmFactors.sample.planSampleDate': 1 });
  jcqlcProjectSchema.index({ 'station.harmFactors.sample.batch': 1 });
  jcqlcProjectSchema.index({ 'station.harmFactors.sample.sampleSN': 1 });

  jcqlcProjectSchema.plugin(permissionPlugin, {
    ctx,
    fieldMapping: {
      enterpriseId: 'EnterpriseID',
    },
  });
  return mongoose.model('JcqlcProject', jcqlcProjectSchema, 'jcqlcProject');
};
