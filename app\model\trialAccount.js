/**
 * Created by xxn on 2022/4/22.
 * 试用账号
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const CryptoJS = require('crypto-js');

  const TrialAccountSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    Enterprise: String, // 企业名称
    name: String, // 姓名
    phoneNum: { // 手机号
      type: String,
      trim: true,
      require: true,
    },
    countryCode: { // 手机号前国家代码
      type: String,
      default: '86',
    },
    userName: String, // 用户名
    password: { // 账户密码
      type: String,
      set(val) {
        return val && val.length > 30 ? val : CryptoJS.SHA256(val + app.config.salt_sha2_key).toString();
      },
    },
    channel: { // 了解渠道
      type: Number,
    },
    userModel: { // 绑定的用户表model,企业端肯定就是AdminUser
      type: String,
      default: 'AdminUser',
    },
    userId: String, // 绑定的用户id, 审核的时候分配
    applyDate: { // 申请日期
      type: Date,
      default: Date.now,
    },
    auditDate: Date, // 审核日期
    effectiveDate: { // 试用有效期至
      type: Date,
      require: true,
    },
    reviewer: [{ // 审核人
      type: String,
      ref: 'OperateUser',
    }],
    remark: String, // 备注/驳回理由
    status: { // 状态
      type: Number,
      default: 0, // 0未审核  1审核通过 2审核未通过
    },
    enable: { // 是否可用
      type: Boolean,
      default: false,
    },
  });

  TrialAccountSchema.index({ phoneNum: -1 }, { unique: true });

  return mongoose.model('TrialAccount', TrialAccountSchema, 'trialAccount');
};
