const Controller = require('egg').Controller;

// const {
//   superUserRule,
// } = require('@validate');
// const {
//   validatorUtil,
// } = require('@utils');

// const _ = require('lodash');
const moment = require('moment');


class QuestionnaireController extends Controller {
  // 新增问卷
  async addNewQuestionnaire() {
    const { ctx } = this;
    try {
      const { title, description } = ctx.request.body;
      const { _id, EnterpriseID } = ctx.session.adminUserInfo;
      const companies = await ctx.service.db.find('Adminorg', { _id: EnterpriseID });
      const cname = companies[0].cname;

      const data = await ctx.curl(
        `${this.config.iServiceHost}/questionnaire/createQuestionnaire`,
        {
          method: 'POST',
          dataType: 'json', // 返回的数据类型
          headers: {
            'Content-Type': 'application/json',
          },
          data: JSON.stringify({
            title,
            description,
            userId: _id,
            Enterprises: [
              {
                EnterpriseID,
                cname,
              },
            ],
          }),
        }
      );
      ctx.helper.renderSuccess(ctx, {
        data: data.data.data.res,
        status: data.status,
        message: data.data.message,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  // 获取问卷列表
  async getQuestionnaireList() {
    const { ctx } = this;
    try {
      const {
        searchKey = '',
        searchDate = [],
        currentPage = 1,
        pageSize = 10,
      } = ctx.request.body;
      const { _id } = ctx.session.adminUserInfo;
      const query = {
        userId: _id,
        searchDate,
        currentPage,
        pageSize,
        searchKey,
      };
      const list = await ctx.curl(
        `${this.config.iServiceHost}/questionnaire/getQuestionnaireList`,
        {
          method: 'POST',
          dataType: 'json', // 返回的数据类型
          data: query,
        }
      );
      list.data.data.list = list.data.data.list.map(item => {
        return {
          ...item,
          deadline: moment(item.deadline).format('YYYY-MM-DD'),
          createdAt: moment(item.createdAt).format('YYYY-MM-DD'),
        };
      });
      //   console.log('list', list);
      ctx.helper.renderSuccess(ctx, {
        data: list.data.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 修改问卷发布状态
  async editPublishStatus() {
    const { ctx, service } = this;
    try {
      const list = await ctx.curl(
        `${this.config.iServiceHost}/questionnaire/editPublishStatus`,
        {
          method: 'POST',
          dataType: 'json', // 返回的数据类型
          data: ctx.request.body,
        }
      );
      if (ctx.request.body.publishStatus === 1) {
        const { enterprises } = ctx.request.body;
        const companyId = enterprises.map(item => item.EnterpriseID);
        const userIds = await this.ctx.service.db.find('User', { companyId: { $in: companyId } }, { _id: 1 });
        const readerIds = userIds.map(user => ({
          readerID: user._id,
          readerGroup: '',
          isRead: 0,
        }));
        const messageNotice = {
          title: `请填写${ctx.request.body.title}问卷调查`,
          message: ctx.request.body.description,
          reader: readerIds,
          sendWay: 'systemMessage',
          templateCode: '',
          type: 1,
          SignName: '',
          files: [],
          authorID: ctx.session.adminUserInfo._id,
          authorGroup: ctx.session.adminUserInfo.group,
          informer: ctx.session.adminUserInfo.name || '', // 发通知的人名
        };
        await service.messageNotification.sendMessageToUser(messageNotice);
      }
      ctx.helper.renderSuccess(ctx, {
        data: {},
        status: list.status,
        message: '修改成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 删除问卷
  async delQuestionnaire() {
    const { ctx } = this;
    try {
      const list = await ctx.curl(
        `${this.config.iServiceHost}/questionnaire/delQuestionnaire`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          dataType: 'json', // 返回的数据类型
          data: JSON.stringify(ctx.request.body),
        }
      );
      ctx.helper.renderSuccess(ctx, {
        status: list.status,
        message: '删除成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 获取指定问卷详细信息
  async getQuestionnaireDetailById() {
    const { ctx } = this;
    try {
      const list = await ctx.curl(
        `${this.config.iServiceHost}/questionnaire/getQuestionnaireDetailById`,
        {
          method: 'POST',
          dataType: 'json', // 返回的数据类型
          data: ctx.request.body,
        }
      );
      ctx.helper.renderSuccess(ctx, {
        status: list.status,
        data: list.data.data,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 保存指定问卷详细信息
  async saveQuestionnaireDetail() {
    const { ctx, service } = this;
    try {
      const enterprises = ctx.request.body[ctx.request.body.length - 2];
      if (enterprises.publishStatus === 1) {
        const companyId = enterprises.investigated.map(
          item => item.EnterpriseID
        );
        const userIds = await this.ctx.service.db.find('User', { companyId: { $in: companyId } }, { _id: 1 });
        const readerIds = userIds.map(user => ({
          readerID: user._id,
          readerGroup: '',
          isRead: 0,
        }));
        const messageNotice = {
          title: `请填写${
            ctx.request.body[ctx.request.body.length - 2].title
          }问卷调查`,
          message: ctx.request.body[ctx.request.body.length - 2].description,
          reader: readerIds,
          sendWay: 'systemMessage',
          templateCode: '',
          type: 1,
          SignName: '',
          files: [],
          authorID: ctx.session.adminUserInfo._id,
          authorGroup: ctx.session.adminUserInfo.group,
          informer: ctx.session.adminUserInfo.name || '', // 发通知的人名
        };
        await service.messageNotification.sendMessageToUser(messageNotice);
      }
      const list = await ctx.curl(
        `${this.config.iServiceHost}/questionnaire/saveQuestionnaireDetail`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          dataType: 'json', // 返回的数据类型
          data: JSON.stringify(ctx.request.body),
        }
      );
      ctx.helper.renderSuccess(ctx, {
        status: list.status,
        data: '保存成功',
        message: '保存成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 设置问卷为模板
  async setQuestionnaireTemplate() {
    const { ctx } = this;
    try {
      const { _id, EnterpriseID } = ctx.session.adminUserInfo;
      ctx.request.body.userId = _id;
      ctx.request.body.EnterpriseID = EnterpriseID;
      // 根据_id查找adminUser表中符合的_id文档中的userName
      // const { name } = await ctx.model.AdminUser.findOne(
      const { name } = await ctx.service.db.findOne(
        'AdminUser',
        { _id },
        { name: 1, _id: 0 }
      );
      ctx.request.body.userName = name;
      const list = await ctx.curl(
        `${this.config.iServiceHost}/questionnaire/setQuestionnaireTemplate`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          dataType: 'json', // 返回的数据类型
          data: JSON.stringify(ctx.request.body),
        }
      );
      ctx.helper.renderSuccess(ctx, {
        status: list.status,
        data: '设置成功',
        message: '设置成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 获取问卷模板列表
  async getQuestionnaireTemplateList() {
    const { ctx } = this;
    try {
      const {
        searchKey = '',
        searchDate = [],
        currentPage = 1,
        pageSize = 10,
      } = ctx.request.body;
      const { _id } = ctx.session.adminUserInfo;
      const query = {
        userId: _id,
        searchDate,
        currentPage,
        pageSize,
        searchKey,
      };
      const list = await ctx.curl(
        `${this.config.iServiceHost}/questionnaire/getQuestionnaireTemplateList`,
        {
          method: 'POST',
          dataType: 'json', // 返回的数据类型
          data: query,
        }
      );
      list.data.data.list = list.data.data.list.map(item => {
        return {
          ...item,
          createdAt: moment(item.createdAt).format('YYYY-MM-DD HH:mm:ss'),
        };
      });
      //   console.log('list', list);
      ctx.helper.renderSuccess(ctx, {
        data: list.data.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 新增模板问卷
  async addNewQuestionnaireTmp() {
    const { ctx } = this;
    try {
      const { title, description } = ctx.request.body;
      const { _id, EnterpriseID } = ctx.session.adminUserInfo;
      // const { name } = await ctx.model.AdminUser.findOne(
      const { name } = await ctx.service.db.findOne(
        'AdminUser',
        { _id },
        { name: 1, _id: 0 }
      );
      const data = await ctx.curl(
        `${this.config.iServiceHost}/questionnaire/addNewQuestionnaireTmp`,
        {
          method: 'POST',
          dataType: 'json', // 返回的数据类型
          data: {
            title,
            description,
            userId: _id,
            EnterpriseID,
            userName: name,
          },
        }
      );
      ctx.helper.renderSuccess(ctx, {
        data: data.data.data.res,
        status: data.status,
        message: data.data.message,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  // 获取指定模板问卷详细信息
  async getQuestionnaireTmpById() {
    const { ctx } = this;
    try {
      const list = await ctx.curl(
        `${this.config.iServiceHost}/questionnaire/getQuestionnaireTmpById`,
        {
          method: 'POST',
          dataType: 'json', // 返回的数据类型
          data: ctx.request.body,
        }
      );
      ctx.helper.renderSuccess(ctx, {
        status: list.status,
        data: list.data.data,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 删除模板问卷
  async delQuestionnaireTmp() {
    const { ctx } = this;
    try {
      const list = await ctx.curl(
        `${this.config.iServiceHost}/questionnaire/delQuestionnaireTmp`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          dataType: 'json', // 返回的数据类型
          data: JSON.stringify(ctx.request.body),
        }
      );
      ctx.helper.renderSuccess(ctx, {
        status: list.status,
        message: '删除成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 保存模板问卷详细信息
  async saveQuestionnaireTmpDetail() {
    const { ctx } = this;
    try {
      const list = await ctx.curl(
        `${this.config.iServiceHost}/questionnaire/saveQuestionnaireTmpDetail`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          dataType: 'json', // 返回的数据类型
          data: JSON.stringify(ctx.request.body),
        }
      );
      ctx.helper.renderSuccess(ctx, {
        status: list.status,
        data: '保存成功',
        message: '保存成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 复制问卷
  async copyNewQuestionnaire() {
    const { ctx } = this;
    try {
      const list = await ctx.curl(
        `${this.config.iServiceHost}/questionnaire/copyNewQuestionnaire`,
        {
          method: 'POST',
          dataType: 'json', // 返回的数据类型
          data: ctx.request.body,
        }
      );
      ctx.helper.renderSuccess(ctx, {
        status: list.status,
        data: list.data.data,
        message: '复制成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 查询关联企业
  async findGroupEnterprises() {
    const { ctx } = this;
    try {
      const { EnterpriseID } = ctx.session.adminUserInfo;
      const data = await ctx.curl(
        `${this.config.iServiceHost}/questionnaire/findGroupEnterprises`,
        {
          method: 'POST',
          dataType: 'json', // 返回的数据类型
          data: { EnterpriseID },
        }
      );
      ctx.helper.renderSuccess(ctx, {
        data: {
          data: data.data.data.res,
          server_path: this.app.config.server_path,
        },
        status: data.status,
        message: data.data.message,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  // 提交答卷
  async submitAnswer() {
    const { ctx } = this;
    try {
      if (ctx.session.adminUserInfo) {
        const question = ctx.request.body;
        const data = {
          ...question,
          questionAnswer: ctx.session.adminUserInfo._id,
          companyId: ctx.session.adminUserInfo.EnterpriseID,
          editStatus: 1,
        };
        const list = await ctx.curl(
          `${this.config.iServiceHost}/questionnaire/submitAnswer`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            dataType: 'json', // 返回的数据类型
            data: JSON.stringify(data),
          }
        );
        console.log('list', list);
        ctx.helper.renderSuccess(ctx, {
          data: '提交成功',
          status: list.status,
          message: list.data.message,
        });
      }
    } catch (error) {
      console.log(error);
    }
  }

  // 获取小程序图片
  async getQrCodeImg() {
    const { app, ctx } = this;
    const { id } = ctx.query;
    const appid = app.config.wxAutho.appid;
    const secret = app.config.wxAutho.secret;
    const res = await ctx.curl('https://api.weixin.qq.com/cgi-bin/token', {
      data: {
        grant_type: 'client_credential',
        appid,
        secret,
      },
      dataType: 'json',
    });
    if (res.data && res.data.access_token) {
      ctx.response.type = 'arraybuffer';
      const res2 = await ctx.curl(
        'https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=' +
          res.data.access_token,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          data: {
            page: 'pages/user/questionnaireDetail',
            scene: 'questionnaire=' + id,
            check_path: false,
            env_version: this.app.config.app_env_version,
          },
        }
      );
      // 将图片流转换为base64
      const base64Img =
        'data:image/png;base64,' +
        Buffer.from(res2.data, 'binary').toString('base64');
      ctx.helper.renderSuccess(ctx, {
        data: base64Img,
        status: res2.status,
        message: res2.data.message,
      });
    }
  }

  // 获取统计部分
  async getStatistics() {
    const { ctx } = this;
    try {
      console.log('ctx.request.body', ctx.request.body);
      const list = await ctx.curl(
        `${this.config.iServiceHost}/questionnaire/getStatistics`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          dataType: 'json', // 返回的数据类型
          data: JSON.stringify(ctx.request.body),
        }
      );
      ctx.helper.renderSuccess(ctx, {
        status: list.status,
        data: list.data.data,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 获取统计详情
  async getStatisticsDetail() {
    const { ctx } = this;
    try {
      console.log('ctx.request.body', ctx.request.body);
      const list = await ctx.curl(
        `${this.config.iServiceHost}/questionnaire/getStatisticsDetail`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          dataType: 'json', // 返回的数据类型
          data: JSON.stringify(ctx.request.body),
        }
      );
      list.data.data = list.data.data.map(item => {
        return {
          ...item,
          createdAt: moment(item.createdAt).format('YYYY-MM-DD HH:mm'),
        };
      });
      ctx.helper.renderSuccess(ctx, {
        status: list.status,
        data: list.data.data,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 设置模板为问卷
  async saveTempQuestionnaire() {
    const { ctx, service } = this;
    try {
      const { publishStatus, Enterprises, title, description } = ctx.request.body;
      if (publishStatus === 1) {
        const companyId = Enterprises.map(item => item.EnterpriseID);
        const userIds = await this.ctx.service.db.find('User', { companyId: { $in: companyId } }, { _id: 1 });
        const readerIds = userIds.map(user => ({
          readerID: user._id,
          readerGroup: '',
          isRead: 0,
        }));
        const messageNotice = {
          title: `请填写${title}问卷调查`,
          message: description,
          reader: readerIds,
          sendWay: 'systemMessage',
          templateCode: '',
          type: 1,
          SignName: '',
          files: [],
          authorID: ctx.session.adminUserInfo._id,
          authorGroup: ctx.session.adminUserInfo.group,
          informer: ctx.session.adminUserInfo.name || '', // 发通知的人名
        };
        await service.messageNotification.sendMessageToUser(messageNotice);
      }
      const userId = ctx.session.adminUserInfo._id;
      ctx.request.body.userId = userId;
      const list = await ctx.curl(
        `${this.config.iServiceHost}/questionnaire/saveTempQuestionnaire`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          dataType: 'json', // 返回的数据类型
          data: JSON.stringify(ctx.request.body),
        }
      );
      ctx.helper.renderSuccess(ctx, {
        status: list.status,
        data: '设置成功',
        message: '设置成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
}

module.exports = QuestionnaireController;
