---
description: Vue2 single-spa 子项目规范
globs: backstage/*
alwaysApply: false
---
# Vue2 single-spa 子项目规范

## 项目结构规范

### 1. 目录结构
```
backstage/模块名/
├── src/                      # 源代码目录
│   ├── api/                  # API 接口定义
│   ├── assets/               # 静态资源文件
│   ├── components/           # 组件目录
│   ├── router/               # 路由配置
│   ├── store/                # Vuex 状态管理
│   ├── styles/               # 全局样式
│   ├── utils/                # 工具函数
│   ├── views/                # 页面视图组件
│   ├── App.vue               # 根组件
│   ├── main.js               # 入口文件
│   └── set-public-path.js    # 微前端公共路径设置
├── package.json              # 项目依赖配置
├── vue.config.js             # Vue CLI 配置
├── babel.config.js           # Babel 配置
└── postcss.config.js         # PostCSS 配置
```

### 2. 命名规范
- 文件夹名：使用小驼峰命名法，如 `recordSort`
- 组件文件名：使用小驼峰命名法，如 `recordSort.vue`
- 组件名称：使用 PascalCase 命名法，如 `RecordSort`
- 路由名称：使用 camelCase 命名法，如 `recordSort`
- API 服务名：使用 camelCase 命名法，如 `findRecordSort`

## 组件规范

### 1. 组件基本结构
```vue
<template>
  <div class="组件名">
    <!-- 组件内容 -->
  </div>
</template>

<script>
export default {
  name: '组件名',
  components: {
    // 子组件注册
  },
  props: {
    // 属性定义
  },
  data() {
    return {
      // 数据定义
    };
  },
  computed: {
    // 计算属性
  },
  watch: {
    // 监听器
  },
  created() {
    // 创建时执行
  },
  mounted() {
    // 挂载时执行
  },
  methods: {
    // 方法定义
  }
};
</script>

<style scoped>
/* 组件样式 */
</style>
```

### 2. 组件通信
- 父子组件通信：使用 props 和 $emit
- 跨组件通信：使用 Vuex 或事件总线

### 3. 样式规范
- 使用 scoped 属性限制样式作用域
- 使用tailwindcss 进行样式书写
- 组件样式使用组件名作为根类名

## 路由规范
参考[vue2.mdc](mdc:.cursor/rules/vue2.mdc)

## 状态管理规范

### 1. Vuex 配置
```javascript
import Vue from 'vue';
import Vuex from 'vuex';
import * as types from './types';

Vue.use(Vuex);

export default new Vuex.Store({
  state: {
    // 状态定义
  },
  mutations: {
    [types.MUTATION_TYPE](mdc:state, payload) {
      // 状态修改
    },
  },
  actions: {
    async actionName({ commit }) {
      // 异步操作
      commit(types.MUTATION_TYPE, payload);
    },
  },
  getters: {
    // 计算状态
  },
});
```

### 2. 状态管理最佳实践
- 使用常量定义 mutation 类型
- 异步操作放在 actions 中
- 使用 getters 计算派生状态

## API 服务规范
参考[vue2_request.mdc](mdc:.cursor/rules/vue2_request.mdc)
## 入口文件规范

### 1. main.js 配置
```javascript
import Vue from 'vue';
import './set-public-path';
import ElementUI from 'element-ui';
import singleSpaVue from 'single-spa-vue';
import router from './router';
import store from './store';
import App from './App';
import axios from '@/utils/axios';
import serviceAPI from '@/serviceAPI';

// 全局配置
Vue.use(ElementUI);
Vue.prototype.$axios = axios;
Vue.prototype.$serviceAPI = serviceAPI;
Vue.config.productionTip = false;

// 微前端配置
const vueLifecycles = singleSpaVue({
  Vue,
  appOptions: {
    render: h => h(App),
    router,
    store,
  },
});

export const bootstrap = [vueLifecycles.bootstrap];
export function mount(props) {
  return vueLifecycles.mount(props);
}
export const unmount = [vueLifecycles.unmount];
```

## 构建配置规范

### 1. vue.config.js 配置
```javascript
const path = require('path');
const defaultSettings = require('../publicMethods/settings.js');

function resolve(dir) {
  return path.join(__dirname, dir);
}

module.exports = {
  configureWebpack: {
    name: defaultSettings.name,
    resolve: {
      alias: {
        '@root': resolve('../'),
        '@': resolve('src'),
      },
    },
  },
  chainWebpack: config => {
    config.devServer.set('inline', false);
    config.devServer.set('hot', false);
    config.externals(['vue', 'vue-router', 'vuex', 'element-ui', 'axios']);
  },
  filenameHashing: false,
};
```

## 依赖管理规范

### 1. package.json 配置
```json
{
  "name": "模块名",
  "version": "1.0.0",
  "private": true,
  "scripts": {
    "serve": "vue-cli-service serve --port 端口号",
    "build": "vue-cli-service build"
  },
  "dependencies": {
    "axios": "^0.19.0",
    "element-ui": "^2.13.x",
    "js-cookie": "^2.2.1",
    "moment": "^2.24.0",
    "single-spa-vue": "^1.5.4",
    "vue": "^2.6.10",
    "vue-router": "^3.0.3",
    "vuex": "^3.1.1"
  },
  "devDependencies": {
    "@vue/cli-plugin-babel": "^3.9.0",
    "@vue/cli-service": "^3.9.0",
    "sass": "^1.52.2",
    "sass-loader": "^10.1.1",
    "vue-template-compiler": "^2.6.10"
  }
}
```

## 禁止事项

1. 禁止使用 Vue 3 的相关依赖和语法
2. 禁止修改前端项目的 App.vue 文件
3. 禁止使用可选链操作符 `?.`
4. 禁止使用 TypeScript 语法，项目为纯 JavaScript

## 最佳实践

1. 使用 Element UI 2.13.x 组件库
2. 使用 Vuex 进行状态管理
3. 使用 Vue Router 进行路由管理
4. 使用 Axios 进行 HTTP 请求
5. 使用 Moment.js 处理日期和时间
6. 使用 SCSS 预处理器编写样式
7. 使用 Single-SPA 微前端架构

以上就是基于对 backstage 目录下 Vue2 项目的分析总结出的 Vue2 mdc 规则。
