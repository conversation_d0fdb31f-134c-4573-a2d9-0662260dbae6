/**
 * Created by Zhanglc on 2021/12/11.
 * 付费套餐组对象
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const PayCategorySchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    showName: String, // 企业要显示的套餐名
    showComments: String, // 企业要显示的套餐描述
    name: String, // 运营端要显示的套餐名
    comments: String, // 运营端要显示的套餐描述
    power: [{
      type: String,
      ref: 'AdminResource',
    }],
    apiPower: [{ // 对外接口的套餐还未与开放API管理对应，暂时留下此字段已备后用
      type: String,
      ref: 'ApiResource',
    }],
    date: {
      type: Date,
      required: true,
      default: Date.now,
    },
    state: { // 假删除，1 未删除 0 已删除
      type: String,
      default: '1',
      enum: [ '0', '1' ],
    },
  });


  return mongoose.model('PayCategory', PayCategorySchema, 'payCategorys');

};
