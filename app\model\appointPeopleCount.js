module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const appointPeopleCountSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    appointDate: { // 预约日期
      type: Date,
      required: true,
    },
    organizationId: { // 检查医院_id
      type: String,
      required: true,
      ref: 'PhysicalExamOrg',
    },
    appointment: { // 预约人数
      type: Number,
    },
    flexible: { // 弹性人数
      type: Number,
      default: 0,
    },
    appointed: [ // 已预约人数
      {
        type: Number,
        default: 0,
      },
    ],
    physicalExamined: { // 已体检人数
      type: Number,
      default: 0,
    },

  }, { timestamps: true });
  return mongoose.model('appointPeopleCount', appointPeopleCountSchema);
};
