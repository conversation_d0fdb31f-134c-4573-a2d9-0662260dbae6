module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');
  const moment = require('moment');
  const CryptoJS = require('crypto-js');
  const ctx = app.createAnonymousContext();
  const permissionPlugin = require('../utils/permission.js');

  const UserSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    unitCode: String, // 编码
    enable: {
      type: Boolean,
      default: true,
    }, // 用户是否有效
    wxUnionId: String, // 微信unionId
    alipayUnionId: String, // 支付宝unionId
    name: String,
    userName: String,
    nickName: String, // 第三方昵称
    country: String, // 国家
    password: {
      type: String,
      set(val) {
        return val.length > 30 ? val : CryptoJS.SHA256(val + app.config.salt_sha2_key).toString();
      },
    },

    aboutTransfer: { //  oprate by jhw to transfer job postion ///
      isRead: { type: String, default: '0' }, // 是否已读
      employeeId: String, // 人员id
      beforePosition: String, // 转岗前的岗位
      nowPosition: String, // 转岗后的岗位
    },

    employeeId: { // 当前绑定的企业  所在Employee表的_id, 没有绑定企业或者解绑之后会变为 ''
      type: String,
      ref: 'Employee',
    },
    email: String,
    qq: Number,
    phoneNum: {
      type: String,
      sparse: true,
      trim: true,
    },
    wx: {
      unionId: String,
      nickName: String,
      logo: String,
    },
    alipay: {
      unionId: String,
      nickName: String,
      logo: String,
    },
    countryCode: {
      type: String,
      default: '86',
    }, // 手机号前国家代码
    idNo: String, // 身份证号码
    idType: {
      type: String,
      default: '1',
    }, // 证件类型 1为身份证
    comments: {
      type: String,
      default: '',
    },
    introduction: {
      type: String,
      default: '',
    }, // 个人简介
    position: String, // 职位
    profession: String, // 职业
    industry: String, // 行业
    experience: String, // 教育经历
    company: String, // 大学或公司(当前，最新的)
    companyId: [
      { // 所在企业ID合集，默认最后一个是当前绑定的企业
        type: String,
        ref: 'Adminorg',
      },
    ],
    companyStatus: { // 企业是否确认用户为员工
      type: Number,
      default: 0, // 0 是未绑定 1是已绑定待企业审核  2是企业审核通过 3是企业审核未通过
      enum: [ 0, 1, 2, 3 ],
    },
    website: String, // 个人站点
    date: {
      type: Date,
      default: Date.now,
    },
    updateTime: Date, // 更新时间，主要是指绑定企业啊之类的
    logo: { // 头像
      type: String,
      default: '/static/upload/images/defaultlogo.png',
    },
    group: {
      type: String,
      default: app.config.groupID.userGroupID || '0',
      ref: 'UserGroup',
    }, // 0 普通用户
    province: String, // 所在省份
    city: String, // 所在城市
    birth: {
      type: Date,
      default: new Date('1770-01-01'),
    }, // 出生年月日 2018-03-21
    gender: {
      type: String,
      default: '0',
    }, // 性别 0男 1女
    despises: [{
      type: String,
      ref: 'Content',
    }], // 文章或帖子
    despiseMessage: [{
      type: String,
      ref: 'Message',
    }], // 评论

    favorites: [{
      type: String,
      ref: 'Content',
    }], // 收藏文章或帖子

    praiseContents: [{
      type: String,
      ref: 'Content',
    }], // 点赞的文章或帖子
    praiseMessages: [{
      type: String,
      ref: 'Message',
    }], // 点赞的评论
    state: {
      type: String,
      default: '1', // 1正常，0删除
    },
    // category: {
    //   type: String,
    //   ref: 'ContentCategory'
    // }, // 文章类别
    followers: [{
      type: String,
      ref: 'User',
    }], // 关注我的创作者
    watchers: [{
      type: String,
      ref: 'User',
    }], // 我关注的创作者

    watchTags: [{
      type: String,
      ref: 'ContentTag',
    }], // 我关注的标签
    retrieve_time: {
      type: Number,
    }, // 用户发送激活请求的时间
    loginActive: {
      type: Boolean,
      default: false,
    }, // 首次登录
    deviceId: String, // 针对游客的设备id
    role: { // 角色
      type: String,
      default: '',
      enum: [ '', 'expert' ], // expert 专家
    },
    forTrainrole: { // 培训所用角色
      type: String,
      default: '',
      enum: [ '', 'responsible', 'management ', 'laborer' ], // responsible 主要负责人 laborer 劳动者
    },
    expertApplyDate: Date, // 专家入驻申请日期
    expertReviewDate: Date, // 专家入驻审核日期
    expertStatus: {
      type: Number,
      default: 0,
      enum: [ 0, 1 ], // 0:下线 1：上线
    },
    source: { // 来源端
      type: String,
      default: 'enterprise',
      enum: [ 'enterprise', 'tj', 'super', 'dbscript', 'opt', 'jc', 'oapi' ],
    },

  });

  // UserSchema.index({ phoneNum: 1 }, { unique: true }); // 唯一索引添加失败，因数据库中有一百多条重复数据
  UserSchema.index(
    { phoneNum: 1 },
    {
      unique: true,
      partialFilterExpression: { phoneNum: { $exists: true } },
    }
  );
  // UserSchema.index({ userName: 1 }, { unique: true }); // 唯一索引添加失败，因数据库中有一百多条重复数据
  UserSchema.index(
    { userName: 1 },
    {
      unique: true,
      partialFilterExpression: { userName: { $exists: true } },
    }
  );

  UserSchema.set('toJSON', {
    getters: true,
    virtuals: true,
  });
  UserSchema.set('toObject', {
    getters: true,
    virtuals: true,
  });

  UserSchema.path('date').get(function(v) {
    return moment(v).format('YYYY-MM-DD HH:mm:ss');
  });

  UserSchema.path('birth').get(function(v) {
    return moment(v).format('YYYY-MM-DD');
  });

  UserSchema.plugin(permissionPlugin, {
    ctx,
    fieldMapping: {
      enterpriseId: 'companyId',
      employeeId: 'employeeId',
    },
  });
  return mongoose.model('User', UserSchema, 'users');
};
