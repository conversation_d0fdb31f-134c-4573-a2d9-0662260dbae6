/**
 * 危害因素必检项目
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  const MandatoryItemsSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    defaultCommon: Boolean, // 是否默认的必检项目
    hazardCode: String, // 危害因素代码
    hazardName: String, // 危害因素名称
    careType: String, // 体检类型 01：上岗前 02：在岗期间 03：离岗时 04：应急 05：离岗后
    items: [ String ], // 体检项目
  }, { timestamps: true });

  return mongoose.model('MandatoryItems', MandatoryItemsSchema, 'mandatoryItems');
};
