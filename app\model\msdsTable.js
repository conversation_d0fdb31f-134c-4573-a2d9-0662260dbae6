/**
 * MSDS物质安全数据表
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;


  const MsdsTableSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    table: String, // MSDS表名
    tableUrl: String, // 表链接
    englishName: String, // 英文名称
    englishName2: String, // 英文名称2
    name2: String, // 中文名称
    basis: String, // 主要成分
    content: String, // 含量
    casNo: String, // CAS No. CAS编号
    molecularFormula: String, // 分子式
    molecularWeight: String, // 分子量
    appearanceAndAppearance: String, // 外观与性状
    meltingPoint: String, // 熔点(℃)
    boilingPoint: String, // 沸点(℃)
    relativeDensityWater: String, // 相对密度(水)
    relativeDensityAir: String, // 相对密度(空气)
    hazardCategory: String, // 危险性类别
    vaporPressure: String, // 饱和蒸气压
    solubility: String, // 溶解性
    mainPurpose: String, // 主要用途
    hazardsIdentification: { // 危险性概述
      health: String, // 健康危害
      environment: String, // 环境危害
      explosion: String, // 燃爆危险
    },
    protective: { // 防护措施
      engineeringControl: String, // 工程控制
      respiratory: String, // 呼吸系统
      eye: String, // 眼睛防护
      body: String, // 身体防护
      hand: String, // 手防护
      others: String, // 其他防护
    },
    emergency: { // 急救措施
      skinContact: String, // 皮肤接触
      eyeContact: String, // 眼睛接触
      inhalation: String, // 吸入
      ingestion: String, // 食入
    },
    fireProtectionMeasures: { // 消防措施
      characteristics: String, // 危险特性
      products: String, // 燃烧产物
      extinguishment: String, // 灭火方法
      fireExtinguisher: String, // 灭火器
    },
    leakage: String, // 泄露应急处理
    handlingAndStorage: { // 操作处置与储存
      handlePrecautions: String, // 操作注意事项
      storagePrecautions: String, // 储存注意事项
    },
    exposureLimits: { // 职业接触限值(mg/m3)
      mac: String, // MAC
      pcTwa: String, // PC-TWA
      pcStel: String, // PC-STEL
      invasionPathway: String, // 侵入途径
    },
    toxicological: String, // 毒理学资料
    transportation: { // 运输信息
      hazardCode: String, // 危规号
      un: String, // UN编号
      packingGroup: String, // 包装类别
      packingMark: String, // 包装标志
      packingMethod: String, // 包装方法
    },
  });

  return mongoose.model('MsdsTable', MsdsTableSchema, 'MsdsTable');
};
