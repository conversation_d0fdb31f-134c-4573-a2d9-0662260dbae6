/**
 * 运营端-API资源表
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const ApiResourceSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    label: String, // 用来校验重复API
    type: String, // 0为API归类 1为API列表
    routePath: String, // 路由路径
    api: String, // 资源路径
    parentId: String,
    enable: { // 是否有效
      type: Boolean,
      default: true,
    },
    sortId: {
      type: Number,
      default: 0,
    },
    date: {
      type: Date,
      default: Date.now,
    },
    comments: String,
  });

  return mongoose.model('ApiResource', ApiResourceSchema, 'apiResources');
};

