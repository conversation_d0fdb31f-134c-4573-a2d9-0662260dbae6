const _ = require('lodash');
const moment = require('moment');
module.exports = (options, app) => {
  let routeWhiteList = [
    'logout',
    'getUserSession',
    'getSitBasicInfo',
    'adminResource/getListByPower',
    'admin/index',
    'adminResource/alllist',
    'getBasicMessage',
    'getAssessmentInfo',
    'getChartData',
    'getCheckStatistics',
    'updateHealthList',
    'getMainData',
    'getAllReadMessage',
    'getNoReadMessage', // 获取没读的消息
    'updateStatistical',
    'setStatistical',
    'isReadMessage', // 标记已读消息
    'getIsReadMessage', // 获取已读消息
    'handleRole',
    'getSuspectData', // //得到  职业健康检查异常结果  分类别数据
    'updateOdiseases', // 更新全部企业职业病人数到统计表
    'updateOdisease', // 更新某家企业职业病人数到统计表
    'handleWarnNotice', // 清理 警示告知管理 的历史数据
    'getHealthCheckByYear', // 根据年份获取体检统计信息
    'checkUserName', // 修改用户信息检查用户名
    'dangerousPoints', // 获取企业危险点/法律风险
    'bindEnterpriseID', // 企业绑定当前新添加企业ID 是企业添加多个企业时用于告知是添加的哪个企业，以此获取其审核状态
    'getFilesCompleteness', // 企业的档案完成情况
    'getCheckData', // 获取最后一次体检统计结果
    'returnLogin', // 登陆进去可以返回登录页
    'warning/count3', // 导航栏中的预警总数统计
    'preventAssess/find', // 按年度查
    'adminorg/getCompany', // 获取首页用到的企业信息
    'getEveryYearHealthCheck', //
    'harmStatisticsPerson', // 获取所有接害人
    'stationCheck', // 岗位是否进行检测
    'jcTend', // 检测趋势变化数据
    'whjcData', // 获取所有的危害因素检测数据
    'getEducationData', // 获取教育培训数据
    'tjEcharts', // 获取新首页体检数据
    'getPersonInfo', // 获取当前登陆人的用户名
    'emptyRequest', // 空请求，用于更新cookies时间
    'adminResource/getProjectVisibilityEnabled',
    'adminResource/getRadioButtonEnabled',
    'policyManage/getIsSuperAdmin',
    'testDataPermission',
  ];
  // const notPhoneOrIDCardWhiteList = [
  //   'adminorg/getCompany',
  //   'warning/list',
  //   'preventAssess/findAll',
  // ];
  return async function checkAdminUserPower(ctx, next) {
    const getPluginApiWhiteList = app.getExtendApiList();
    if (!_.isEmpty(getPluginApiWhiteList) && !_.isEmpty(getPluginApiWhiteList.adminApiWhiteList) && routeWhiteList.indexOf(getPluginApiWhiteList.adminApiWhiteList.join(',')) < 0) {
      routeWhiteList = routeWhiteList.concat(getPluginApiWhiteList.adminApiWhiteList);
    }
    // const isSuperAdmin = await ctx.helper.getIsSuperAdmin(userid);
    let adminPower = [];
    const isExistUserId = await ctx.helper.getScopeData('userid');
    if (!isExistUserId) {
      adminPower = await ctx.helper.getUserResourceList();
    } else {
      adminPower = await ctx.helper.getScopeData('powerIds');
    }
    const isSuper = await ctx.helper.getScopeData('superAdmin');
    const targetApi = (ctx.originalUrl).replace('/manage/', '').split('?')[0];
    const targetResourceObj = await ctx.service.adminResource.find({
      isPaging: '0',
    }, {
      query: {
        type: '1',
        api: targetApi,
      },
      files: '_id api',
    });
    const DATENOW = new Date();
    let hasPower = false,
      hasAuth = false,
      adminorg = { effectiveDate: new Date('1999-1-1'), personCount: 0, harmPersonCount: 1 };
    // const targetResourceObj = _.find(resouce, {
    //   api: targetApi,
    // });
    if (_.isEmpty(ctx.session.adminUserInfo)) {
      hasPower = false;
      // adminPower = ctx.helper.getCache(
      //   `powerIds:${ctx.session.adminUserInfo._id}`
      // );
      // if (!adminPower || adminPower.length === 0) {
      //   const adminPowerList = await ctx.helper.getPolicyAdminPower(ctx);
      //   const { powerIds } = adminPowerList;
      //   adminPower = powerIds;
      // }
    } else {
      // adminPower = await ctx.helper.getAdminPower(ctx);
    }
    isSuper === '1' && ((hasPower = true), (hasAuth = true));
    if ((_.indexOf(routeWhiteList, targetApi) > -1)) {
      hasPower = true;
      hasAuth = true;
    } else {
      const targetResourceObjCount = targetResourceObj.length;
      for (let i = 0; i < targetResourceObjCount; i++) {
        const element = targetResourceObj[i];
        if (targetResourceObj && adminPower && (_.indexOf(adminPower, element._id) > -1)) {
          hasPower = true;
          const s = 0;
          if (s === 1 || ![ 'hz' ].includes(app.config.branch)) {
            hasAuth = true;
          } else {
            const EnterpriseID = ctx.session.adminUserInfo.EnterpriseID;
            const adminorgsPayCategory = await ctx.service.adminorg.item(ctx, { query: { _id: EnterpriseID }, files: '_id payCategoryPower' });
            if (!adminorgsPayCategory.payCategoryPower || adminorgsPayCategory.payCategoryPower.length === 0) {
              const payCategory = await ctx.model.PayCategory.find({ _id: app.config.defaultPayCategoryID }, '_id power apiPower').limit(1);
              const payCategoryItem = payCategory[0];
              if (payCategoryItem) {
                await ctx.service.adminorg.update(EnterpriseID, {
                  payCategoryID: payCategoryItem._id,
                  payCategoryPower: payCategoryItem.power,
                  payCategoryApiPower: payCategoryItem.apiPower,
                  personCount: 5000,
                  effectiveDate: new Date('2100-1-1') });
              } else {
                ctx.auditLog('系统错误（套餐）', '找不到配置文件中基础套餐defaultPayCategoryID的相关数据', 'error');
              }
            }
            let adminorgs = await ctx.service.adminorg.item(ctx, { query: { _id: EnterpriseID }, files: '_id payCategoryPower personCount effectiveDate harmStatistics' });
            if (adminorgs.harmStatistics && adminorgs.harmStatistics.length > 0) {
              const pipeline = [
                { $match: { _id: EnterpriseID } },
                { $project: { _id: 0, payCategoryPower: 1, personCount: 1, effectiveDate: 1, harmStatistics: 1 } },
                { $unwind: '$harmStatistics' },
                { $match: { 'harmStatistics.sort': 'all' } },
                { $unwind: '$harmStatistics.count' },
                { $match: { 'harmStatistics.count.label': '劳动者接害总人数' } },
                { $project:
                  { effectiveDate: 1, personCount: 1, harmPersonCount: '$harmStatistics.count.value',
                    auth: {
                      $and: [
                        {
                          $eq: [{ $cond: [{ $lte: [ DATENOW, '$effectiveDate' ] }, true, false ] }, true ],
                        },
                        {
                          $eq: [{ $cond: [{ $lte: [ '$harmStatistics.count.value', '$personCount' ] }, true, false ] }, true ],
                        },
                        { $ne: [{ $indexOfArray: [ '$payCategoryPower', element._id ] }, -1 ] },
                      ],
                    },
                  },
                },
              ];
              adminorgs = await ctx.service.db.aggregate('adminorg', pipeline);
              if (adminorgs.length > 0) {
                adminorg = adminorgs[0];
                hasAuth = adminorg.auth;
              } else {
                adminorg = adminorgs;
                hasAuth = false;
              }
            } else {
              if (adminorgs._id) {
                adminorg = adminorgs;
                hasAuth = _.indexOf(adminorg.payCategoryPower, element._id) > -1;
              }
            }
          }
          break;
        }
      }
    }

    if (hasPower && hasAuth) {
      ctx.auditLog('鉴权信息', '鉴权成功：' + ctx.originalUrl);
      ctx.auditLog('套餐认证信息', '套餐认证成功：' + ctx.originalUrl);
      const adminuser = await ctx.service.adminUser.item(ctx, {
        query: { _id: ctx.session.adminUserInfo._id },
        files: '-_id IDcard phoneNum -group' });
      const IDcard = adminuser.IDcard || '',
        phoneNum = adminuser.phoneNum || '';
      if (IDcard !== '' && phoneNum !== '') {
        await next();
      } else {
        // if (routeWhiteList.indexOf(targetApi) === -1) {
        //   if (notPhoneOrIDCardWhiteList.indexOf(targetApi) !== -1) {
        //     await next();
        //     return;
        //   }
        //   ctx.helper.renderAutoResponse(ctx, 510, {
        //     message: `您好！请先补充或确认${adminuser.IDcard ? '手机号' : '身份证号'}才可使用此功能，关系到后续职业卫生档案等资料的归档等，请点击右上角头像中的设置按钮认真填写。`,
        //   });
        //   return;
        // }
        await next();
      }
    } else if (!hasPower) {
      ctx.auditLog('鉴权信息', '鉴权失败：' + ctx.originalUrl);
      ctx.helper.renderFail(ctx, {
        message: '对不起，您暂无此权限',
      });
    } else if (!hasAuth) {
      ctx.auditLog('鉴权信息', '鉴权成功：' + ctx.originalUrl);
      ctx.auditLog('套餐认证信息', '套餐认证失败：' + ctx.originalUrl);
      let message = '对不起，您当前的套餐无法使用此功能，可联系客服升级';
      if (DATENOW > adminorg.effectiveDate) {
        message = `对不起，您的套餐有效期为${moment(adminorg.effectiveDate).format('YYYY-MM-DD')}日0点，当前已过期，无法使用此功能，可联系客服升级`;
      } else if (adminorg.harmPersonCount > adminorg.personCount) {
        message = `对不起，您的套餐接害人数为${adminorg.personCount}人，当前接害人数为${adminorg.harmPersonCount}人，已超过套餐接害人数，无法使用此功能，可联系客服升级`;
      }
      ctx.helper.renderFail(ctx, {
        message,
      });
    }
  };
};
