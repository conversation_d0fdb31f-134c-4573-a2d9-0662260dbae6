const _ = require('lodash');
module.exports = (options, app) => {

  let routeWhiteList = [
    // 'logout',
    'getUserSession',
    'getSitBasicInfo',
    'adminResource/getListByPower',
    'admin/index',
    'survey',
    // 测试
    'adminResource/alllist',
    'emptyRequest',
    'policyManage/getSuperAdminUser',
    'policyManage/getIsSuperAdmin',
    'jobHealth/updateMergePointData',
    'api/testDataPermission',
  ];

  return async function authAdminPower(ctx, next) {
    // 添加插件中的白名单
    const getPluginApiWhiteList = app.getExtendApiList();
    if (!_.isEmpty(getPluginApiWhiteList) && !_.isEmpty(getPluginApiWhiteList.adminApiWhiteList) && routeWhiteList.indexOf(getPluginApiWhiteList.adminApiWhiteList.join(',')) < 0) {
      routeWhiteList = routeWhiteList.concat(getPluginApiWhiteList.adminApiWhiteList);
    }
    let adminPower = [];
    const isExistUserId = await ctx.helper.getScopeData('userid');
    if (!isExistUserId) {
      adminPower = await ctx.helper.getUserResourceList();
    } else {
      adminPower = await ctx.helper.getScopeData('powerIds');
    }
    const isSuperAdmin = await ctx.helper.getScopeData('superAdmin');
    // const isSuperAdmin = await ctx.helper.getIsSuperAdmin(userid);
    // ctx.helper.setPolicyCache(`superAdmin:${userid}`, isSuperAdmin);
    // if (isSuperAdmin) {
    //   const superEnterprise_ids = await ctx.helper.getSuperAdminEnterpriseIds();
    //   ctx.helper.setPolicyCache(`superEnterprise_ids:${userid}`, superEnterprise_ids);
    // }

    const resouce = await ctx.service.adminResource.find({
      isPaging: '0',
    }, {
      query: {
        type: '1',
      },
      files: '_id api',
    });

    let hasPower = false;
    // console.log('\r\n\r\n1.检查权限  app>middleware>authAdminPower\r\n');
    // console.log('==========');
    isSuperAdmin === '1' && (hasPower = true);
    for (let i = 0; i < resouce.length; i++) {
      const resourceObj = resouce[i];
      const targetApi = (ctx.originalUrl).replace('/manage/', '').split('?')[0];
      if (!_.isEmpty(ctx.session.adminUserInfo)) {
        // const adminPower = await ctx.helper.getAdminPower(ctx);
        // let adminPower = await ctx.helper.getScopeData('powerIds');
        if (resourceObj.api === targetApi && adminPower && adminPower.indexOf(resourceObj._id) > -1) {

          hasPower = true;
          // console.log(targetApi);
          // console.log(hasPower);
          break;
        } else {
          // 没有配置但是包含在白名单内的路由校验
          if (!_.isEmpty(routeWhiteList)) {
            const checkWhiteRouter = _.filter(routeWhiteList, item => {
              if (item.indexOf('*') > 0 && targetApi.indexOf(item.split('*')[0]) === 0) {
                return true;
              }
              return item === targetApi;

            });
            if (!_.isEmpty(checkWhiteRouter)) {
              hasPower = true;
              break;
            }
          }
        }
      }
    }

    if (!hasPower) {
      ctx.helper.renderFail(ctx, {
        message: '对不起，您暂无此权限',
      });
    } else {
      // console.log('鉴权通过！当前用户：', ctx.session.adminUserInfo.userName);
      await next();
    }
  };
};
