module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const ctx = app.createAnonymousContext();
  const permissionPlugin = require('../utils/permission.js');

  const AdminorgDetailSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    EnterpriseID: { type: String }, // 企业id
    industryCategory: [ Array ], // 行业分类
    questionnaireCode: String, // 调查表编号
    questionnaireDate: Date, // 调查日期
    productionDate: Date, // 投产日期
    peopleTotal: Number, // 总人数
    womenTotal: Number, // 女职工人数-1
    contributorsNum: Number, // 劳务派遣人员
    harmStatisticsTotal: Number, // 接害总人数
    shouldCheckTotal: Number, // 职业健康体检应检总人数
    actuallTotal: Number, // 实检总人数
    healthCheck: String, // "是否开展职业健康检查情况" 均未体检"0"/年份
    lastHealthCheckCode: String, // "最近一次在岗期间职业健康体检报告编号"
    HealthCheckOrg: String, // 对应的体检机构名称"
    onlineDeclaration: Boolean, // "职业病危害项目申报情况是否申报"
    leaderTrain: Boolean, // "用人单位负责人是否参加了培训" : "无",
    manageTrain: Boolean, // "职业健康管理人员是否参加了培训" : "无",
    train: String, // "是否参加了培训" '0'：未培训，'1'：部分培训， '2'：全部培训
    trainNum: Number, // "参与培训人数"
    jobhealth: String, // "开展职业病危害因素检测情况" : "均未检测"/年份,
    lastJobhealthCode: String, // "最近一次检测（评价报告编号）
    jobhealthOrg: String, // "对应的检测评价机构名称"
    check: String, // "已开展检测情况" '0'：未检测，'1'：部分检测， '2'：全部检测
    result: [// 结果
      {
        _id: String,
        label: String, // 危害因素的名称  煤尘/苯/噪音/...
        parentLabel: String, // 取其父类的label，没有父类为0
        harmStatisticsNum: Number, // 接害人数
        point: Number, // 检测点数
        exceed: Number, // 检测超标点数
        stationPoint: Number, // 检测岗位点数/工种数
        stationExceed: Number, // 检测超标的岗位点数
        shouldCheckNum: Number, // 应检人数
        actuallNum: Number, // 实检人数
        shouldReCheckNum: Number, // 应复查人数
        actuallReCheckNum: Number, // 实复查人数
        abnormalNum: Number, // 异常人数
      },
    ],
  });

  AdminorgDetailSchema.set('toJSON', {
    getters: true,
    virtuals: true,
  });
  AdminorgDetailSchema.set('toObject', {
    getters: true,
    virtuals: true,
  });

  AdminorgDetailSchema.plugin(permissionPlugin, {
    ctx,
    fieldMapping: {
      enterpriseId: 'EnterpriseID',
    },
  });

  return mongoose.model('AdminorgDetail', AdminorgDetailSchema, 'adminorgDetails');

};
