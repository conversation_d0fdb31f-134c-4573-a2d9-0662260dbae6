---
description: Vue2 Router 的规则：
globs: backstage/*/src/router/index.js
alwaysApply: false
---
# Vue2 Router 规则总结

## 基本结构规范

1. **导入规范**
   - 必须导入 Vue 和 Vue Router
   - 使用 `Vue.use(Router)` 或 `Vue.use(VueRouter)` 注册路由
   - 从 `@root/publicMethods/settings` 导入设置

2. **路由器创建**
   - 使用工厂函数 `createRouter()` 创建路由实例
   - 支持路由重置功能 `resetRouter()`

3. **路由配置**
   - 使用 `history` 模式
   - 设置 `base: process.env.BASE_URL`
   - 配置 `scrollBehavior` 返回顶部 `{ y: 0 }`

4. **路径规范**
   - 管理后台路径使用 `settings.admin_base_path + '/模块名'`
   - 用户前台路径使用 `settings.user_base_path + '/模块名'`
   - 路径命名采用小写加连字符格式

5. **组件导入**
   - 使用 `@/views/` 或 `@/components/` 路径导入组件
   - 组件名称采用 PascalCase 命名法

6. **路由定义**
   - 每个路由必须包含 `path`、`name` 和 `component` 属性
   - 路由名称采用 camelCase 命名法
   - 动态参数使用 `:参数名` 格式

## 路由结构示例

```javascript
const createRouter = () => new Router({
  mode: 'history',
  base: process.env.BASE_URL,
  scrollBehavior: () => ({
    y: 0,
  }),
  routes: [
    {
      path: settings.admin_base_path + '/模块名',
      name: '模块名',
      component: 组件名,
    },
    {
      path: settings.admin_base_path + '/模块名/:参数',
      name: '详情页名',
      component: 详情组件,
    },
    {
      path: settings.admin_base_path + '/模块名/其他页面名',
      name: '其他页面名',
      component: 其他页面名,
    }
  ],
});
```

## 路由重置功能

```javascript
export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}
```

## 最佳实践

1. 路由名称应与组件功能相对应
2. 动态参数应使用有意义的名称
3. 相关路由应当分组放置
4. 管理后台和用户前台的路由应分开定义
5. 路由路径应当简洁明了，反映功能层次

## 注意事项

1. 路由路径必须以 `settings.admin_base_path` 或 `settings.user_base_path` 开头
2. 路由名称不能重复
3. 组件必须正确导入
4. 动态参数应当在组件中正确处理
