/**
 * policy权限表
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const { manageJtPoliceIDCommon } = app.config.groupID;

  const PolicySchema = new Schema(
    {
      _id: {
        type: String,
        default: shortid.generate,
      },
      name: String, // 管理员名称
      user_ids: Array, // 用户组ID
      scope_type: {
        type: String,
      }, // Enterprise, Dingtree, MillConstruction
      enterprise_ids: Array, // 企业ID
      dingtree_ids: Array, // 部门ID
      millConstruction_ids: { type: [ String ] }, // 工作场所
      // 添加对层级工作场所ID的支持
      millConstruction_hierarchical_ids: [
        {
          _id: {
            type: String,
            default: shortid.generate,
          },
          id: { type: String }, // 实际ID
          parentId: { type: String }, // 父级ID
          level: { type: String }, // 级别（mill/workspaces/stations）
          fullId: { type: String }, // 完整ID格式，用于前端展示
        },
      ],
      group_ids: {
        type: Array,
        default: [ manageJtPoliceIDCommon ],
      }, // 角色组ID
      enable: {
        // 是否可编辑
        type: Boolean,
        default: true,
      },
      enterpriseScopeType: {
        type: Number,
      },
      dingtreeScopeType: {
        type: Number,
      },
      millScopeType: {
        type: Number,
      },
      topEnterpriseID: {
        type: String,
        ref: 'Adminorg',
      },
      // 是否是超级管理员
      isSuper: {
        type: Boolean,
        default: false,
      },
      sortId: {
        type: Number,
        default: 0,
      },
      lastOperator: {
        type: String,
      },
    },
    {
      timestamps: true,
    }
  );

  return mongoose.model('Policy', PolicySchema);
};
