module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  const ctx = app.createAnonymousContext();
  const permissionPlugin = require('../utils/permission.js');

  const TaskBatchSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    totalTasks: Number, // 总任务数量
    completedTasks: Number, // 完成任务数量
    enterpriseId: {
      type: String,
      ref: 'Adminorg',
    },
    year: Date, // 任务年份
    isCompleted: Boolean, // 是否所有任务都已完成
    error: String, // 错误信息
    archiveProgress: {
      type: Schema.Types.Mixed,
      default: {},
    }, // 档案生成进度记录
    subTaskRes: [
      {
        enterpriseId: String,
        enterpriseName: String,
        year: String,
        successCount: [ String ],
        errArr: [
        ],
        records: [
        ],
        _id: {
          type: String,
          default: shortid.generate,
        },
      },
    ],
    adminUser: {},
  },
  {
    timestamps: true,
  }
  );

  TaskBatchSchema.plugin(permissionPlugin, {
    ctx,
    fieldMapping: {
      enterpriseId: 'enterpriseId',
    },
  });
  return mongoose.model('TaskBatch', TaskBatchSchema, 'taskBatch');
};
