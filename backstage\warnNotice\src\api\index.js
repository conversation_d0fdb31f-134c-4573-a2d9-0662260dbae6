import request from '@root/publicMethods/request';

export default {
  // 获取员工详情
  getEmployeeInfo: data => {
    return request({
      url: '/manage/violationInfoManage/getEmployeeInfo',
      method: 'post',
      data,
    });
  },

  // 获取表格数据
  getDatas: params => {
    return request({
      url: '/manage/warnnotice/find',
      method: 'get',
      params,
    });
  },
  // 取下
  delete: ids => {
    return request({
      url: '/manage/warnnotice/delete',
      method: 'post',
      data: ids,
    });
  },
  // 提交表单
  submitData: data => {
    return request({
      url: '/manage/warnnotice/create',
      method: 'post',
      data,
    });
  },
  // 修改表格数据
  updateData: data => {
    return request({
      url: '/manage/warnnotice/update',
      method: 'post',
      data,
    });
  },
  // 上传告知卡
  uploadCard: data => {
    return request({
      url: '/manage/warnnotice/uploadCard',
      method: 'post',
      data,
    });
  },
  getOrg: () => { // 用这个接口是为了获取当前登录的企业的名称，也可以另写一个接口，只返回需要的字段
    return request({
      method: 'get',
      url: '/manage/adminorg/getCompany',
    });
  },
  getAllSubCompany: () => { // 获取所有子公司的名字和id
    return request({
      method: 'get',
      url: '/manage/adminorg/getAllSubCompany',
    });
  },
  getWarnList: id => {
    return request({
      method: 'get',
      url: '/manage/warnnotice/getWarnList',
      data: id,
    });
  },
};
