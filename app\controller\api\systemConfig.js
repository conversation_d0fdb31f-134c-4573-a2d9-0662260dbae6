/*
 * @Author: doramart
 * @Date: 2019-07-07 13:07:27
 * @Last Modified by: mikey.zhaopeng
 * @Last Modified time: 2021-08-18 15:35:57
 */
const Controller = require('egg').Controller;
const _ = require('lodash');
const sendToWormhole = require('stream-wormhole');
const awaitWriteStream = require('await-stream-ready').write;
const path = require('path');
const url = require('url');
const fs = require('fs');
const { mkdirp } = require('mkdirp');
const { siteFunc } = require('@utils');
const { nanoid } = require('nanoid');
const {
  config,
  // upload,
} = require('../../utils/upload');

// 处理Ueditor上传保存路径
function setFullPath(dest, ctx) {
  const date = new Date();

  const map = {
    t: date.getTime(), // 时间戳
    m: date.getMonth() + 1, // 月份
    d: date.getDate(), // 日
    h: date.getHours(), // 时
    i: date.getMinutes(), // 分
    s: date.getSeconds(), // 秒
  };

  dest = dest.replace(/\{([ymdhis])+\}|\{time\}|\{rand:(\d+)\}/g, function(all, t, r) {
    let v = map[t];
    if (v !== undefined) {
      if (all.length > 1) {
        v = '0' + v;
        v = v.substr(v.length - 2);
      }
      return v;
    } else if (t === 'y') {
      return (date.getFullYear() + '').substr(6 - all.length);
    } else if (all === '{time}') {
      return map.t;
    } else if (r >= 0) {
      return Math.random().toString().substr(2, r);
    }
    return all;
  });
  let enterpriseid =
    url.parse(ctx.url).query && url.parse(ctx.url).query.indexOf('=') !== -1
      ? url.parse(ctx.url).query.split('=')[1]
      : null;
  if (enterpriseid.includes('/')) {
    const parts = enterpriseid.split('/');
    enterpriseid = parts[parts.length - 1];
  }
  console.log(enterpriseid);
  dest = dest.replace('{ENTERPRISEID}', (enterpriseid && enterpriseid !== 'undefined') ? enterpriseid : 'default');
  return dest;
}


const getUploadConfig = userUploadConfig => {
  const conf = Object.assign({}, config, userUploadConfig || {});
  const uploadType = {
    [conf.imageActionName]: 'image',
    [conf.scrawlActionName]: 'scrawl',
    [conf.catcherActionName]: 'catcher',
    [conf.videoActionName]: 'video',
    [conf.fileActionName]: 'file',
  };
  const listType = {
    [conf.imageManagerActionName]: 'image',
    [conf.fileManagerActionName]: 'file',
  };
  return {
    conf,
    uploadType,
    listType,
  };
};

const getFileInfoByStream = (ctx, uploadOptions, stream) => {
  const {
    conf,
    uploadType,
  } = getUploadConfig(uploadOptions);
  // console.log('======================================2222')
  console.log(uploadOptions);
  const fileParams = stream.fields;
  const askFileType = fileParams.action || 'uploadimage'; // 默认上传图片
  // console.log(fileParams)
  // console.log(askFileType)
  if (Object.keys(uploadType).includes(askFileType)) {
    const actionName = uploadType[askFileType];
    console.log('上传路径拼接：', conf[actionName + 'PathFormat']);
    console.log(setFullPath(conf[actionName + 'PathFormat'], ctx), '第二个split');
    console.log(ctx.url, '赋值后');
    const pathFormat = setFullPath(conf[actionName + 'PathFormat'], ctx).split('/');
    const newFileName = pathFormat.pop();
    const uploadForder = path.join('.', ...pathFormat);
    // 所有表单字段都能通过 `stream.fields` 获取到
    const fileName = path.basename(stream.filename); // 文件名称
    const extname = path.extname(stream.filename).toLowerCase(); // 文件扩展名称
    if (!extname) {
      throw new Error('文件扩展名有问题');
    }

    return {
      uploadForder,
      uploadFileName: newFileName + extname,
      fileName,
      fileType: extname,
    };

  }
  throw new Error(ctx.__('validate_error_params'));


};


class SystemConfigController extends Controller {
  async list() {
    const ctx = this.ctx;
    const systemConfigList = await ctx.service.systemConfig.find(
      {
        isPaging: '0',
      },
      {
        files:
          'siteName ogTitle siteDomain siteDiscription siteKeywords siteAltKeywords registrationNo showImgCode statisticalCode',
      }
    );
    ctx.helper.renderSuccess(ctx, {
      data: systemConfigList[0],
    });
  }

  async addressList() {
    const ctx = this.ctx;

    try {
      const payload = {
        current: 1,
        pageSize: 10000,
        isPaging: '0',
      };
      // console.log('取地址列表，当前入参', ctx.query);
      let query = ctx.query;
      if (query.root) {
        query = {
          parent_code: 0,
        };
      } else {
        query = {
          parent_code: query.area_code,
        };
      }

      const addlist = await ctx.service.district.find(payload, {
        sort: {
          id: -1,
        },
        files:
          'id parent_code name lng lat zip_code area_code level merger_name short_name',
        query,
      });
      // gs ++++
      // console.log(addlist, 'addlist66666666666666666666');
      if (addlist.length > 0 && addlist[0].level === '2') {
        for (let i = 0; i < addlist.length; i++) {
          // const res = await ctx.model.District.find({ parent_code: addlist[i].area_code }).count();
          const res = await ctx.service.db.count('District', { parent_code: addlist[i].area_code }, {}, { count: true });
          if (res === 0) {
            const item = JSON.parse(JSON.stringify(addlist[i]));
            item.hasChildren = true;
            addlist[i] = JSON.parse(JSON.stringify(item));
            // console.log(addlist[i], '77777777777777777777addlist');
          }
        }
      }
      // console.log(addlist, 'res-------------');
      // addlist.forEach(item => {
      //   while (item.level = 2) {

      //   }
      // });
      await ctx.helper.renderSuccess(ctx, {
        message: '地址列表',
        data: addlist,
      });
    } catch (e) {
      ctx.helper.renderFail(ctx, {
        message: e,
      });
    }
  }

  // 上传资质
  async uploadQualifiesImages() {
    const ctx = this.ctx;
    const app = ctx.app;
    try {

      // const options = !_.isEmpty(app.config.doraUploadFile.uploadFileFormat) ? app.config.doraUploadFile.uploadFileFormat : {};
      const options = {};
      // const dataType = 'stream';
      // let  returnPath;
      // const uploadConfigInfo = await ctx.service.uploadFile.create({
      //   type: 'local',
      //   uploadPath: process.cwd() + '/app/public',
      // });
      const stream = await ctx.getFileStream();

      const beforeUploadFileInfo = await getFileInfoByStream(ctx, options, stream);
      const {
        // uploadForder,
        uploadFileName,
      } = beforeUploadFileInfo;

      // console.log('上传图片地址enterprise_path',app.config.enterprise_path)
      // const publicDir = options.upload_path || (process.cwd() + '/app/public');
      if (!ctx.session.adminUserInfo.EnterpriseID) {
        ctx.body = {
          ststus: 400,
          msg: '此处上传资质照片必须要有EnterpriseID',
        };
        return;
      }
      const qualifiesPath = ctx.session.adminUserInfo.EnterpriseID;
      const uploadPath = `${app.config.upload_path}/${qualifiesPath}`;
      if (!fs.existsSync(uploadPath)) {
        // mkdirp.sync(uploadPath);
        fs.mkdirSync(uploadPath);
      }
      const target = path.join(uploadPath, `${uploadFileName}`);
      try {
        const res = await ctx.helper.pipe({
          readableStream: stream,
          target,
        });
        console.log('fileUploadRes', res);
      } catch (err) {
        // 必须将上传的文件流消费掉，要不然浏览器响应会卡死
        await sendToWormhole(stream);
        throw err;
      }
      const returnPath = `${app.config.static.prefix}${app.config.upload_http_path}/${qualifiesPath}/${uploadFileName}`;
      await ctx.helper.renderSuccess(ctx, {
        message: '上传资质',
        status: true,
        data: {
          path: returnPath,
          imgName: `/${qualifiesPath}/${uploadFileName}`,
        },
      });
    } catch (e) {
      ctx.helper.renderFail(ctx, {
        message: e,
      });
    }
  }

  // async uploadEnterpriseImage() {
  //   const ctx = this.ctx;
  //   const app = ctx.app;
  //   try {
  //     const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
  //     if (!ctx.url.endsWith(EnterpriseID)) {
  //       ctx.url = ctx.url + EnterpriseID;
  //     }
  //     const options = !_.isEmpty(app.config.doraUploadFile.uploadFileFormat) ? app.config.doraUploadFile.uploadFileFormat : {};
  //     // const dataType = 'stream';
  //     // let  returnPath;
  //     // const uploadConfigInfo = await ctx.service.uploadFile.create({
  //     //   type: 'local',
  //     //   uploadPath: process.cwd() + '/app/public',
  //     // });
  //     const stream = await ctx.getFileStream();

  //     const beforeUploadFileInfo = await getFileInfoByStream(ctx, options, stream);
  //     const {
  //       uploadForder,
  //       uploadFileName,
  //     } = beforeUploadFileInfo;

  //     // console.log('上传图片地址enterprise_path',app.config.enterprise_path)
  //     // const publicDir = options.upload_path || (process.cwd() + '/app/public');

  //     const uploadPath = `${app.config.upload_path}/${uploadForder}`;
  //     // const uploadPath = `${app.config.enterprise_path}/${uploadForder}`;
  //     // const uploadPath1 = `${app.config.enterprise_path}/${uploadForder}`;
  //     // console.log('上传图片地址uploadPath',app.config.enterprise_path,uploadPath1)

  //     if (!fs.existsSync(uploadPath)) {
  //       mkdirp.sync(uploadPath);
  //     }
  //     const target = path.join(uploadPath, `${uploadFileName}`);
  //     try {
  //       const res = await ctx.helper.pipe({
  //         readableStream: stream,
  //         target,
  //       });
  //       console.log('fileUploadRes', res);
  //       // console.log('百度识别返回', baiduAPIBack);
  //       let returnPath = '';
  //       if (res.status === 200) {
  //         if (res.type === 'oss') {
  //           returnPath = await ctx.helper.formatUrl(res.url);
  //         } else {
  //           returnPath = `${app.config.static.prefix}${app.config.upload_http_path}/${uploadForder}/${uploadFileName}`;
  //         }
  //       }
  //       // const image = fs.readFileSync(target).toString('base64');
  //       let baiduAPIBack = {};
  //       if (app.config.baiduBusinessLicenseOCR === 'open') {
  //         baiduAPIBack = await siteFunc.baiduBusinessLicenseOCR(returnPath);
  //       }
  //       // const returnPath = `${app.config.static.prefix}${app.config.upload_http_path}/${uploadForder}/${uploadFileName}`;
  //       // const returnPath = `${app.config.static.prefix}${app.config.enterprise_http_path}/${uploadForder}/${uploadFileName}`;
  //       console.log('returnpath:', returnPath);
  //       await ctx.helper.renderSuccess(ctx, {
  //         message: '上传图片控制器',
  //         data: {
  //           baiduAPIBack,
  //           path: returnPath,
  //           fileName: uploadFileName,
  //         },
  //       });
  //     } catch (err) {
  //       // 必须将上传的文件流消费掉，要不然浏览器响应会卡死
  //       await sendToWormhole(stream);
  //       throw err;
  //     }
  //   } catch (e) {
  //     ctx.helper.renderFail(ctx, {
  //       message: e,
  //     });
  //   }
  // }

  async uploadEnterpriseImage() {
    const ctx = this.ctx;
    const app = ctx.app;
    try {
      const enterpriseIds = await ctx.helper.getScopeData('enterprise_ids');

      console.log('enterpriseIds', enterpriseIds);
      console.log('ctx', ctx);

      // 从 ctx.url 中提取企业ID
      const urlParams = new URLSearchParams(ctx.url.split('?')[1] || '');
      const enterpriseId = urlParams.get('id') || '';

      const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
      if (!enterpriseIds.includes(enterpriseId) && !ctx.url.endsWith(EnterpriseID)) {
        ctx.url = ctx.url + EnterpriseID;

      }
      const options = !_.isEmpty(app.config.doraUploadFile.uploadFileFormat) ? app.config.doraUploadFile.uploadFileFormat : {};
      // const dataType = 'stream';
      // let  returnPath;
      // const uploadConfigInfo = await ctx.service.uploadFile.create({
      //   type: 'local',
      //   uploadPath: process.cwd() + '/app/public',
      // });
      const stream = await ctx.getFileStream();

      const beforeUploadFileInfo = await getFileInfoByStream(ctx, options, stream);
      const {
        uploadForder,
        uploadFileName,
      } = beforeUploadFileInfo;

      // console.log('上传图片地址enterprise_path',app.config.enterprise_path)
      // const publicDir = options.upload_path || (process.cwd() + '/app/public');

      const uploadPath = `${app.config.upload_path}/${uploadForder}`;
      // const uploadPath = `${app.config.enterprise_path}/${uploadForder}`;
      // const uploadPath1 = `${app.config.enterprise_path}/${uploadForder}`;
      // console.log('上传图片地址uploadPath',app.config.enterprise_path,uploadPath1)

      if (!fs.existsSync(uploadPath)) {
        mkdirp.sync(uploadPath);
      }
      const target = path.join(uploadPath, `${uploadFileName}`);
      try {
        const res = await ctx.helper.pipe({
          readableStream: stream,
          target,
        });
        console.log('fileUploadRes', res);
        // console.log('百度识别返回', baiduAPIBack);
        let returnPath = '';
        if (res.status === 200) {
          if (res.type === 'oss') {
            returnPath = await ctx.helper.formatUrl(res.url);
          } else {
            returnPath = `${app.config.static.prefix}${app.config.upload_http_path}/${uploadForder}/${uploadFileName}`;
          }
        }
        // const image = fs.readFileSync(target).toString('base64');
        let baiduAPIBack = {};
        if (app.config.baiduBusinessLicenseOCR === 'open') {
          baiduAPIBack = await siteFunc.baiduBusinessLicenseOCR(returnPath);
        }
        // const returnPath = `${app.config.static.prefix}${app.config.upload_http_path}/${uploadForder}/${uploadFileName}`;
        // const returnPath = `${app.config.static.prefix}${app.config.enterprise_http_path}/${uploadForder}/${uploadFileName}`;
        console.log('returnpath:', returnPath);
        await ctx.helper.renderSuccess(ctx, {
          message: '上传图片控制器',
          data: {
            baiduAPIBack,
            path: returnPath,
            fileName: uploadFileName,
          },
        });
      } catch (err) {
        // 必须将上传的文件流消费掉，要不然浏览器响应会卡死
        await sendToWormhole(stream);
        throw err;
      }
    } catch (e) {
      ctx.helper.renderFail(ctx, {
        message: e,
      });
    }
  }

  async uploadEnterpriseFile() {
    const ctx = this.ctx;
    const params = ctx.query;
    try {
      const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
      const stream = await ctx.getFileStream();

      const uploadPath = path.join(ctx.app.config.upload_path, EnterpriseID);
      // 生成随机戳
      const extname = path.extname(stream.filename).toLowerCase(); // 文件扩展名称
      const fileName = path.basename(stream.filename, extname); // 文件名称
      let uploadFileName = fileName + `_${nanoid(10)}` + extname;

      if (params.prefix) {
        uploadFileName = params.prefix + '_' + uploadFileName;
      }

      if (!fs.existsSync(uploadPath)) {
        mkdirp.sync(uploadPath);
      }
      const target = path.join(uploadPath, `${uploadFileName}`);
      try {
        const res = await ctx.helper.pipe({
          readableStream: stream,
          target,
        });
        console.log('fileUploadRes', res);
        // const staticSrc = path.join('/static', ctx.app.config.upload_http_path, EnterpriseID, uploadFileName);
        let staticSrc = '';
        if (res.status === 200) {
          if (res.type === 'oss') {
            staticSrc = await ctx.helper.formatUrl(res.url);
          } else {
            staticSrc = path.join('/static', ctx.app.config.upload_http_path, EnterpriseID, uploadFileName);
          }
        }

        await ctx.helper.renderSuccess(ctx, {
          message: '上传图片控制器',
          data: {
            filename: uploadFileName,
            staticSrc,
          },
        });
        // await awaitWriteStream(stream.pipe(writeStream));
      } catch (err) {
        // 必须将上传的文件流消费掉，要不然浏览器响应会卡死
        await sendToWormhole(stream);
        throw err;
      }
    } catch (e) {
      ctx.helper.renderFail(ctx, {
        message: e,
      });
    }
  }

  async uploadFrontIDCardImage() {
    const ctx = this.ctx;
    const app = ctx.app;
    try {
      const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
      ctx.url = ctx.url + '?id=' + EnterpriseID;
      const options = !_.isEmpty(app.config.doraUploadFile.uploadFileFormat) ? app.config.doraUploadFile.uploadFileFormat : {};

      // const dataType = 'stream';
      // let  returnPath;
      // const uploadConfigInfo = await ctx.service.uploadFile.create({
      //   type: 'local',
      //   uploadPath: process.cwd() + '/app/public',
      // });
      const stream = await ctx.getFileStream();

      const beforeUploadFileInfo = await getFileInfoByStream(ctx, options, stream);
      console.log('============beforeUploadFileInfo============');
      console.log(beforeUploadFileInfo);
      const {
        uploadForder,
        uploadFileName,
      } = beforeUploadFileInfo;
      // const publicDir = options.upload_path || (process.cwd() + '/app/public');
      const uploadPath = `${app.config.enterprise_path}/${uploadForder}`;
      // console.log(publicDir);
      // console.log(uploadPath);
      if (!fs.existsSync(uploadPath)) {
        mkdirp.sync(uploadPath);
      }
      const target = path.join(uploadPath, `${uploadFileName}`);
      const writeStream = fs.createWriteStream(target);
      try {
        await awaitWriteStream(stream.pipe(writeStream));
      } catch (err) {
        // 必须将上传的文件流消费掉，要不然浏览器响应会卡死
        await sendToWormhole(stream);
        throw err;
      }
      const image = fs.readFileSync(target).toString('base64');
      const baiduAPIBack = await siteFunc.FrontIdCardScan(image);
      // console.log('百度识别返回', baiduAPIBack);
      let returnPath = `${app.config.static.prefix}${app.config.enterprise_http_path}/${uploadForder}/${uploadFileName}`;
      if (app.config.storageType === 'oss') {
        const res = await ctx.helper.pipe({
          readableStream: stream,
          target,
        });
        console.log('fileUploadRes', res);
        returnPath = await ctx.helper.formatUrl(res.url);
      }

      await ctx.helper.renderSuccess(ctx, {
        message: '上传图片控制器',
        data: {
          baiduAPIBack,
          path: returnPath,
          filename: uploadFileName,
          localPath: uploadPath,
        },
      });
      // this.deleteFileReal(uploadFileName, uploadPath);
    } catch (e) {
      ctx.helper.renderFail(ctx, {
        message: e,
      });
    }
  }

  async uploadBackIDCardImage() {
    const ctx = this.ctx;
    const app = ctx.app;
    try {
      const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
      ctx.url = ctx.url + '?id=' + EnterpriseID;
      const options = !_.isEmpty(app.config.doraUploadFile.uploadFileFormat) ? app.config.doraUploadFile.uploadFileFormat : {};

      // const dataType = 'stream';
      // let  returnPath;
      // const uploadConfigInfo = await ctx.service.uploadFile.create({
      //   type: 'local',
      //   uploadPath: process.cwd() + '/app/public',
      // });
      const stream = await ctx.getFileStream();

      const beforeUploadFileInfo = await getFileInfoByStream(ctx, options, stream);
      const {
        uploadForder,
        uploadFileName,
      } = beforeUploadFileInfo;
      // const publicDir = options.upload_path || (process.cwd() + '/app/public');
      const uploadPath = `${app.config.enterprise_path}/${uploadForder}`;
      if (!fs.existsSync(uploadPath)) {
        mkdirp.sync(uploadPath);
      }
      const target = path.join(uploadPath, `${uploadFileName}`);
      const writeStream = fs.createWriteStream(target);
      try {
        await awaitWriteStream(stream.pipe(writeStream));
      } catch (err) {
        // 必须将上传的文件流消费掉，要不然浏览器响应会卡死
        await sendToWormhole(stream);
        throw err;
      }
      const image = fs.readFileSync(target).toString('base64');
      const baiduAPIBack = await siteFunc.BackIdCardScan(image);
      console.log('百度识别返回', baiduAPIBack);
      let returnPath = `${app.config.static.prefix}${app.config.enterprise_http_path}/${uploadForder}/${uploadFileName}`;
      if (app.config.storageType === 'oss') {
        const res = await ctx.helper.pipe({
          readableStream: stream,
          target,
        });
        console.log('fileUploadRes', res);
        returnPath = await ctx.helper.formatUrl(res.url);
      }
      await ctx.helper.renderSuccess(ctx, {
        message: '上传图片控制器',
        data: {
          baiduAPIBack,
          path: returnPath,
          filename: uploadFileName,
          localPath: uploadPath,
        },
      });
    } catch (e) {
      ctx.helper.renderFail(ctx, {
        message: e,
      });
    }
  }


  async getCompanyByCode() {
    const ctx = this.ctx;
    // const app = ctx.app;

    try {

      const param = ctx.request.body;
      const collections = [
        {
          name: 'adminusers',
          selfKey: 'adminUserId',
          foreignKey: '_id',
          asKey: 'adminUserId',
        },
      ];
      const unwindArray = [ 'adminUserId' ];
      const searchKeys = [ 'code' ];
      const files = {
        cname: 1,
        code: 1,
        phoneNum: '$adminUserId.phoneNum',
        userName: '$adminUserId.userName',
      };

      const list = await ctx.service.adminorg.unionQuery(
        {
          isPaging: '0',
          pageSize: parseInt(param.count),
          searchkey: param.code,
        },
        {
          searchKeys,
          collections,
          unwindArray,
          files,
        }
      );

      ctx.helper.renderSuccess(ctx, {
        message: '返回成功',
        data: list.docs,
      });

    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }


  }

  // 判断数据库连接状态
  async adoConnection() {
    const { ctx } = this;
    try {
      // const district = await ctx.model.District.findOne({}, { area_code: 1 });
      const district = await ctx.service.db.findOne('District', {}, { area_code: 1 });
      const version = fs.readFileSync('version', 'utf-8') || '';
      if (district && district.area_code) {
        ctx.helper.renderSuccess(ctx, {
          message: 'mongoose connected',
          data: {
            version: version.replace(/(\n|VERSION=)/g, ''),
          },
        });
      } else {
        ctx.helper.renderCustom(ctx, {
          status: 500,
          message: 'mongoose disconnected',
          data: version,
        });
      }
    } catch (err) {
      ctx.helper.renderCustom(ctx, {
        status: 500,
        message: JSON.stringify(err),
      });
    }
  }

  // 空请求
  async emptyRequest() {
    this.ctx.body = {
      status: 200,
      message: '空请求',
    };
  }
  async getAuthCookieName() {
    const { ctx, config } = this;
    ctx.helper.renderCustom(ctx, {
      message: '获取成功',
      data: 'admin_' + config.auth_cookie_name,
    });
  }

  // 登陆时间限制
  async getAuthLogin() {
    const { ctx } = this;
    const { isLimitOperation, limitOperationDoc } = await ctx.service.systemConfig.authAdminEnabel();
    ctx.helper.renderSuccess(ctx, {
      message: '获取成功',
      data: {
        isLimitOperation,
        limitOperationDoc,
      },
    });
  }
}

module.exports = SystemConfigController;
