/*
 * @Author: WZQ
 * @Date: 2021-06-10 09:03:25
 * @LastEditTime: 2021-06-10 15:17:54
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \jcqlc\lib\plugin\egg-jk-spotRecord\app\model\spotProject.js
 */
// 现场记录单危害因素分类表
module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  // 单个项目的 现场采样单汇总情况表
  const SpotRecordGroupSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    spotProjectID: String, // 汇总表id

    companyName: String, // 受检单位名称
    projectNumber: String, // 项目编号
    projectName: String, // 项目名称
    checkType: String, // 检测类型 (定期 评价 ...)
    harmFactor: String, // 危害因素
    samplingTime: Date, // 采样/检测 时间
    samplingFun: String, // 采样方法
    standard: String, // 采样依据
    batch: String, // 批次
    sampleEquipment: String, // 采样仪器
    instrument: Object, // 仪器
    spotRecordSort: String, // 类型

    // 声源 （仅限噪声存在）
    soundSource: {
      standard: String, // 标准声源
      soundNum: String, // 声源编号
      calibrationValue: String, // 校准值dB(A)
      deviationValue: String, // 偏差dB(A)
    },
    // 微小气候 检测仪器 （仅限微小气候存在）
    TMCDetector: {
      temperatureDName: String, // 温湿度检测仪
      temperatureDID: String, // 温湿度检测仪编号
      windDName: String, // 风速检测仪
      windDID: String, // 风速检测仪编号
      pressureDName: String, // 气压检测仪
      pressureDID: String, // 气压检测仪编号
    },
    // 被测设备 （仅限 高频电场 超高频电场 存在）
    testedEquipment: {
      name: String,
      params: String,
    },
    // 接触情况（仅限 高频电场 超高频电场 存在）
    contactSituation: {
      peopleNum: String, // 接触人数
      contactTime: String, // 接触时间
    },
    protective: String, // 防护措施（仅限 高频电场 超高频电场 存在）
    samplingConfirmDate: Date, // 采样完成确认时间

    signManage: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      signRole: String, // 陪同人 采样人 测量人
      name: String, // 姓名
      signPath: String, // 签名文件储存位置
      signTime: Date, // 签名时间
    }],

    // 天气情况
    weatherScene: {
      weather: String, // 天气
      pressure: String, // 气压
      temperature: String, // 温度
      humidity: String, // 湿度
    },

    createAt: { // 创建日期
      type: Date,
    },
    updateAt: { // 更新时间
      type: Date,
    },
  }, { timestamps: true });


  return mongoose.model('spotRecordGroup', SpotRecordGroupSchema, 'spotRecordGroup');
};
