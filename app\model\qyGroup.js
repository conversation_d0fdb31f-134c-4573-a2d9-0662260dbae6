/**
 * Created by Xiangmy on 2024/6/16.
 * 企业端角色对象
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const QyGroupSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    name: String, // 角色名
    power: [{ // 内部权限
      type: String,
      ref: 'AdminResource',
    }],
    date: { // 创建时间
      type: Date,
      default: Date.now,
    },
    topEnterpriseID: {
      type: String,
      ref: 'Adminorg',
    },
    enable: Bo<PERSON>an,
    comments: String, // 描述
  });

  return mongoose.model('QyGroup', QyGroupSchema);
};
