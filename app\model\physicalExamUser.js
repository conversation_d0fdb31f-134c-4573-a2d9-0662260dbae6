/**
 * Created By htt
 * 体检端用户对象
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const CryptoJS = require('crypto-js');
  const physicalExamGroupID = app.config.groupID.physicalExamGroupID || ''; // 体检用户角色ID

  const PhysicalExamUserSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    userName: {
      // 用户名
      type: String,
      default: '',
    },
    name: {
      // 姓名
      type: String,
      default: '',
    },
    phoneNum: {
      type: String,
      sparse: true,
    },
    email: {
      type: String,
      default: '',
    },
    password: {
      type: String,
      set(val) {
        return CryptoJS.SHA256(val + app.config.salt_sha2_key).toString();
      },
    },
    org_id: {
      // 所属体检机构id
      type: String,
      default: '',
      ref: 'PhysicalExamOrg', // 关联体检机构表
    },
    org: {
      // 所属机构名, 后面已经被弃用了，不建议使用
      type: String,
      default: '',
    },
    department_id: {
      // 所属部门id
      type: String,
      default: '',
    },
    department: {
      // 所属部门名称
      type: String,
      default: '',
    },
    group: {
      type: String,
      default: physicalExamGroupID,
      ref: 'AdminGroup',
    },
    countryCode: {
      // 手机号前国家代码
      type: String,
      default: '86',
    },
    ctime: {
      // 创建/更改时间
      type: Date,
      default: Date.now,
    },
    logo: {
      // 头像
      type: String,
      default: '/static/upload/images/defaultlogo.png',
    },
    state: {
      // 在职状态
      type: Boolean,
      default: true,
    },
    enable: {
      // 是否能用
      type: Boolean,
      default: true,
    },
    comments: String, // 备注
  });

  // PhysicalExamUserSchema.index({ phoneNum: 1 });
  PhysicalExamUserSchema.index(
    { phoneNum: 1 },
    {
      unique: true,
      partialFilterExpression: { phoneNum: { $exists: true } },
    }
  );


  return mongoose.model('PhysicalExamUser', PhysicalExamUserSchema, 'physicalExamUsers');


};

