

// const _ = require('lodash');
const mkdirp = require('mkdirp');
const awaitWriteStream = require('await-stream-ready').write;
const path = require('path');
const fs = require('fs');
const AdmZip = require('adm-zip');
// const axios = require('axios');
const { CaseCardRecordStatusEnum, MilestoneStatusEnum } = require('../../enums/tjCaseCard');

const Controller = require('egg').Controller;
class ZywsToolsController extends Controller {
  async uploadCaseCard() {
    const { ctx, app } = this;

    try {
      const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
      const configFilePath = path.resolve(app.config.upload_path, EnterpriseID);
      console.log(312313, EnterpriseID, configFilePath);

      try {
        await mkdirp.sync(configFilePath);
      } catch (error) {
        console.log(4444, error);
      }
      const dataForm = await this.parseDataForm(configFilePath);
      console.log(654, dataForm);
      if (dataForm && dataForm.fileType === '.zip') {
        // 读取压缩包文件。并解压
        const fileList = await this.unzipToCurrentDir(dataForm.src);

        for (let i = 0; i < fileList.length; i++) {
          const fileItem = fileList[i];
          const filename = path.basename(fileItem);
          let doc = await ctx.service.tjCaseCard.item({ fileName: filename, uid: EnterpriseID });
          if (doc) {
            await ctx.service.tjCaseCard.setStatus(doc._id, CaseCardRecordStatusEnum.PENDING_PARSE);
            await ctx.service.tjCaseCard.setMilestone(doc._id, {
              status: MilestoneStatusEnum.PENDING_PARSE.code,
            });
          } else {
            doc = await ctx.service.tjCaseCard.create({
              fileName: filename,
              staticName: fileItem,
              uid: EnterpriseID,
            });
          }

          const task = await ctx.model.TaskBatch.create({
            totalTasks: 1,
            completedTasks: 0,
            enterpriseId: EnterpriseID,
            isCompleted: false,
            subTaskRes: [],
          });
          await ctx.service.rabbitMq.produce({
            queue: 'zyjk_qy_personTjReport_analysis',
            message: {
              _id: doc._id,
              taskBatchId: task._id,
            },
            durable: true,
          });
        }

        ctx.helper.renderSuccess(ctx, {
          data: {},
          message: 'success',
          status: 200,
        });
        return;
      }

      // 查询是否已存在该文件
      const doc = await ctx.service.tjCaseCard.item({ fileName: dataForm.fileName, uid: EnterpriseID });
      console.log(555555, doc);
      let res = null;
      if (doc) {
        res = doc;
        await ctx.service.tjCaseCard.setStatus(doc._id, CaseCardRecordStatusEnum.PENDING_PARSE);
        await ctx.service.tjCaseCard.setMilestone(doc._id, {
          status: MilestoneStatusEnum.PENDING_PARSE.code,
        });
      } else {
        res = await ctx.service.tjCaseCard.create({
          fileName: dataForm.fileName,
          staticName: dataForm.staticName,
          uid: EnterpriseID,
        });
        console.log(777, res);
      }

      ctx.helper.renderSuccess(ctx, {
        data: {},
        message: 'success',
        status: 200,
      });

      ctx.service.tjCaseCard.aiParseDoc(res);
    } catch (error) {
      console.log(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  async unzipToCurrentDir(zipPath) {
    const zip = new AdmZip(zipPath);
    const outputDir = path.dirname(zipPath);

    // 获取压缩包内所有文件的相对路径
    const entries = zip.getEntries();
    const pdfFiles = [];

    entries.forEach(entry => {
      const isPDF = entry.name.toLowerCase().endsWith('.pdf'); // 检查是否是 PDF
      const isNotMacOSX = !entry.entryName.includes('__MACOSX/'); // 排除 macOS 元数据

      if (isPDF && isNotMacOSX) {
        const outputPath = path.join(outputDir, entry.entryName);
        fs.mkdirSync(path.dirname(outputPath), { recursive: true }); // 创建父目录
        fs.writeFileSync(outputPath, entry.getData());
        pdfFiles.push(entry.entryName); // 记录 PDF 文件路径
      }
    });

    return pdfFiles;
  }

  async parseDataForm(configFilePath) {
    const { ctx } = this;
    let staticName = '';
    let fileType = '';
    let target = '';
    const parts = ctx.multipart({
      autoFields: true,
    });
    let stream;
    while ((stream = await parts()) != null) {
      if (!stream.filename) {
        // 注意如果没有传入直接返回
        return;
      }

      staticName = new Date().getTime() + '_' + stream.filename;
      fileType = path.extname(staticName);
      if (!fs.existsSync(configFilePath)) {
        try {
          await mkdirp(configFilePath);
        } catch (err) {
          console.log(3333, err);
        }
      }

      target = path.resolve(configFilePath, staticName);
      // 生成一个文件写入 文件流
      const writeStream = fs.createWriteStream(target);
      try {
        // 异步把文件流 写入
        await awaitWriteStream(stream.pipe(writeStream));
      } catch (error) {
        console.log(error);
        // 如果出现错误，关闭管道,防止浏览器响应卡死
        // await sendToWormhole(stream);
        writeStream.destroy();
        throw error;
      }
    }

    return {
      ...parts.field,
      staticName,
      fileType,
      src: target,
    };
  }

  // 获取个案卡列表
  async getList() {
    const { ctx } = this;
    const { params, pagination } = ctx.request.body;

    const res = await ctx.service.tjCaseCard.getList({
      params,
      pagination,
    });

    if (res) {
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: 'success',
        status: 200,
      });
    }
  }

  // 获取个案卡详情
  async getCaseCard() {
    const { ctx } = this;
    const { _id } = ctx.query;
    const res = await ctx.service.tjCaseCard.getList({ params: { recordId: _id } });
    const data = (res.tableData && res.tableData[0]) ? res.tableData[0] : null;
    // await ctx.service.tjCaseCard.setMilestone(_id, {
    //   status: MilestoneStatusEnum.REVIEWING.code,
    // });
    if (data) {
      ctx.helper.renderSuccess(ctx, {
        data,
        message: '获取成功',
        status: 200,
      });
    } else {
      ctx.helper.renderSuccess(ctx, {
        data,
        message: '获取失败',
        status: 200,
      });
    }
  }

  // 删除个案卡
  async delCaseCard() {
    const { ctx, config } = this;
    const { ids } = ctx.request.body;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';

    console.log(3131, ids);
    for (let i = 0; i < ids.length; i++) {
      const id = ids[i];
      const doc = await ctx.service.tjCaseCard.item({ _id: id });
      console.log(3333, doc);
      if (doc && doc.staticName) {
        const filepath = path.resolve(config.caseCard_path, EnterpriseID, doc.staticName);
        try {
          fs.unlinkSync(filepath);
        } catch (err) {
          console.log(err);
        }
        if (filepath.endsWith('.pdf')) {
          const wordFilePath = filepath.replace('.pdf', '.docx');
          try {
            fs.unlinkSync(wordFilePath);
          } catch (err) {
            console.log(err);
          }
        }
      }
    }
    const res = await ctx.service.tjCaseCard.removes(ids);
    console.log(9999, res);
    if (res && res.deletedCount) {
      ctx.helper.renderSuccess(ctx, {
        data: {},
        message: '删除成功',
        status: 200,
      });
    } else {
      // 删除失败
      ctx.helper.renderSuccess(ctx, {
        data: {},
        message: '删除失败',
        status: 200,
      });
    }
  }

  // 更新个案卡
  async updateCaseCard() {
    const { ctx } = this;
    const { type, _id, data } = ctx.request.body;
    console.log(4134, data);
    if (type === 'form') {
      await ctx.service.tjCaseCard.update({ _id }, {
        $set: {
          baseForm: data.baseForm,
          personalHistory: data.personalHistory,
          mainFactorsData: data.mainFactorsData,
          employmentHistory: data.employmentHistory,
          ohItemClassResult: data.ohItemClassResult,
        },
      });

      const caseCardRecord = await ctx.model.CaseCardRecord.findOne({ _id });

      // 生成suspect表数据
      const result = await ctx.service.tjCaseCard.generateSuspect(data, caseCardRecord);
      if (result) {
        await ctx.service.tjCaseCard.update({ _id }, {
          $set: {
            dockingStatus: result.status,
            dockingMsg: result.msg,
            dockingTime: new Date(),
          },
        });
      }

    } else if (type === 'dockingStatus') {
      await ctx.service.tjCaseCard.update({ _id }, {
        $set: {
          dockingStatus: data.dockingStatus,
          dockingTime: new Date(),
          baseForm: data.baseForm,
          personalHistory: data.personalHistory,
          mainFactorsData: data.mainFactorsData,
          ohItemClassResult: data.ohItemClassResult,
        },
      });
    }

    ctx.helper.renderSuccess(ctx, {
      data: {},
      message: '保存成功',
      status: 200,
    });
  }

  // 重新解析个案卡
  async reParseCard() {
    const { ctx } = this;
    const { _id } = ctx.request.body;
    const doc = await ctx.service.tjCaseCard.item({ _id });
    if (doc) {
      ctx.service.tjCaseCard.aiParseDoc(doc);
      ctx.helper.renderSuccess(ctx, {
        data: {},
        message: 'success',
        status: 200,
      });
      return;
    }
    ctx.helper.renderSuccess(ctx, {
      data: {},
      message: 'success',
      status: 200,
    });
  }

  // 对接个案卡
  async dockingCaseCard() {
    console.log('1');
  }

  async reportRejectDocking() {
    const { ctx } = this;
    try {
      const { id } = ctx.request.body;
      await ctx.service.tjCaseCard.setMilestone(id, {
        status: MilestoneStatusEnum.REPORT_ABANDONED.code,
      });
      ctx.helper.renderSuccess(ctx, {
        data: { mes: '', success: true },
      });
    } catch (error) { /* empty */ }
  }
}
module.exports = ZywsToolsController;
