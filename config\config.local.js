const path = require('path');
const fs = require('fs');
module.exports = appInfo => {
  return {
    admin_root_path: 'http://localhost',
    // DEV_CONFIG_MODULES_BEGIN
    dev_modules: [
      // 'dataBigScreen', // 数据大屏
      // 'oneImport', // 一键导入报告
      // 'warnSignGenerate', // 警示标识
      // 'autoForm', // 人员体检表推荐
      // 'company', // 企业概况
      // 'facilities', // 防护设施录入
      // 'jobHealth', // 危害监测管理
      // 'serviceOrg', // 机构管理
      // 'testAppointment',
      'warnNotice', // 警示告知管理
      // 'propagate', // 健康培训管理
      // 'repropagate', // 重构健康培训管理
      // 'ledger', // 工艺流程
      // 'diseasesOverhaul', // 设备检修管理
      // 'preventionFunds', // 防治经费
      // 'preventionFunding', // 北元防治经费
      // 'preventionFundingStatistics', // 北元防治经费统计
      // 'preventionPlan', // 北元防治计划
      // 'preventionPlanImplementation', // 北元防治计划实施
      // 'preventionPlanStatistics', // 北元防治计划统计
      // 'millConstruction', // 车间岗位管理
      // 'navbar', // 导航栏
      // 'record', // 职业卫生档案
      // 'employees', // 人员导入
      // 'employeesInfoManage', // 人员信息管理
      // 'project', // 三同时项目管理
      // 'test', // 测试
      // 'dashboard', // 主页
      // 'setting', // 设置
      // 'systemConfig',
      // 'system OptionLog',
      // 'onlineDeclaration', // 危害申报管理
      // 'sanitaryInspection', // 日常检查管理
      // 'doublePrevention', // 双预防
      // 'role', // 人员架构
      // 'roleSecurity', //创伤急救机构
      // 'defendproducts', // 个人防护用品发放
      // 'protectionCloudManagement', // 防护用品云管理
      // 'healthcheck', // 职业健康监护
      // 'harmFactorManage', // 职业卫生助企工具
      // 'orgApply', // 企业申请
      // 'preventAssess', // 防治评估
      // 'riskAssessment', // 风险评估
      // 'warning', // 预警
      // 'codehub', // 工具ui文档库
      // 'manageSystem', // 管理制度
      // 'manageSystemSecurity', //创伤急救制度设置
      // 'manageEmergency', // 应急救援演练
      // 'testPaper', // 试卷管理
      // 'employeesTrainingPlan', // 员工培训计划
      // 'adminTraining', // 管理员培训
      // 'groupComanyIndex', // 集团公司首页
      // 'regulations', // 法律法规(集团可以crud)
      // 'regulationsView', // 法律法规查看(分子公司只能查看)
      // 'onlineMonitoring', // 在线监测
      // 'recordMakeManage' // 档案托管管理
      // 'certificate', // 证书管理
      // 'workFlow', // 工作流管理
      // 'hzWarning', // 杭州预警处置页面
      // 'butlerProjects', // 管家服务清单
      // 'operateLog', // 操作日志
      // 'detectionHotspots', // 检测数据热点图
      // 'messageNotification', // 消息通知
      // 'questionnaire', // 问卷列表
      // 'templateManage', // 模板管理
      // 'violationInfoManage', // 违章信息管理
      // 'dailyMonitoring', // 日常监测
      // 'hazardManagement', // 危害防治
      // 'occupationalHealthCheckList', // 职业健康体检清单管理
      // 'standardLibrary', // 标准库
      // 'healthMachineCheck', // 健康一体机
      // 'indicatorsTrendAnalysis', // 重要指标趋势分析
      // 'harmFactorsMap', // 危害因素分布图
      // 'checkItems', // 检查项目管理
      // 'checkItemsAudit', // 检查项目审核
      // 'checkItemManage', // 指标字典管理
      // 'checkupPackage', // 体检套餐
      // 'qyGroup', // 功能配置(企业角色管理)
      // 'policyManage', // 权限管理
      // 'tjPlanManage', // 体检计划管理
      // 'physicalExam', // 体检机构管理
      // 'assignPhyExam', // 分配成员单位的体检机构
      // 'aidNetwork', // 创伤急救网络
      // 'physicalExamination', // 体检人员查询
      // 'adminorgGov', // 企业列表
      // 'employmentInjury', // 工伤员工探访
      // 'memberUnitsStatistic', // 成员单位体检统计
      // 'healthExamConclusion', // 按维度统计职业健康体检结论
      // 'diseaseDiagnosisData', // 年度职业病诊断数据统计
      // 'reviewOverdueList', // 复查超期列表
      // 'reviewOverdueStatistic', // 复查超期统计
      // 'healthExamRate', // 职业健康检查体检率统计
      // 'abnormalHealthStatistic', // 职业健康检查异常情况统计
      // 'groupExamPlan', // 集团体检预约计划
      // 'welfareExamPlan', // 福利体检预约计划
      // 'baseExamPlan', // 基地/成员单位体检预约计划
      // 'deviceExamPlan', // 装置体检预约计划
      // 'deviceExamArrangement', // 装置体检预约安排
      // 'reviewExamArrangement', // 复查预约安排
      // 'beforeEmploymentExam', // 岗前预约安排
      // 'healthCheckForm', // 职业健康检查推荐表
      // 'personTjReport', // 个案卡解析
      // 'crudDemo',
      // 'whGoods', // 万华商城商品管理
      // 'whGoodsMain', // 万华商城订单记录
      // 'whIndustrialInjury', // 万华全球HSE工伤管理
      // 'protectiveStatistics', // 防护用品管理-防护用品统计
      // 'harmFactorAnalysis', // 危害因素检测结果统计分析
      // 'harmFactorList', // 危害因素管理-危害因素列表
      // 'protectiveStatistics', // 防护用品统计
      // 'wj', // surveyking问卷管理
    ],
    // DEV_CONFIG_MODULES_END
    storageType: process.env.storageType
      ? process.env.storageType.replace('\n', '')
      : 'oss', // 存储类型： local, oss,
    oss: {
      accessKeyId: process.env.accessKey
        ? process.env.accessKey.replace('\n', '')
        : 'LTAI5tQphaDkN48ZFkT2Tqwz',
      accessKeySecret: process.env.secretKey
        ? process.env.secretKey.replace('\n', '')
        : '******************************',
      endPoint: process.env.endPoint
        ? process.env.endPoint.replace('\n', '')
        : 'https://zyws-net.oss-cn-hangzhou.aliyuncs.com',
      region: process.env.region
        ? process.env.region.replace('\n', '')
        : 'oss-cn-hangzhou',
      timeout: process.env.ossTimeout
        ? process.env.ossTimeout.replace('\n', '')
        : '60s',
      buckets: {
        default: {
          name: process.env.bucket
            ? process.env.bucket.replace('\n', '')
            : 'zyws-net',
          accessPolicy: process.env.accessPolicy
            ? process.env.accessPolicy.replace('\n', '')
            : 'private', // 是否是私有的bucket，默认是空，private私有
        },
      },
    },
    mongoose: {
      client: {
        // url: 'mongodb://mdb.duopu.cn:25000/zyws-by',
        // url: 'mongodb://192.168.18.213:27017/fujian',
        // url: 'mongodb://127.0.0.1:27017/zyws-wh',
        // url: 'mongodb://127.0.0.1:27017/zyws-by1218?retryWrites=false',
        // url: 'mongodb://dbAdmin:asER^&10O@192.168.66.162:31017,192.168.1.43:31018,192.168.66.175:31019/zyws-wh0425?replicaSet=rs0&authSource=admin&ssl=false',
        // url: 'mongodb://127.0.0.1:27017/sxcc1027',
        url: 'mongodb://127.0.0.1:27017/zyws-by1218',
        // url: 'mongodb://192.168.19.196:27017/zyws-by823?replicaSet=xmyReplicaSet',
        // url: 'mongodb://dbadmin:<EMAIL>:25000/frameData?authSource=admin',
        // url: 'mongodb://dbadmin:<EMAIL>:25000/zyws-by822?authSource=admin',
        // url: 'mongodb://dbadmin:<EMAIL>:25000/zyws-sxcc?authSource=admin',
        // url: '****************************************************************',
        // url: 'mongodb://vango:<EMAIL>:62717/zyws-by823?authSource=admin',
        // url: '***************************************************************************************************************************************************',
        // url: 'mongodb://dbAdmin:asER^&10O@192.168.66.162:31017,192.168.1.43:31018,192.168.66.175:31019/zyws-wh0425?replicaSet=rs0&authSource=admin&ssl=false',
        options: {
          // authSource: 'admin',
          // dbName: 'shanxi',
          // user: 'dbadmin',
          // pass: 'DpPass6789',
          useNewUrlParser: true,
          useUnifiedTopology: true,
          useCreateIndex: true,
          keepAlive: 3000,
          readPreference: 'nearest',
          ssl: false,
        },
      },
      tools: {
        url: 'mongodb://mdb.duopu.cn:25000',
        // url: 'mongodb://127.0.0.1:27017/framData',
        options: {
          authSource: 'admin',
          dbName: 'tools',
          user: 'dbadmin',
          pass: 'DpPass6789',
          useNewUrlParser: true,
          useUnifiedTopology: true,
          useCreateIndex: true,
          keepAlive: 3000,
          readPreference: 'nearest',
          ssl: false,
        },
      },
      jkqy: {
        url: 'mongodb://dbadmin:<EMAIL>:25000/shanxi?authSource=admin',
        // url: 'mongodb://127.0.0.1:27017/company',
      },
      cms: {
        url: '*********************************************************************',
        // url: 'mongodb://127.0.0.1:27017/company',
        // url: 'mongodb://dbadmin:<EMAIL>:25000/frameData?authSource=admin',
      },
    },
    redis: {
      client: {
        port: 63379,
        host: 'www.beasts.wang',
        password: 'Vancy0727wangxi',
        // port: 6379,
        // host: '127.0.0.1',
        // password: '',
        db: 1,
      },
    },
    iServiceHost: 'http://iservice.beasts.wang',
    iService2Host: 'http://localhost:3000',
    // iServiceHost: 'http://127.0.0.1:8666',
    // oapi里的不一样要打开
    // upload_http_path: '/upload/images',
    auth_cookie_name: 'zyws_xhl_qy',
    // mongodb相关路径
    mongodb: {
      binPath: '',
      backUpPath: path.join(appInfo.baseDir, 'databak/'),
    },
    upload_path: process.cwd() + '/app/public/upload/enterprise',
    // 上行用户自行上传文件的http路径， 代码拼接：/static + upload_http_path + /EnterpriseID/ + 文件名
    upload_http_path: '/upload/enterprise',
    app_env_version: 'develop',
    siteFile: {
      '/favicon.ico': fs.readFileSync(
        path.join(appInfo.baseDir, 'app/public/favicon-zfy.ico')
      ),
    },
    static: {
      prefix: '/static',
      dir: [
        path.join(appInfo.baseDir, 'backstage/dist'),
        path.join(appInfo.baseDir, 'app/public'),
        // 'E:/jkqySuper/app/public',
        // 'E:/jkqyOperate/app/public',
        // 'E:/zyjk/app/public',
        // 'D:/workspace/oapi/app/public',
        '/opt/pubilc/super',
        '/opt/pubilc/jc',
        '/opt/public',
      ],
      maxAge: 31536000,
    },
    logger: {
      dir: path.join(appInfo.baseDir, 'logs'),
    },
    // 是否开启以版本号(手动定版)控制的静态界面更新 默认为true  注意：手动更新优先级大于自动更新
    isVersionUpdate: false,
    // 是否开启静态界面自动更新 默认为false  警告：慎重！！！如果改为false，强烈建议手动清空缓存或开启手动定版并更新版本号后再加载，否则有可能加载的并非最新界面
    isAutoUpdate: true,
    // server_path: 'http://************:8001',
    // server_path: 'http://0.0.0.0:8001',
    server_path: 'http://127.0.0.1:7001',
    // server_api: 'http://************/api',
    // server_api: 'http://0.0.0.0:8001/api',
    server_api: 'http://127.0.0.1:7001/api',
    branch: 'by', // 测试用的，可以删
    keyCloakApiHost: 'http://oidc.beasts.wang/auth',
    oidc: {
      // 北元oidc认证系统
      keyCloak: {
        api_host: process.env.KEYCLOAK_API_HOST, // keycloak的api地址
        realm_name: process.env.KEYCLOAK_REALM_NAME, // keycloak的realm名称
        client_id: process.env.KEYCLOAK_CLIENT_ID, // keycloak的client_id
        client_secret: process.env.KEYCLOAK_CLIENT_SECRET, // keycloak的client_secret
        redirect_uri: process.env.KEYCLOAK_REDIRECT_URI, // keycloak的登录回调地址
        post_logout_redirect_uri: process.env.KEYCLOAK_POST_LOGOUT_REDIRECT_URI, // keycloak的退出回调地址
      },
    },
    byOidcAutoCreate: false, // 是否自动登录
    allowUnRegOrg: false,
    // 基础版套餐ID
    // defaultPayCategoryID: 't4WLsyWvF',
    // removeWarningPermission: true, // 企业是否有解除预警的权限
    filePreviewHost: 'https://filepreview.qyehs.com',
    rabbitMq: {
      url: 'amqp://guest:<EMAIL>:5673',
      // url: 'amqp://guest:guest@127.0.0.1:5672',
      queues: [
        {
          name: 'zyjk_qy_file_record',
          service: 'fileRecord',
          callback: 'dealTask',
        },
        {
          name: 'zyjk_qy_file_subRecord',
          service: 'fileRecord',
          callback: 'dealSubTask',
        },
        {
          name: 'zyjk_qy_file_archive',
          service: 'fileRecord',
          callback: 'dealArchiveTask',
        },
        {
          name: 'zyjk_qy_file_compression',
          service: 'fileRecord',
          callback: 'dealCompressionTask',
        },
        {
          name: 'zyjk_qy_file_archive_six_batch',
          service: 'fileRecord',
          callback: 'dealArchiveSixBatchTask',
        },
        {
          name: 'zyjk_qy_checkAssessment_save',
          service: 'commonService',
          callback: 'saveCheckAssessment',
        },
        {
          name: 'zyjk_qy_personTjReport_analysis',
          service: 'tjCaseCard',
          callback: 'analysisPersonTjReport',
        },
      ],
      // 其余配置 参考 https://www.npmjs.com/package/amqplib
    },
    // 不同端的域名
    domainNames: {
      super: 'http://127.0.0.1:7003',
      operate: 'http://127.0.0.1:7005',
      enterprise: 'http://127.0.0.1:7001/',
      enterprise_int: 'http://127.0.0.1:7001',
      service: 'http://127.0.0.1:7007',
    },
    // 特殊企业项目是否互相可见开关
    isProjectVisibilityEnabled: process.env.isProjectVisibilityEnabled || '0',
    // 是否允许企业自行注册, 0: 不允许, 1: 允许
    isOrgSelfRegisterEnabled: process.env.isOrgSelfRegisterEnabled || '1',
    // 是否显示发放标准按钮选项
    isRadioButtonEnabled: process.env.isRadioButtonEnabled || '1',
    // 默认distributionStandard按钮选项 depart 为部门，mill 为工作场所
    distributionStandard: process.env.distributionStandard || 'depart',
    // 是否允许查看加密字段 0: 不允许, 1: 允许
    isShowEncryptField: process.env.isShowEncryptField || '0',
    // 职能机构是否为模版, 0: 不是, 1: 是
    isRoleTemplate: process.env.isRoleTemplate || '1',
    // 危害申报模版是否隐藏, 0: 不隐藏, 1: 隐藏
    isHazardDeclarationTemplateHidden:
      process.env.isHazardDeclarationTemplateHidden || '0',
    // master和集团分支功能区分开关
    isGroupBranch: process.env.isGroupBranch || '0',
    // 是否开启发送验证码校验手机号是否存在
    shouldVerifyPhoneExistence: process.env.shouldVerifyPhoneExistence || '0',
    occupationalCheckInfo: {
      disable: false,
      immediate: false,
      cron: process.env.occupationalCheckInfoCron || '0 0 5 * * *', // 每日凌晨5点执行一次
      type: 'worker', // 指定每次只有一个 随机的worker 执行
    },
    // 安全帽定期生成预警  每天凌晨2点10分执行一次  cron表达式  */1表示刚开始2点10分触发一次，之后每一天2点10分触发一次
    hardHat: {
      disable: false,
      immediate: false,
      cron: '0 10 2 */1 * ?',
      type: 'worker', // 指定每次只有一个 随机的worker 执行
    },

    // 告知书签订预警
    statusChangeWarn: {
      disable: false,
      immediate: false,
      cron: '0 20 2 */1 * ?',
      type: 'worker', // 指定每次只有一个 随机的worker 执行
    },
    passwordValidityPeriod: false,
    permissionRedisInfo: {
      disable: false,
      immediate: true,
      cron: process.env.permissionRedisInfoCron || '0 0 5 * * *', // 每日凌晨5点执行一次
      type: 'worker', // 指定每次只有一个 随机的worker 执行
    },
    bigDataScreenUser: process.env.bigDataScreenUser || '08070004',
    harmCountStatistics: {
      disable: true,
      immediate: true,
      interval: '1h',
      type: 'worker',
    },
    // 工作场所名称
    // workplaceName: {
    //   mill: '厂房',
    //   workspaces: '车间',
    //   stations: '岗位',
    // },
    workplaceName: {
      mill: '厂房',
      workspaces: '装置',
      stations: '工序',
    },
    // workplaceName: {
    //   mill: '厂房',
    //   workspaces: '装置',
    //   stations: '工序',
    // },
    // 反馈建议收件地址
    suggestionAddressEmail: process.env.suggestionAddressEmail || '<EMAIL>',
    // 万华GIS地图基底影像url
    whGISbaseLayerUrl:
      process.env.whGISbaseLayerUrl ||
      'https://ytmap.ims.whchem.com:28008/gisserver/DOM_Yantai/{z}/{x}/{y}.png',
    // 是否开启数据脱敏 0: 不开启, 1: 开启
    dataMask: process.env.dataMask || '0',
    whRequest: {
      // wh用户岗位推送返回接口
      userPositionPush:
        process.env.WH_USER_POSITION_PUSH ||
        'http://127.0.0.1:7001/api/testDataPermission',
      host: process.env.WH_HOST || 'http://127.0.0.1:7001',
      username: process.env.WH_USER_NAME || 'user_ohs',
      password: process.env.WH_PASSWORD || 'Ohs@2024',
      qy: process.env.WH_QY || 'https://ohs-qy.cloudqas.whchem.com',
      h5sp: process.env.WH_H5SP || 'https://ohs-h5.cloudqas.whchem.com/#/pages_user/pages/user/approvalfh',
      whWechatUrl: process.env.WH_WECHAT_URL || 'https://apigwqas.whchem.com:643/moa_api_msg',
      whWechatApiKey: process.env.WH_WECHAT_API_KEY || '7ea6dcfbdb7f43eea6a0cd6b6bc2a5c8',
    },
    // 不启用高德地图api
    useGmap: {
      enable: process.env.USE_GAMP_ENABLE || '0',
    },
    // 客服开关 0: 不显示, 1: 显示
    customerService: {
      enable: process.env.CUSTOMER_SERVICE_ENABLE || '0',
    },
    // 系统右上角的设置功能 0: 不显示, 1: 显示
    systemSetting: {
      enable: process.env.SYSTEM_SETTING_ENABLE || '1',
    },
    // 车间岗位中同一个公司的员工不同岗位是否可以重复添加 0: 不允许, 1: 允许
    isRepeatAddEmployee: process.env.isRepeatAddEmployee || '0',
    // 危害因素管理-防护设施管理-模板下载
    templatePath: process.env.templatePath || '职业病防护设施_wh.xlsx',
  };
};
