/**
 * 运营端用户对象
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const CryptoJS = require('crypto-js');

  require('./adminGroup');

  const OperateUserSchema = new Schema({
    id: String,
    _id: {
      type: String,
      default: shortid.generate,
    },
    name: String,
    userName: String,
    password: {
      type: String,
      set(val) {
        // return CryptoJS.AES.encrypt(val, app.config.encrypt_key).toString();
        return CryptoJS.SHA256(val + app.config.salt_sha2_key).toString();
      },
    },
    email: String,
    cname: String, // 单位名称
    regAdd: Array, // 注册地址
    area_code: {
      type: String,
    }, // 所在辖区区号
    phoneNum: String,
    countryCode: {
      type: String,
    }, // 手机号前国家代码
    comments: String,
    date: {
      type: Date,
      default: Date.now,
    },
    logo: {
      type: String,
      default: '/static/upload/images/defaultlogo.png',
    },
    enable: {
      type: Boolean,
      default: false,
    },
    state: {
      type: String,
      default: '1', // 1正常，0删除
    },
    group: {
      type: String,
      ref: 'AdminGroup',
    },
    targetEditor: {
      type: String,
      ref: 'User',
    },
  });


  return mongoose.model('OperateUser', OperateUserSchema, 'operateusers');

};
